name: bcn_agency_banking_flutter
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+5

environment:
  sdk: '>=3.7.0 <=3.7.2'
  flutter: '>=3.29.2'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  intl: ^0.19.0
  shared_preferences: ^2.5.3
  cached_network_image: ^3.4.1
  flutter_bloc: ^9.1.0
  freezed_annotation: ^3.0.0
  pull_to_refresh_flutter3: ^2.0.2
  uuid: ^4.5.1
  geolocator: ^13.0.4
  google_maps_flutter: ^2.12.1
  google_maps_flutter_android: ^2.16.0
  geocoding: ^3.0.0
  http: ^1.3.0
  crypto: ^3.0.6
  path: ^1.9.1
  navigation_history_observer: ^1.1.1
  json_annotation: ^4.9.0
  url_launcher: ^6.3.1
  package_info_plus: ^8.3.0
  visibility_detector: ^0.4.0+2
  flutter_localizations:
    sdk: flutter

  agency_banking_rpcs:
    git:
      url: https://bcn-bot:<EMAIL>/Resolut-Tech/BCN-RPC-Dart.git
      ref: "4921273"
  leo_dart_runtime:
    git:
      url: **************:SuryaDigital/LeoDartRuntime.git
      ref: 60c3f0c
  leo_flutter_ui:
    git:
      url: **************:SuryaDigital/Leo-Flutter-UI.git
      ref: 5dc4cc4
  dedwig:
    git:
      url: **************:SuryaDigital/Dedwig.git
      ref: b2e3096
  native_flutter_proxy: ^0.2.3
  get_it: ^8.0.3
  fluttertoast: ^8.2.12
  local_auth: ^2.3.0
  flutter_native_splash: ^2.4.6
  pinput: ^5.0.1
  share_plus: ^10.1.4
  screenshot: ^3.0.0
  path_provider: ^2.1.5
  firebase_core: ^3.13.0
  firebase_crashlytics: ^4.3.5
  store_redirect: ^2.0.4
  syncfusion_flutter_pdfviewer: ^29.1.37
  syncfusion_flutter_core: ^29.1.37
  sprintf: ^7.0.0
  country_flags: ^3.2.0
  phone_numbers_parser: ^9.0.3
  flutter_svg: ^2.0.17
  flutter_oss_licenses: ^3.0.4
  flutter_cache_manager: ^3.4.1
  async: ^2.12.0
  device_info_plus: ^11.3.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.4.15
  flutter_lints: ^5.0.0
  freezed: ^3.0.6
  json_serializable: ^6.9.4

flutter:
  uses-material-design: true
  generate: true


  assets:
    - assets/light/
    - assets/light/2.0x/
    - assets/light/3.0x/
    - assets/dark/
    - assets/dark/2.0x/
    - assets/dark/3.0x/
    - assets/ab_icons/
    - assets/images/
    - assets/maps/

  fonts:
    - family: PTSans
      fonts:
        - asset: assets/fonts/PTSans/PTSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/PTSans/PTSans-Regular.ttf
          weight: 400

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
