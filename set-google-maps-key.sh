set -eu

GOOGLE_MAPS_KEY=$1

# Set the key for Android
ANDROID_KEY_FILE_PATH=android/googlemaps.properties
touch $ANDROID_KEY_FILE_PATH && echo "api_key=\"$GOOGLE_MAPS_KEY\"" > $ANDROID_KEY_FILE_PATH

# Set the key for Flutter
FLUTTER_KEY_FILE_PATH=lib/src/core/app_config.dart
touch $FLUTTER_KEY_FILE_PATH && echo "const String googleMapsApiKey = \"$GOOGLE_MAPS_KEY\";" > $FLUTTER_KEY_FILE_PATH

# Set the key for iOS
IOS_KEY_FILE_PATH=ios/Runner/GoogleMapsKey.swift
touch $IOS_KEY_FILE_PATH && echo "import Foundation\n\nlet googleMapsAPIKey = \"$GOOGLE_MAPS_KEY\"" > $IOS_KEY_FILE_PATH
