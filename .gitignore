# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/android/build

# Generated files
*.freezed.dart
untranslated-messages.json
launch.json
*.g.dart
/lib/src/core/third_party_software_licenses.dart

# Signing Config
android/signingConfig.properties

# Google Maps Key Files
lib/src/core/app_config.dart
ios/Runner/GoogleMapsKey.swift
android/googlemaps.properties

# Generarated localization files
lib/i18n/generated/**

# Generated upon starting Flutter dev tools
devtools_options.yaml
