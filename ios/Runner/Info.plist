<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>${APP_NAME}</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>yafika_agency</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>itms-beta</string>
		<string>itms</string>
		<string>mailto</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Access to camera is required to register as an agent on Yafika and carry out transactions from there.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Biometric authentication is needed to secure the app during usage.</string>
	<key>NSLocationUsageDescription</key>
	<string>Access to your location is used to set you up as an agent or validate an agent's application and carry out transactions from there.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Access to your location is used to set you up as an agent or validate an agent's application and carry out transactions from there.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
    <string>Access to your location is used to set you up as an agent or validate an agent's application and carry out transactions from there.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Access to photo library is required to register as an agent on Yafika and carry out transactions from there.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Access to photo library is required to register as an agent on Yafika and carry out transactions from there.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
    <false/>
</dict>
</plist>
