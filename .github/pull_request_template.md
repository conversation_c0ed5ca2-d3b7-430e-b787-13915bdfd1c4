# Checklist

## PR Description (One of the Following Must be Checked)

- [ ] What is the problem and how did you solve it?
- [ ] Link to Jira ticket.

## Please make sure you have gone through this checklist before asking for review.

- [ ] Made UI changes in this PR (if yes)
    - [ ] Tested on Pixel 3
    - [ ] Tested on Nexus S
    - [ ] Tested on Pixel 6 pro
    - [ ] Tested on iPhone 13


- [ ] Added new `string` resources (if yes)
  Check if text is changing on changing locale of the app - `EN` to `NY` & vice versa
    - [ ] Tested for NY (Nyanja)
    - [ ] Tested for EN (English)

- [ ] Added new widgets (if yes)
    - [ ] Check to see with different text length. If there's no overflow occurring.
    - [ ] Check responsiveness as per different screen length.

- [ ] Check all screens related to the work flow, you are working on. If screen you made edits to
  impact other workflows, check those workflows as well
- [ ] Correct Typos
- [ ] Run code analysis
- [ ] Apply formatting (cmd + options + L or ctrl + alt + L for Android studio) (shift + alt + f or
  shift + option + f for VSCode)
- [ ] Add the demo gif/video/screenshot if there is any UI changes
- [ ] Review your PR yourself after a gap of 15 minutes, allowing for a fresh perspective
