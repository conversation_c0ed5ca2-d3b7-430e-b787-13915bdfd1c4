name: Release-Prod-Android

on:
  push:
    branches:
      - prod

jobs:
  Release:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 21.0.6

      - uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version: 3.29.2
      - name: Setup Flutter SSH Keys.
        uses: webfactory/ssh-agent@v0.9.1
        with:
          ssh-private-key: |
            ${{ secrets.DEDWIG_DEPLOY_KEY_PRIVATE }}
            ${{ secrets.LEO_DART_RUNTIME_DEPLOY_KEY_PRIVATE }}
            ${{ secrets.LEO_FLUTTER_UI_DEPLOY_KEY_PRIVATE }}
      - name: Setup Signing Config
        run: |
          echo "storePassword=${{secrets.ANDROID_SIGNING_PASSWORD}}" >> android/signingConfig.properties
          echo "keyAlias=${{secrets.ANDROID_SIGNING_KEY_ALIAS}}" >> android/signingConfig.properties
          echo "keyPassword=${{secrets.ANDROID_SIGNING_PASSWORD}}" >> android/signingConfig.properties
      - name: Setup Google Maps Key
        run: |
          sh set-google-maps-key.sh ${{secrets.GOOGLE_MAPS_AB_PROD_KEY}}
      - name: Get Dependencies
        run: flutter pub get
        timeout-minutes: 1
      - name: Check Untranslated Messages
        run: ./check_untranslated_messages.sh
      - name: Generate package's licenses
        run: dart run flutter_oss_licenses:generate -o lib/src/core/third_party_software_licenses.dart
      - name: Build localization files
        run: flutter gen-l10n
      - name: Build scripts
        run: dart run build_runner build
      - name: Analyze Code
        run: flutter analyze
      - name: Test
        run: flutter test
      - name: Determine next version
        uses: gps/determine-next-version-mobile-app@master
        id: next_version
        with:
          GH_TOKEN: ${{ github.token }}
          TAG_PREFIX: prod-v
      - name: Set version code
        # Replace "0.0.1+1" in the /pubspec.yaml file with the new version at line 5
        # Thanks: https://stackoverflow.com/a/34133944
        # After that, the value is set to 0.0.1(1). We change it to 0.0.1+1
        # For that, thanks: https://unix.stackexchange.com/a/272597 and https://stackoverflow.com/a/11145362
        run: |
          sed -i -e "5s/0.0.1+1/${{steps.next_version.outputs.NEXT_BUILD_VERSION}}/g" ./pubspec.yaml &&
          sed -i -e "5s/(/+/g" ./pubspec.yaml &&
          sed -i -e "5s/)//g" ./pubspec.yaml
      - name: Build Debug APK
        run: flutter build apk --debug --dart-define=flavor=prod --flavor prod
      - name: Build Release APK
        run: flutter build apk --dart-define=flavor=prod --flavor prod
      - name: Build AppBundle
        run: flutter build appbundle --dart-define=flavor=prod --flavor prod

      - name: Rename AAB and APKs
        run: |
          mv ./build/app/outputs/bundle/prodRelease/app-prod-release.aab "./build/app/outputs/bundle/prodRelease/BCN-Agency-Banking-PROD (Release).aab"
          mv ./build/app/outputs/flutter-apk/app-prod-debug.apk "./build/app/outputs/flutter-apk/BCN-Agency-Banking-PROD (Debug).apk"
          mv ./build/app/outputs/flutter-apk/app-prod-release.apk "./build/app/outputs/flutter-apk/BCN-Agency-Banking-Prod (Release).apk"
      
      - name: Create Release and Upload Assets
        uses: softprops/action-gh-release@v2
        with:
          tag_name: prod-v${{ steps.next_version.outputs.NEXT_BUILD_VERSION }}
          name: prod-v${{ steps.next_version.outputs.NEXT_BUILD_VERSION }}
          body: Release number ${{ steps.next_version.outputs.NEXT_BUILD_VERSION }} of the Android Agency Banking app Prod.
          draft: false
          prerelease: false
          files: |
            ./build/app/outputs/bundle/prodRelease/BCN-Agency-Banking-PROD (Release).aab
            ./build/app/outputs/flutter-apk/BCN-Agency-Banking-PROD (Debug).apk
            ./build/app/outputs/flutter-apk/BCN-Agency-Banking-Prod (Release).apk
          token: ${{ github.token }}
