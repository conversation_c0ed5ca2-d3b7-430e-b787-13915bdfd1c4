name: Run analyze and tests
on: pull_request
jobs:
  flutter-analyze-and-format:
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.workflow }}-${{ github.head_ref }}
      cancel-in-progress: true
    steps:
      - name: Setup Flutter SSH Keys.
        uses: webfactory/ssh-agent@v0.9.1
        with:
          ssh-private-key: |
            ${{ secrets.DEDWIG_DEPLOY_KEY_PRIVATE }}
            ${{ secrets.LEO_DART_RUNTIME_DEPLOY_KEY_PRIVATE }}
            ${{ secrets.LEO_FLUTTER_UI_DEPLOY_KEY_PRIVATE }}
      - uses: actions/checkout@v4
        with:
          lfs: true
      - uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version: 3.29.2
      - name: Setup Google Maps Key
        run: |
          sh set-google-maps-key.sh ${{secrets.GOOGLE_MAPS_AB_DEV_KEY}}
      - name: Get Dependencies
        run: flutter pub get
        # pub get doesn't give up on trying to fetch dependencies.
        # See: https://github.com/dart-lang/pub/issues/2242
        # This bone-headed-ness is not needed from a tool that racks up
        # execution minutes and costs us money. Since there is no way to
        # set a limit of retries in the tool, we are limiting it via
        # the CI step's timeout.
        timeout-minutes: 1
      - name: Check Untranslated Messages
        run: ./check_untranslated_messages.sh
      - name: Check Formatting
        run: dart format .
      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: Format code
      - name: Generate package's licenses
        run: dart run flutter_oss_licenses:generate -o lib/src/core/third_party_software_licenses.dart
      - name: Build localization files
        run: flutter gen-l10n
      - name: Build scripts
        run: dart run build_runner build --delete-conflicting-outputs
      - name: Analyze Code
        run: flutter analyze
      - name: Test
        run: flutter test
