import 'package:bcn_agency_banking_flutter/src/core/auth/auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  setupLocator();
  const fakeSLT = "Fake SLT";
  const fakeLLT = "Fake LLT";
  const fakeUserType = UserType.agent;

  // Exposes too much of the implementation details but still worth it.
  setupKeys() {
    const sltKey = "com.resoluttech.agencybanking.SLTKey";
    const lltKey = "com.resoluttech.agencybanking.LLTKey";
    const userTypeKey = "com.resoluttech.agencybanking.UserTypeKey";
    final values = {
      sltKey: fakeSLT,
      lltKey: fakeLLT,
      userTypeKey: fakeUserType.name,
    };
    // See: https://pub.dev/packages/shared_preferences#testing
    SharedPreferences.setMockInitialValues(values);
  }

  // Thanks: https://stackoverflow.com/a/********
  setUp(() {
    setupKeys();
  });

  // These are horrible tests because
  // we are relying too much on the implementation details.
  // Ideally we should just test "set" and "get" and not worry about importing
  // shared preferences.
  // However, in attempting such a test the `set` portion of the underlying code
  // in shared preferences was crashing somewhere internally in the dependency's
  // code and hence this test.
  // was not working, The suspicion is that
  // this has something to do with the test environment.
  // When tested manually in the app, the code works as it stands right now.
  // However, testing instructions for Shared Preferences mentioned that tests
  // are not done in a vanilla way you would expect
  // (exposing implementation details in the process.)
  // All this being said, it's still useful to at least see if the values can be
  // retreived.
  test('SLT Is Retrieved', () async {
    final retrievedSLT = await RPCAuthProvider.instance.getSLT();
    expect(retrievedSLT, equals(fakeSLT));
  });
  test('User Type Is Retrieved', () async {
    final userType = await AuthProvider.instance.getSignedInUserType();
    expect(userType, equals(fakeUserType));
  });
}
