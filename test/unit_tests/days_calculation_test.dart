import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final DateTime today = DateTime(2023, 03, 07);
  group("DateTime Operations Test", () {
    test("Date returned is tomorrow", () {
      final DateTime tomorrow = DateTime(2023, 03, 08);
      final daysLeft = differenceInDays(tomorrow, today);
      expect(daysLeft, 1);
    });

    test("Date returned is today", () {
      final DateTime todayCopy = DateTime(2023, 03, 07);
      final daysLeft = differenceInDays(todayCopy, today);
      expect(daysLeft, 0);
    });

    test("Date returned is a past date", () {
      final DateTime pastDate = DateTime(2023, 02, 28);
      //Thanks: https://stackoverflow.com/a/********
      expect(
        () => differenceInDays(pastDate, today),
        throwsA(isA<DeveloperError>()),
      );
    });

    test("Date is in next month", () {
      final DateTime nextMonthDate = DateTime(2023, 04, 07);
      final daysLeft = differenceInDays(nextMonthDate, today);
      expect(daysLeft, 31);
    });

    test("Date is in next month of leap year", () {
      final DateTime nextMonthDate = DateTime(2020, 03, 01);
      final DateTime todayButLeapYear = DateTime(2020, 02, 01);
      final daysLeft = differenceInDays(nextMonthDate, todayButLeapYear);
      expect(daysLeft, 29);
    });
  });
}
