import 'dart:math';

import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_text_input_formatter.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';

void main() {
  setupLocator();
  group("Amount Formatter Test", () {
    test("Format amount from RPC to raw integer amount", () {
      final randomAmount = Random().nextInt(100000);
      final rpcAmount =
          randomAmount * AmountFormatter.amountMultiplicationFactor;
      final rawIntegerAmount = AmountFormatter.getActualAmount(rpcAmount);
      expect(rawIntegerAmount, randomAmount);
    });

    test("Format number as per device locale", () {
      const number = 1126;
      final NumberFormat numberFormatter =
          AmountFormatter.amountIntlFormatter();
      final String formattedNumber = numberFormatter.format(number);
      expect(formattedNumber, "1,126");
    });

    test(
      "Format amount of int data type from RPC to Locale formatted amount",
      () {
        const int randomAmount = 26301;
        const int rpcAmount =
            randomAmount * AmountFormatter.amountMultiplicationFactor;
        const String currencySymbol = "MWK";
        final localeFormattedAmount =
            AmountFormatter.getLocaleFormattedAmountFromRPCAmount(
              rpcAmount,
              currencySymbol,
            );
        expect(localeFormattedAmount, "$currencySymbol 26,301");
      },
    );

    test(
      "Format amount of double data type from RPC to Locale formatted amount",
      () {
        const double randomAmount = 26.301;
        final int rpcAmount =
            (randomAmount * AmountFormatter.amountMultiplicationFactor).toInt();
        const String currencySymbol = "MWK";
        final localeFormattedAmount =
            AmountFormatter.getLocaleFormattedAmountFromRPCAmount(
              rpcAmount,
              currencySymbol,
            );
        expect(localeFormattedAmount, "$currencySymbol 26.30");
      },
    );

    test("Convert Formatted amount to Raw amount", () {
      const String formattedAmount = "12,314";
      final int rawAmount = AmountTextInputFormatter.parseEnteredAmount(
        formattedAmount,
      );
      expect(rawAmount, 12314);
    });
  });
}
