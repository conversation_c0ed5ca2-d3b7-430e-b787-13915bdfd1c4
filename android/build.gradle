allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

def newRootBuildDir = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newRootBuildDir)

subprojects {
    def newSubprojectBuildDir = newRootBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}

subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete(rootProject.layout.buildDirectory)
}
