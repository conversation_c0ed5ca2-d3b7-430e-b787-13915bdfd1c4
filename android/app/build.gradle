plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

// Load the Google Maps key from the file googlemaps.properties.
def googleMapApiKey = new Properties()
googleMapApiKey.load(new FileInputStream(rootProject.file("googlemaps.properties")))

android {
    compileSdkVersion flutter.compileSdkVersion
    buildFeatures {
        buildConfig = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_21
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    flavorDimensions "yafika_agency"

    productFlavors {
        dev {
            dimension "yafika_agency"
            applicationIdSuffix ".dev"
            resValue "string", "app_name", "Yafika Agency Banking - DEV"
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key"]
        }
        uat {
            dimension "yafika_agency"
            applicationIdSuffix ".uat"
            resValue "string", "app_name", "Yafika Agency Banking - UAT"
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key"]
        }
        prod {
            dimension "yafika_agency"
            resValue "string", "app_name", "Yafika Agency Banking"
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key"]
        }
    }

    signingConfigs {
        release
    }

    defaultConfig {
        applicationId "com.resoluttech.bcn.agency"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.
        minSdkVersion 23
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
    namespace 'com.resoluttech.bcn.agency'
}

def signingConfigPropertiesFile = rootProject.file("signingConfig.properties")
if (signingConfigPropertiesFile.exists()) {
    def signingConfigProperties = new Properties()
    signingConfigProperties.load(new FileInputStream(signingConfigPropertiesFile))
    // Setting up release signing configs
    if (
            signingConfigProperties != null &&
                    signingConfigProperties.containsKey("storePassword") &&
                    signingConfigProperties.containsKey("keyAlias") &&
                    signingConfigProperties.containsKey("keyPassword")
    ) {
        android.signingConfigs.release.storeFile file('android.keystore')
        android.signingConfigs.release.storePassword signingConfigProperties["storePassword"]
        android.signingConfigs.release.keyAlias signingConfigProperties["keyAlias"]
        android.signingConfigs.release.keyPassword signingConfigProperties["keyPassword"]
    } else {
        logger.debug('Release signing configs are not found')
        android.buildTypes.release.signingConfig = null
    }
} else {
    logger.debug('signingConfig.properties file not found')
}

flutter {
    source '../..'
}
