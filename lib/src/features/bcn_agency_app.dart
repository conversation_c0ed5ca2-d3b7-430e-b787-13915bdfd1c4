import 'package:bcn_agency_banking_flutter/src/core/auth/app_pin_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_manager_home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/session_pin/enter_session_pin/enter_session_pin_screen.dart';
import 'package:bcn_agency_banking_flutter/src/resources/agency_banking_theme.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bcn_agency_banking_flutter/i18n/generated/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../core/auth/auth_provider.dart';
import '../core/locale_service/locale_fallbacks.dart';
import '../core/logger.dart';
import '../core/route_generator.dart';
import '../helpers/helpers.dart';

class BCNAgencyApp extends StatefulWidget {
  final UserType? currentUserType;

  const BCNAgencyApp({super.key, required this.currentUserType});

  @override
  State<BCNAgencyApp> createState() => BCNAgencyAppState();
}

class BCNAgencyAppState extends State<BCNAgencyApp>
    with WidgetsBindingObserver {
  DateTime? _lastInActiveTime;
  AppLifecycleState _previousAppLifeCycleState = AppLifecycleState.resumed;
  static const _secondsOfInActivityAllowed = 20;
  bool _isAppPinAlreadyShown = false;

  Locale _currentLocale = BCNLocale.englishFlutterLocale;

  set locale(Locale newLocale) => setState(() {
    _currentLocale = newLocale;
  });
  late final LocationBloc _locationBloc = BlocProvider.of<LocationBloc>(
    context,
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeTheApp();
    _getCurrentLocale();
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: NavigationService.navigatorKey,
      debugShowCheckedModeBanner: false,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        FallbackMaterialLocalisationsDelegate(),
        FallbackCupertinoLocalisationsDelegate(),
      ],
      onGenerateTitle: (context) => context.localizations.appTitle,
      locale: _currentLocale,
      supportedLocales: BCNLocale.supportedLocales,
      theme: AgencyBankingTheme.lightTheme,
      darkTheme: AgencyBankingTheme.darkTheme,
      themeMode: ThemeMode.system,
      // This route is added separately here as it is the initial route and
      // we want to pass arguments to it.
      // For non-initial routes, onGenerateRoute will be used.
      routes: {
        EnterSessionPinScreen.launchId: (context) {
          _isAppPinAlreadyShown = true;
          return EnterSessionPinScreen(
            isCancellable: false,
            onSuccessfulAuthentication:
                () => _onSuccessfulAuthentication(context),
            // If the user exhausts the app pin attempts they are
            // force signed out from the application. In this case the
            // `onSuccessfulAuthentication` callback function is not executed.
            // Thus the `_isAppPinAlreadyShown` is set to false with the
            // `onForceSignOut` callback function.
            onSignOut: () => _isAppPinAlreadyShown = false,
          );
        },
      },
      onGenerateRoute: Routes.generateRoute,
      builder: (BuildContext context, Widget? child) {
        setStatusBarColorToTransparent(context);
        final MediaQueryData data = context.mq;
        return MediaQuery(
          data: data.copyWith(textScaler: const TextScaler.linear(1.0)),
          child: child!,
        );
      },
      initialRoute: Routes.initialRoute,
      navigatorObservers: [NavigationHistoryObserver()],
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed &&
        _previousAppLifeCycleState != AppLifecycleState.resumed) {
      _locationBloc.add(const LocationEvent.checkAndSetLocation());
    }
    if (await AppPinProvider.isAppPinSetup()) {
      await _evaluateAppLifecycle(state);
    }
    super.didChangeAppLifecycleState(state);
  }

  void _onSuccessfulAuthentication(BuildContext context) {
    _isAppPinAlreadyShown = false;
    l.v("User Successfully authenticated");
    switch (widget.currentUserType) {
      case UserType.agent:
        context.navigator.popAndPushNamed(HomeScreen.id);
        return;

      case UserType.agentManager:
        context.navigator.popAndPushNamed(AgentManagerHomeScreen.id);
        return;

      case null:
        l.e("There is no user in the app");
        // This case should never be triggered since we are accessing the
        // app pin screen on launch only if there is a user signed into the app.
        // The type of signed in user can only be either Agent or Agent Manager.
        throw DeveloperError('No user found');
    }
  }

  Future<void> _evaluateAppLifecycle(AppLifecycleState state) async {
    final now = DateTime.now();
    switch (state) {
      case AppLifecycleState.resumed:
        l.d("App Resumed");
        if (_lastInActiveTime != null) {
          final wasInActiveForLongTime =
              now.difference(_lastInActiveTime!).inSeconds >=
              _secondsOfInActivityAllowed;
          if (wasInActiveForLongTime) {
            if (_isAppPinAlreadyShown) return;
            _isAppPinAlreadyShown = true;
            await _authenticateUserUsingSessionPin();
            _isAppPinAlreadyShown = false;
          }
        }
        _previousAppLifeCycleState = state;
        // The `_lastInActiveTime` is reset as the app is now in foreground
        // and the user is authenticated.
        _lastInActiveTime = null;
        break;
      case AppLifecycleState.inactive:
        l.d("App Got into inactive state");
        _onLifecycleUpdated(state);
        break;
      case AppLifecycleState.paused:
        l.d("App Paused");
        _onLifecycleUpdated(state);
        break;
      case AppLifecycleState.detached:
        l.d("App Detached");
        _onLifecycleUpdated(state);
        break;
      case AppLifecycleState.hidden:
        l.d("App Hidden");
        _onLifecycleUpdated(state);
        break;
    }
  }

  void _onLifecycleUpdated(AppLifecycleState state) {
    final now = DateTime.now();
    l.d("On Lifecycle update. State: $state, now: $now");
    // On iOS, when the app in put to foreground from the background mode, the
    // app lifecycle changes to inactive from paused before changing to resumed.
    // The below condition makes sure that the `_lastInActiveTime` is updated
    // only when the previous lifecycle state is resumed, thus preventing
    // `_lastInActiveTime` from updating when the app is in inactive state when
    // transitioning from background mode to foreground mode on iOS.
    if (_previousAppLifeCycleState == AppLifecycleState.resumed) {
      _lastInActiveTime = now;
      l.d("Last Inactive time: $_lastInActiveTime");
      _previousAppLifeCycleState = state;
    }
  }

  Future<void> _authenticateUserUsingSessionPin() async {
    l.d("Authenticate user using Session PIN");
    final BuildContext currentContext =
        NavigationService.navigatorKey.currentContext!;
    await currentContext.navigator.pushNamed(
      EnterSessionPinScreen.id,
      arguments: EnterSessionPinScreenArguments(
        isCancellable: false,
        // If the user exhausts the app pin attempts they are
        // force signed out from the application. In this case the
        // Session Pin Screen is not popped.
        // Thus the `_isAppPinAlreadyShown` is set to false with the
        // `onForceSignOut` callback function.
        onSignOut: () => _isAppPinAlreadyShown = false,
      ),
    );
    l.d("Authentication complete");
  }

  void _initializeTheApp() async {
    if (context.mounted) _startLocationStream(context);
  }

  void _startLocationStream(BuildContext context) {
    final LocationBloc locationBloc = BlocProvider.of<LocationBloc>(context);
    locationBloc.add(const LocationEvent.startLocationStream());
  }

  void _getCurrentLocale() async {
    final currentLocale = await BCNLocale.fetchCurrentLocale();
    setState(() {
      _currentLocale = currentLocale;
    });
  }
}
