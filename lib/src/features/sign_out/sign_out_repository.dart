import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class SignOutRepository {
  final String llt;

  SignOutRepository({required this.llt});

  Future<LeoRPCResult<AgencySignOutUserResponse, Never>> signOutUser() async {
    final AgencySignOutUserRPC signOutUserRPCImpl = AgencySignOutUserRPCImpl(
      client: apiClient,
      authenticationProvider: RPCAuthProvider.instance,
    );
    final AgencySignOutUserRequest signOutUserRequest =
        AgencySignOutUserRequest(llt: llt);
    final LeoRPCResult<AgencySignOutUserResponse, Never> signOutUserResponse =
        await signOutUserRPCImpl.execute(signOutUserRequest);
    return signOutUserResponse;
  }
}
