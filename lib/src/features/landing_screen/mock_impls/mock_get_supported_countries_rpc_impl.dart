import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetSupportedCountriesRPCImpl extends GetSupportedCountriesRPC {
  @override
  Future<LeoRPCResult<GetSupportedCountriesResponse, Never>> execute(
    GetSupportedCountriesRequest request,
  ) {
    final LeoRPCResult<GetSupportedCountriesResponse, Never> response =
        LeoRPCResult<GetSupportedCountriesResponse, Never>.response(
          GetSupportedCountriesResponse(
            countries: [
              Country(
                displayName: LocalizedText(en: "India"),
                code: CountryCode(code: "IN"),
                phoneCode: "+91",
              ),
              Country(
                displayName: LocalizedText(en: "Malawi"),
                code: CountryCode(code: "MW"),
                phoneCode: "+265",
              ),
            ],
          ),
        );

    return Future.delayed(2.seconds, () => response);
  }
}
