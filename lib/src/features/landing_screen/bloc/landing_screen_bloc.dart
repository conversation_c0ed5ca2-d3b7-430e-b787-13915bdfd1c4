import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/landing_screen/repository/landing_screen_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/no_internet_snackbar_with_retry.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/supported_countries.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../core/logger.dart';

part 'landing_screen_bloc.freezed.dart';

class LandingScreenBloc extends Bloc<LandingScreenEvent, LandingScreenState>
    with R<PERSON><PERSON><PERSON><PERSON> {
  final LandingScreenRepository _repository = LandingScreenRepository();

  LandingScreenBloc() : super(const Initial()) {
    on<LandingScreenEvent>((event, emit) async {
      switch (event) {
        case GetSupportedCountries(:final context):
          l.d("Get Supported Countries.");
          emit(const LandingScreenState.loading());
          await rpcHandler(
            () async {
              final rpcResult = await _repository.getCountriesList();
              l.d("Get Supported Countries result: $rpcResult");
              await rpcResult.when(
                response: (response) async {
                  await CachedSupportedCountries.setSupportedCountries(
                    response.countries,
                  );
                  emit(const LandingScreenState.onData());
                },
                error: (error) {
                  // This should never happen since this RPC call does not
                  // throw any errors.
                  // Therefore, seeing this exception is a developer error.
                  l.e("RPC error came while getting supported countries");
                  throw DeveloperError("Something went wrong: $error");
                },
              );
            },
            onServerError: () async {
              emit(const LandingScreenState.serverError());
              await AgencyAppDialog.showErrorDialog(
                context: context,
                contentText: context.localizations.somethingWentWrong,
              );
              // If the Bloc is closed, don't call the RPC.
              if (isClosed) return;
              // If server error exists, retry the RPC call.
              if (context.mounted) {
                add(LandingScreenEvent.getSupportedCountries(context));
              }
            },
            onTransientError: (_) {
              l.d("Emitting transient error");
              emit(const LandingScreenState.transientError());
              showNoInternetSnackBarWithRetry(
                NavigationService.navigatorKey.currentContext!,
                () {
                  add(LandingScreenEvent.getSupportedCountries(context));
                },
              );
            },
            shouldHandleTransientError: false,
          );
      }
    });
  }
}

@freezed
sealed class LandingScreenState with _$LandingScreenState {
  const factory LandingScreenState.initial() = Initial;

  const factory LandingScreenState.loading() = Loading;

  const factory LandingScreenState.onData() = OnData;

  const factory LandingScreenState.transientError() = TransientError;

  const factory LandingScreenState.serverError() = ServerError;
}

@freezed
sealed class LandingScreenEvent with _$LandingScreenEvent {
  const factory LandingScreenEvent.getSupportedCountries(BuildContext context) =
      GetSupportedCountries;
}
