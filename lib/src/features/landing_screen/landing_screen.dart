import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/bcn_logo.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

import '../../resources/ab_assets.dart';
import '../../utils/constants.dart';
import '../../widgets/spinner.dart';
import '../agent/agent_sign_in/agent_phone_number_screen.dart';
import '../agent_manager/sign_in/agent_manager_phone_number_password_screen.dart';
import 'bloc/landing_screen_bloc.dart';
import 'widgets/user_type_button.dart';

class LandingScreen extends StatefulWidget {
  static const id = "/landing-screen";

  const LandingScreen({Key? key}) : super(key: key);

  @override
  State<LandingScreen> createState() => _LandingScreenState();
}

class _LandingScreenState extends State<LandingScreen> {
  @override
  Widget build(BuildContext context) {
    // Setting status bar to the primary color
    // as per current theme.
    final currentStyle = (context.isDarkMode
            ? SystemUiOverlayStyle.dark
            : SystemUiOverlayStyle.light)
        .copyWith(
          statusBarColor: context.appColors.primaryDefaultColor,
          statusBarBrightness:
              context.isDarkMode ? Brightness.dark : Brightness.light,
        );
    return Scaffold(
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: currentStyle,
        child: Stack(
          children: [
            Column(
              children: [
                SizedBox(height: context.mq.padding.top),
                verticalGapTwentyFour,
                const Center(child: BCNLogo()),
                const Spacer(flex: 2),
                Flexible(
                  child: BlocBuilder<LandingScreenBloc, LandingScreenState>(
                    builder: (context, state) {
                      if (state is Loading) return const Spinner();
                      if (state is TransientError || state is ServerError) {
                        return const SizedBox();
                      }
                      return Padding(
                        padding: verticalPaddingEight,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Flexible(
                              child: UserTypeButton(
                                buttonText:
                                    context.localizations.enterAsAgentManager,
                                iconPath: ABAssets.usersIcon,
                                onTap: () {
                                  // When we push from `LandingScreen`,
                                  // the status bar color should change
                                  // to transparent.
                                  setStatusBarColorToTransparent();
                                  context.navigator.pushNamed(
                                    AgentManagerPhoneNumberPasswordScreen.id,
                                  );
                                },
                              ),
                            ),
                            Flexible(
                              child: UserTypeButton(
                                buttonText: context.localizations.enterAsAgent,
                                iconPath: ABAssets.userCircleIcon,
                                onTap: () {
                                  // When we push from `LandingScreen`,
                                  // the status bar color should change
                                  // to transparent.
                                  setStatusBarColorToTransparent();
                                  context.navigator.pushNamed(
                                    AgentPhoneNumberScreen.id,
                                  );
                                },
                              ),
                            ),
                            SizedBox(height: context.mq.padding.bottom),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    context.isDarkMode
                        ? ABAssets.landingScreenImageDark
                        : ABAssets.landingScreenImageLight,
                  ),
                  verticalGapSixteen,
                  Text(
                    context.localizations.welcomeHeading,
                    style: context.appTextStyles.titleBold.copyWith(
                      color: context.appColors.titleColor,
                    ),
                  ),
                  verticalGapFour,
                  Padding(
                    padding: horizontalPaddingSixteen,
                    child: Text(
                      context.localizations.welcomeMessage,
                      textAlign: TextAlign.center,
                      style: context.appTextStyles.smallText1.copyWith(
                        color: context.appColors.neutralShadeDefaultColor,
                      ),
                      maxLines: landingScreenSubtitleMaxLines,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    setStatusBarColorToTransparent();
    super.dispose();
  }
}
