import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/landing_screen/mock_impls/mock_get_supported_countries_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class LandingScreenRepository {
  Future<LeoRPCResult<GetSupportedCountriesResponse, Never>>
  getCountriesList() async {
    final GetSupportedCountriesRPC getCountriesListRPCImpl =
        currentFlavor.isMock
            ? MockGetSupportedCountriesRPCImpl()
            : GetSupportedCountriesRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final GetSupportedCountriesRequest getSupportedCountriesRequest =
        GetSupportedCountriesRequest();
    final LeoRPCResult<GetSupportedCountriesResponse, Never>
    getGetSupportedCountriesResponse = await getCountriesListRPCImpl.execute(
      getSupportedCountriesRequest,
    );
    return getGetSupportedCountriesResponse;
  }
}
