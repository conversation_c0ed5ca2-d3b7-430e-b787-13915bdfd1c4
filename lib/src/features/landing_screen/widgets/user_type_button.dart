import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/list_tile_avatar_content.dart';
import 'package:flutter/material.dart';

class UserTypeButton extends StatelessWidget {
  final String buttonText;
  final Color? buttonTextColor;
  final String iconPath;
  final VoidCallback? onTap;
  final bool isEnabled;
  final Widget? trailingIcon;

  const UserTypeButton({
    super.key,
    required this.buttonText,
    required this.iconPath,
    required this.onTap,
    this.isEnabled = true,
    this.trailingIcon,
    this.buttonTextColor,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      child: InkWell(
        onTap: onTap,
        child: Ink(
          height: dimenSeventyTwo,
          width: double.maxFinite,
          color: context.appColors.backgroundColor,
          padding: horizontalPaddingSixteen,
          child: ListTileAvatarContent(
            isEnabled: isEnabled,
            title: buttonText,
            iconPath: iconPath,
            trailingIcon: trailingIcon,
            titleColor: buttonTextColor,
          ),
        ),
      ),
    );
  }
}
