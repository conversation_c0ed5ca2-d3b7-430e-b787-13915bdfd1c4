import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../resources/ab_assets.dart';
import '../../../resources/dimensions.dart';
import '../../../widgets/bcn_logo.dart';
import '../../../widgets/primary_button.dart';

class SetupSessionPinIntroPage extends StatelessWidget {
  final VoidCallback onContinue;

  const SetupSessionPinIntroPage({super.key, required this.onContinue});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Padding(
            padding: commonScreenPadding.copyWith(
              top: dimenZero,
              bottom: dimenZero,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  context.isDarkMode
                      ? ABAssets.setupSessionPinScreenImageDark
                      : ABAssets.setupSessionPinScreenImageLight,
                ),
                verticalGapSixteen,
                Text(
                  context.localizations.setupSessionPin,
                  style: context.appTextStyles.titleBold.copyWith(
                    color: context.appColors.titleColor,
                  ),
                ),
                verticalGapEight,
                Text(
                  context.localizations.setupSessionPinSubtitle,
                  style: context.appTextStyles.smallText1.copyWith(
                    color: context.appColors.neutralShadeDefaultColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                verticalGapTwentyFour,
                const Center(child: BCNLogo()),
                const Spacer(),
                Padding(
                  padding: commonScreenPadding,
                  child: PrimaryButton(
                    labelText: context.localizations.continueText,
                    onPressed: onContinue,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
