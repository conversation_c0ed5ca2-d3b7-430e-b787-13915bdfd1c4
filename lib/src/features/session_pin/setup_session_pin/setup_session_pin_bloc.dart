import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../core/auth/app_pin_provider.dart';
import '../../../core/logger.dart';
import '../../../utils/constants.dart';

part 'setup_session_pin_bloc.freezed.dart';

class SetupSessionPinBloc
    extends Bloc<SetupSessionPinEvent, SetupSessionPinState> {
  String? sessionPin;
  final PageController pageController = PageController();
  bool bypassSessionPinByBiometricsSwitchValue = false;

  SetupSessionPinBloc() : super(const Initial()) {
    on<SetupSessionPinEvent>((event, emit) async {
      switch (event) {
        case GetInitialBiometricsValue():
          await _setSwitchValue(emit);
        case AddPin(:final pin):
          l.d("Adding initial PIN");
          sessionPin = pin;

          // Go to confirm session pin.
          goToNextPage();
          add(const SetupSessionPinEvent.getInitialBiometricsValue());
        case SetBypassSessionPinUsingBiometrics(:final shouldBypass):
          if (shouldBypass) {
            final bool canCheckBiometrics =
                await AppPinProvider.canDeviceCheckBiometrics();
            final bool isBiometricEnrolled =
                await AppPinProvider.isBiometricEnrolled();
            bypassSessionPinByBiometricsSwitchValue =
                canCheckBiometrics && isBiometricEnrolled;
            if (bypassSessionPinByBiometricsSwitchValue == false) {
              emit(const SetupSessionPinState.biometricsUnavailable());
              emit(const SetupSessionPinState.disableBypassSwitch());
            } else {
              emit(const SetupSessionPinState.enableBypassSwitch());
            }
          } else {
            bypassSessionPinByBiometricsSwitchValue = false;
            emit(const SetupSessionPinState.disableBypassSwitch());
          }
        case ConfirmPin(:final context, :final confirmPin):
          l.d("Confirm PIN");
          if (confirmPin == sessionPin) {
            emit(const SetupSessionPinState.confirmPinAndSessionPinMatches());
            AgencyAppDialog.showSpinnerDialog(context);
            await AppPinProvider.setAppPin(confirmPin);
            await AppPinProvider.setRemainingAppPinAttempts(
              maximumIncorrectAttempts,
            );
            if (context.mounted) {
              // Pop the spinner dialog.
              context.rootNavigator.pop();

              await AppPinProvider.setBiometricsAuthenticationPreference(
                isEnabled: bypassSessionPinByBiometricsSwitchValue,
                localisedReason: context.localizations.identificationIsRequired,
                onLockedOut: () async {
                  await _showBiometricsVerificationFailedError(context);
                  if (context.mounted) _popWithResult(context);
                },
                onAuthenticationCancelled: () async {
                  await _showBiometricsVerificationFailedError(context);
                  if (context.mounted) _popWithResult(context);
                },
                onSuccess: () => _popWithResult(context),
              );
            }
          } else {
            emit(const SetupSessionPinState.confirmPinAndSessionPinNotMatch());
          }
      }
    });
  }

  Future<void> _showBiometricsVerificationFailedError(
    BuildContext context,
  ) async {
    await AppPinProvider.resetBiometricsPreferences();
    if (context.mounted) {
      await AgencyAppDialog.showErrorDialog(
        context: context,
        contentText: context.localizations.biometricsVerificationFailedError,
      );
    }
  }

  void goToNextPage() => pageController.jumpToNextPage;

  void goToPreviousPage() => pageController.jumpToPreviousPage;

  void _popWithResult(BuildContext context) {
    context.rootNavigator.pop(true);
  }

  Future<void> _setSwitchValue(Emitter<SetupSessionPinState> emit) async {
    l.v("Set Switch value.");
    final bool canCheckBiometrics =
        await AppPinProvider.canDeviceCheckBiometrics();
    final bool isBiometricEnrolled = await AppPinProvider.isBiometricEnrolled();
    bypassSessionPinByBiometricsSwitchValue =
        canCheckBiometrics && isBiometricEnrolled;
    l.d('''Can Bypass session PIN by biometrics.
    isBiometricEnrolled: $isBiometricEnrolled
    canCheckBiometrics: $canCheckBiometrics''');
    emit(
      SetupSessionPinState.setBypassSessionPinByBiometrics(
        doesDeviceHasBiometricsHardware: canCheckBiometrics,
        doesUserEnrolledBiometrics: isBiometricEnrolled,
      ),
    );
  }
}

@freezed
sealed class SetupSessionPinState with _$SetupSessionPinState {
  const factory SetupSessionPinState.initial() = Initial;

  const factory SetupSessionPinState.setBypassSessionPinByBiometrics({
    required bool doesDeviceHasBiometricsHardware,
    required bool doesUserEnrolledBiometrics,
  }) = SetBiometricsValue;

  const factory SetupSessionPinState.biometricsUnavailable() =
      BiometricsUnavailable;

  const factory SetupSessionPinState.enableBypassSwitch() = EnableBypassSwitch;

  const factory SetupSessionPinState.disableBypassSwitch() =
      DisableBypassSwitch;

  const factory SetupSessionPinState.confirmPinAndSessionPinNotMatch() =
      ConfirmPinAndSessionPinNotMatch;

  const factory SetupSessionPinState.confirmPinAndSessionPinMatches() =
      ConfirmPinAndSessionPinMatches;
}

@freezed
sealed class SetupSessionPinEvent with _$SetupSessionPinEvent {
  const factory SetupSessionPinEvent.getInitialBiometricsValue() =
      GetInitialBiometricsValue;

  const factory SetupSessionPinEvent.addPin(String pin) = AddPin;

  const factory SetupSessionPinEvent.setBypassSessionPinUsingBiometrics(
    bool shouldBypass,
  ) = SetBypassSessionPinUsingBiometrics;

  const factory SetupSessionPinEvent.confirmPin(
    BuildContext context,
    String confirmPin,
  ) = ConfirmPin;
}
