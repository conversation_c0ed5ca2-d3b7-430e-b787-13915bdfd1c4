import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/bcn_logo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../widgets/agency_auth_pin_field.dart';
import 'setup_session_pin_bloc.dart';

class SetupSessionPinEntryPage extends StatefulWidget {
  const SetupSessionPinEntryPage({Key? key}) : super(key: key);

  @override
  State<SetupSessionPinEntryPage> createState() =>
      _SetupSessionPinEntryPageState();
}

class _SetupSessionPinEntryPageState extends State<SetupSessionPinEntryPage> {
  late final _setupSessionPinBloc = BlocProvider.of<SetupSessionPinBloc>(
    context,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              verticalGapEighty,
              const Center(child: BCNLogo()),
              verticalGapTwentyFour,
              Text(
                context.localizations.enterSessionPin,
                style: context.appTextStyles.titleBold.copyWith(
                  color: context.appColors.neutralShade1Color,
                ),
              ),
              verticalGapTwentyFour,
              AgencyAuthPinField(
                onPinComplete: (pin, clear) {
                  _setupSessionPinBloc.add(SetupSessionPinEvent.addPin(pin));
                  clear();
                },
              ),
              verticalGapTwentyFour,
            ],
          ),
        ),
      ),
    );
  }
}
