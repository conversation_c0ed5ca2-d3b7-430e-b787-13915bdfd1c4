import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../resources/dimensions.dart';
import '../../../widgets/agency_auth_pin_field.dart';
import '../../../widgets/bcn_logo.dart';
import '../../../widgets/primary_text_button.dart';
import 'setup_session_pin_bloc.dart';

class ConfirmSessionPinEntryPage extends StatefulWidget {
  const ConfirmSessionPinEntryPage({Key? key}) : super(key: key);

  @override
  State<ConfirmSessionPinEntryPage> createState() =>
      _ConfirmSessionPinEntryPageState();
}

class _ConfirmSessionPinEntryPageState
    extends State<ConfirmSessionPinEntryPage> {
  late final _setupSessionPinBloc = BlocProvider.of<SetupSessionPinBloc>(
    context,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                alignment: Alignment.centerLeft,
                height: dimenFiftySix,
                child: PrimaryTextButton(
                  text: context.localizations.back,
                  onTap: () {
                    _setupSessionPinBloc.goToPreviousPage();
                  },
                ),
              ),
              verticalGapTwentyFour,
              const Center(child: BCNLogo()),
              verticalGapTwentyFour,
              Text(
                context.localizations.confirmSessionPin,
                style: context.appTextStyles.titleBold.copyWith(
                  color: context.appColors.neutralShade1Color,
                ),
              ),
              BlocBuilder<SetupSessionPinBloc, SetupSessionPinState>(
                buildWhen:
                    (_, newState) =>
                        newState is BiometricsUnavailable ||
                        newState is EnableBypassSwitch,
                builder: (context, state) {
                  if (state is! BiometricsUnavailable) {
                    return const SizedBox();
                  }
                  return Padding(
                    padding: commonScreenPadding.copyWith(
                      top: dimenFour,
                      bottom: dimenZero,
                    ),
                    child: Text(
                      context.localizations.biometricsNotAvailable,
                      style: context.appTextStyles.smallText1.copyWith(
                        color: context.appColors.warningColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  );
                },
              ),
              BlocBuilder<SetupSessionPinBloc, SetupSessionPinState>(
                buildWhen: (_, newState) {
                  return newState is ConfirmPinAndSessionPinMatches ||
                      newState is ConfirmPinAndSessionPinNotMatch;
                },
                builder: (context, state) {
                  if (state is ConfirmPinAndSessionPinNotMatch) {
                    return Padding(
                      padding: commonScreenPadding.copyWith(
                        top: dimenFour,
                        bottom: dimenZero,
                      ),
                      child: Text(
                        context
                            .localizations
                            .sessionPinDoesNotMatchWithConfirmPin,
                        style: context.appTextStyles.smallText1.copyWith(
                          color: context.appColors.errorTextFieldColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    );
                  }
                  return const SizedBox();
                },
              ),
              verticalGapTwentyFour,
              AgencyAuthPinField(
                onPinComplete: (confirmPin, clear) {
                  _setupSessionPinBloc.add(
                    SetupSessionPinEvent.confirmPin(context, confirmPin),
                  );
                  clear();
                },
              ),
              BlocBuilder<SetupSessionPinBloc, SetupSessionPinState>(
                buildWhen: (_, newState) {
                  // Only build for these states.
                  return newState is SetBiometricsValue ||
                      newState is EnableBypassSwitch ||
                      newState is DisableBypassSwitch;
                },
                builder: (context, state) {
                  if (state is SetBiometricsValue &&
                      !state.doesDeviceHasBiometricsHardware) {
                    return const SizedBox();
                  }
                  return Padding(
                    padding: commonScreenPadding.copyWith(top: dimenTwentyFour),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.localizations.bypassPinUsingBiometrics,
                          style: context.appTextStyles.smallText1.copyWith(
                            color: context.appColors.neutralShadeDefaultColor,
                          ),
                        ),
                        Switch(
                          value:
                              _setupSessionPinBloc
                                  .bypassSessionPinByBiometricsSwitchValue,
                          onChanged: (shouldBypass) {
                            _setupSessionPinBloc.add(
                              SetupSessionPinEvent.setBypassSessionPinUsingBiometrics(
                                shouldBypass,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
