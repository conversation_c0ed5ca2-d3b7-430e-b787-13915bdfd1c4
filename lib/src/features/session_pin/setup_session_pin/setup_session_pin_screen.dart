import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'confirm_session_pin_page.dart';
import 'setup_session_pin_bloc.dart';
import 'setup_session_pin_entry.dart';
import 'setup_session_pin_intro_page.dart';

class SetupSessionPinScreen extends StatefulWidget {
  static const id = "/setup-session-pin-screen";

  const SetupSessionPinScreen({Key? key}) : super(key: key);

  @override
  State<SetupSessionPinScreen> createState() => _SetupSessionPinScreenState();
}

class _SetupSessionPinScreenState extends State<SetupSessionPinScreen> {
  @override
  Widget build(BuildContext context) {
    // Setting status bar to the primary color
    // as per current theme.
    final currentStyle = (context.isDarkMode
            ? SystemUiOverlayStyle.dark
            : SystemUiOverlayStyle.light)
        .copyWith(
          statusBarColor: context.appColors.primaryDefaultColor,
          statusBarBrightness:
              context.isDarkMode ? Brightness.dark : Brightness.light,
        );
    return BlocProvider(
      create: (context) => SetupSessionPinBloc(),
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: currentStyle,
        child: Builder(
          builder: (context) {
            final setupSessionPinBloc = BlocProvider.of<SetupSessionPinBloc>(
              context,
            );
            return PopScope(
              canPop: false,
              onPopInvokedWithResult: (bool didPop, _) async {
                if (didPop) {
                  return;
                }
                final double currentPage =
                    setupSessionPinBloc.pageController.page ?? 0;
                if (currentPage > 1) {
                  setupSessionPinBloc.goToPreviousPage();
                }
                return;
              },
              child: PageView(
                controller: setupSessionPinBloc.pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  SetupSessionPinIntroPage(
                    onContinue: () => setupSessionPinBloc.goToNextPage(),
                  ),
                  const SetupSessionPinEntryPage(),
                  const ConfirmSessionPinEntryPage(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    setStatusBarColorToTransparent();
    super.dispose();
  }
}
