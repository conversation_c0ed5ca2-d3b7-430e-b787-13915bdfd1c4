import 'package:bcn_agency_banking_flutter/src/core/auth/app_pin_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../core/logger.dart';
import '../../../widgets/dialogs.dart';

part 'enter_session_pin_bloc.freezed.dart';

class EnterSessionPinBloc
    extends Bloc<EnterSessionPinEvent, EnterSessionPinState> {
  late final String _sessionPin;
  int _attemptsLeft = 0;
  final VoidCallback? onSuccessfulAuthentication;
  final VoidCallback? onSignOut;
  final FocusNode authPinFocusNode = FocusNode();

  EnterSessionPinBloc({
    required this.onSuccessfulAuthentication,
    required this.onSignOut,
  }) : super(const Initial()) {
    on<EnterSessionPinEvent>((event, emit) async {
      switch (event) {
        case AuthenticateUser(:final context, :final sessionPin):
          l.d("Authenticating User");
          if (sessionPin == _sessionPin) {
            _onUserAuthenticatedSuccessfully(context);
          } else {
            _attemptsLeft--;
            l.d("Wrong Session Pin entered. attemptsLeft: $_attemptsLeft");
            if (_attemptsLeft == 0) {
              _forceSignOutUser(context);
            } else if (_attemptsLeft > 0) {
              emit(EnterSessionPinState.sessionPinAttemptLeft(_attemptsLeft));
            } else {
              throw DeveloperError("Negative Attempts Left.");
            }
            await AppPinProvider.setRemainingAppPinAttempts(_attemptsLeft);
          }

        case Initialise(:final context):
          _sessionPin = (await AppPinProvider.getAppPin())!;
          _attemptsLeft = (await AppPinProvider.getRemainingAppPinAttempts());
          if (_attemptsLeft <= maximumIncorrectAttempts &&
              _attemptsLeft != 0 &&
              context.mounted) {
            await _authenticateTheUserViaBiometrics(context, emit);

            /// Request for focus only after the widget is built.
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _requestPinFocus();
            });
          } else if (_attemptsLeft == 0) {
            if (context.mounted) {
              _forceSignOutUser(context);
            }
          } else {
            l.e("Negative Attempts left: $_attemptsLeft");
            throw DeveloperError("Negative Attempts Left");
          }

        case ForgotSessionPin(:final context):
          l.d("Called Forgot Session Pin");
          dismissKeyboard();
          final bool shouldSignOut = await _showForgotSessionPinDialog(context);
          if (shouldSignOut && context.mounted) {
            _signUserOut(context);
          }
      }
    });
  }

  Future<bool> _showForgotSessionPinDialog(BuildContext context) async {
    return await AgencyAppDialog.showAppDialog(
      context: context,
      contentText: context.localizations.forgotSessionPinDialogMessage,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: dialogContext.localizations.cancel,
              onTap: () => context.navigator.pop(false),
            ),
            PrimaryTextButton(
              text: dialogContext.localizations.signOut,
              onTap: () => context.navigator.pop(true),
            ),
          ],
    );
  }

  Future<void> _authenticateTheUserViaBiometrics(
    BuildContext context,
    Emitter<EnterSessionPinState> emit,
  ) async {
    await AppPinProvider.checkBiometricsAndAuthenticate(
      onSuccess: () => _onUserAuthenticatedSuccessfully(context),
      localisedReason: context.localizations.biometricAuthReason,
      onLockedOut: () {
        emit(const EnterSessionPinState.onLockedOut());
        _requestPinFocus();
      },
      onCancelled: () {
        emit(const EnterSessionPinState.onCancelled());
        _requestPinFocus();
      },
      onPermanentlyLockedOut: () {
        emit(const EnterSessionPinState.onPermanentlyLockedOut());
        _requestPinFocus();
      },
      onBiometricsNotEnrolled: () {
        emit(const EnterSessionPinState.onBiometricsNotEnrolled());
      },
    );
  }

  void _onUserAuthenticatedSuccessfully(BuildContext context) {
    AppPinProvider.setRemainingAppPinAttempts(maximumIncorrectAttempts);
    if (onSuccessfulAuthentication != null) {
      onSuccessfulAuthentication?.call();
    } else {
      context.navigator.pop(true);
    }
  }

  void _requestPinFocus() {
    // UnFocus the primary focus.
    dismissKeyboard();
    // Add a small delay for focusing
    // the pin fields.
    Future.delayed(100.milliseconds, () => authPinFocusNode.requestFocus());
  }

  void _forceSignOutUser(BuildContext context) async {
    l.d("Force Signing out user");
    onSignOut?.call();
    AppPinProvider.resetBiometricsPreferences();
    await forceSignOutUser(context);

    // Since after navigation,
    // the above context will become defunct,
    // we will take context from navigation service.
    _showForceSignOutDialog(NavigationService.navigatorKey.currentContext!);
  }

  void _signUserOut(BuildContext context) async {
    onSignOut?.call();
    await forceSignOutUser(context);
  }

  void _showForceSignOutDialog(BuildContext context) async {
    await AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.tooManyAppPinAttempts,
    );
  }
}

@freezed
sealed class EnterSessionPinState with _$EnterSessionPinState {
  const factory EnterSessionPinState.initial() = Initial;

  const factory EnterSessionPinState.sessionPinAttemptLeft(int attemptsLeft) =
      SessionPinAttemptLeft;

  const factory EnterSessionPinState.onLockedOut() = OnLockedOut;

  const factory EnterSessionPinState.onCancelled() = OnCancelled;

  const factory EnterSessionPinState.onPermanentlyLockedOut() =
      OnPermanentlyLockedOut;

  const factory EnterSessionPinState.onBiometricsNotEnrolled() =
      OnBiometricsNotEnrolled;
}

@freezed
sealed class EnterSessionPinEvent with _$EnterSessionPinEvent {
  const factory EnterSessionPinEvent.authenticateUser(
    BuildContext context,
    String sessionPin,
  ) = AuthenticateUser;

  const factory EnterSessionPinEvent.initialise(BuildContext context) =
      Initialise;

  const factory EnterSessionPinEvent.forgotSessionPin(BuildContext context) =
      ForgotSessionPin;
}
