import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../errors/developer_error.dart';
import '../../../helpers/helpers.dart';
import '../../../resources/dimensions.dart';
import '../../../widgets/agency_auth_pin_field.dart';
import '../../../widgets/bcn_logo.dart';
import '../../../widgets/primary_text_button.dart';
import 'enter_session_pin_bloc.dart';

class EnterSessionPinScreen extends StatefulWidget {
  final bool isCancellable;
  final VoidCallback? onSuccessfulAuthentication;
  final VoidCallback? onSignOut;

  static const id = "/session-pin-screen";

  // Adding a different route for app pin screen when application is launched.
  // This helps us to push app pin screen on launch and not cause any other
  // problems with other app pin screens as this string is also added to 'routes'
  // for navigation.
  static const launchId = "/launch-session-pin-screen";

  const EnterSessionPinScreen({
    Key? key,
    this.isCancellable = true,
    this.onSuccessfulAuthentication,
    this.onSignOut,
  }) : super(key: key);

  @override
  State<EnterSessionPinScreen> createState() => _EnterSessionPinScreenState();
}

class _EnterSessionPinScreenState extends State<EnterSessionPinScreen> {
  final authPinFocusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    // Setting status bar to the primary color
    // as per current theme.
    final currentStyle = (context.isDarkMode
            ? SystemUiOverlayStyle.dark
            : SystemUiOverlayStyle.light)
        .copyWith(
          statusBarColor: context.appColors.primaryDefaultColor,
          statusBarBrightness:
              context.isDarkMode ? Brightness.dark : Brightness.light,
        );
    return BlocProvider(
      create:
          (context) => EnterSessionPinBloc(
            onSuccessfulAuthentication: widget.onSuccessfulAuthentication,
            onSignOut: widget.onSignOut,
          )..add(EnterSessionPinEvent.initialise(context)),
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, _) async {
          if (didPop) {
            return;
          }
          final NavigatorState navigator = Navigator.of(context);
          if (widget.isCancellable) {
            navigator.pop();
            return;
          }
        },
        child: AnnotatedRegion<SystemUiOverlayStyle>(
          value: currentStyle,
          child: Scaffold(
            body: SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    if (widget.isCancellable) _buildCancelButton(context),
                    verticalGapEighty,
                    const Center(child: BCNLogo()),
                    verticalGapTwentyFour,
                    _buildSessionPinTitle(context),
                    _buildWarningsIfAny(),
                    _buildErrorsIfAny(),
                    verticalGapTwentyFour,
                    _buildAuthPinFields(),
                    verticalGapTwentyFour,
                    _buildForgotSessionTextButton(context),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Container _buildCancelButton(BuildContext context) {
    return Container(
      height: dimenFiftySix,
      alignment: Alignment.centerLeft,
      child: PrimaryTextButton(
        text: context.localizations.cancel,
        onTap: () => context.rootNavigator.pop(false),
      ),
    );
  }

  Text _buildSessionPinTitle(BuildContext context) {
    return Text(
      context.localizations.enterSessionPin,
      style: context.appTextStyles.titleBold.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
      textAlign: TextAlign.center,
    );
  }

  BlocBuilder<EnterSessionPinBloc, EnterSessionPinState> _buildWarningsIfAny() {
    return BlocBuilder<EnterSessionPinBloc, EnterSessionPinState>(
      buildWhen:
          (_, newState) =>
              newState is OnLockedOut || newState is OnPermanentlyLockedOut,
      builder: (context, state) {
        if (state is Initial) return const SizedBox();
        return Padding(
          padding: commonScreenPadding.copyWith(
            top: dimenFour,
            bottom: dimenZero,
          ),
          child: Text(
            _getWarningMessageBasedOnState(context, state),
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.warningColor,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  BlocBuilder<EnterSessionPinBloc, EnterSessionPinState> _buildErrorsIfAny() {
    return BlocBuilder<EnterSessionPinBloc, EnterSessionPinState>(
      buildWhen: (_, newState) => newState is SessionPinAttemptLeft,
      builder: (context, state) {
        if (state is! SessionPinAttemptLeft) {
          return const SizedBox();
        }
        return Padding(
          padding: commonScreenPadding.copyWith(
            top: dimenFour,
            bottom: dimenZero,
          ),
          child: Text(
            state.attemptsLeft > 1
                ? context.localizations.wrongSessionPinEnteredError(
                  state.attemptsLeft,
                )
                : context.localizations.singleAttemptLeftError,
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.errorTextFieldColor,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  Builder _buildForgotSessionTextButton(BuildContext context) {
    return Builder(
      builder: (context) {
        final enterSessionPinBloc = BlocProvider.of<EnterSessionPinBloc>(
          context,
        );
        return Padding(
          padding: commonScreenPadding.copyWith(top: dimenZero),
          child: PrimaryTextButton(
            text: context.localizations.forgotSessionPin,
            fixedSize: buttonHeight,
            onTap: () {
              dismissKeyboard();
              enterSessionPinBloc.add(
                EnterSessionPinEvent.forgotSessionPin(context),
              );
            },
          ),
        );
      },
    );
  }

  Builder _buildAuthPinFields() {
    return Builder(
      builder: (context) {
        final enterSessionPinBloc = BlocProvider.of<EnterSessionPinBloc>(
          context,
        );
        return Center(
          child: AgencyAuthPinField(
            focusNode: enterSessionPinBloc.authPinFocusNode,
            onPinComplete: (pin, clear) {
              enterSessionPinBloc.add(
                EnterSessionPinEvent.authenticateUser(context, pin),
              );
              clear();
            },
          ),
        );
      },
    );
  }

  String _getWarningMessageBasedOnState(
    BuildContext context,
    EnterSessionPinState state,
  ) {
    if (state is OnPermanentlyLockedOut || state is OnBiometricsNotEnrolled) {
      return context.localizations.biometricsUnavailableOnDevice;
    } else if (state is OnLockedOut) {
      return context.localizations.exhaustedBiometricAttempts;
    } else {
      throw DeveloperError(" $state State is not handled");
    }
  }

  @override
  void dispose() {
    setStatusBarColorToTransparent();
    super.dispose();
  }
}

class EnterSessionPinScreenArguments {
  const EnterSessionPinScreenArguments({
    Key? key,
    this.isCancellable = true,
    this.onSignOut,
    this.onSuccessfulAuthentication,
  });

  final bool isCancellable;
  final VoidCallback? onSignOut;
  final VoidCallback? onSuccessfulAuthentication;
}
