import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_label_with_value.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../resources/dimensions.dart';
import '../../../widgets/bottomsheets.dart';
import '../../../widgets/dotted_divider.dart';
import '../../../widgets/phone_button_widget.dart';
import '../../../widgets/section_widget.dart';
import '../../../widgets/title_widget.dart';
import 'blocs/unblock_mt_refund_request_bloc.dart';

class BlockedRefundRequestDetailsScreen extends StatelessWidget {
  static const id = "/blocked-refund-request-details-screen";
  final BlockedMoneyTransferRequest blockedRefundRequest;

  const BlockedRefundRequestDetailsScreen({
    Key? key,
    required this.blockedRefundRequest,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      disableBackGesture: false,
      appBarTitle: context.localizations.senderRequest,
      padding: verticalPaddingEight,
      ctaWidget: _buildCTA(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _getRefundRequestDetails(context),
          _getAgentDetails(context),
          TitleWidget(title: context.localizations.transactionDetailsString),
          _buildAmountTransferredWidget(context),
        ],
      ),
    );
  }

  PrimaryButton _buildCTA(BuildContext context) {
    return PrimaryButton(
      labelText: context.localizations.unblockRefund,
      onPressed: () async {
        final ABComment? unblockRefundComment =
            await BottomSheets.showCommentBottomSheet(
              context: context,
              commentReason: context.localizations.unblockRefundsCommentReason,
              ctaButtonText: context.localizations.unblock,
            );
        if (unblockRefundComment != null && context.mounted) {
          BlocProvider.of<UnblockMoneyTransferRefundRequestBloc>(context).add(
            UnblockMoneyTransferRefundRequestEvent.unblockMoneyRequest(
              context,
              blockedRefundRequest.requestDetails.requestId,
              unblockRefundComment,
            ),
          );
        }
      },
    );
  }

  SectionWidget _getRefundRequestDetails(BuildContext context) {
    final requestDetail = blockedRefundRequest.requestDetails;
    final senderDetail = requestDetail.senderDetail;
    return SectionWidget(
      title: context.localizations.requestDetails,
      children: [
        InfoLabel(
          title: context.localizations.sendersName,
          bodyText: senderDetail.userName.text,
        ),
        InfoLabel(
          title: context.localizations.requestIdHeader,
          bodyText: requestDetail.requestId.id,
        ),
        InfoLabel(
          title: context.localizations.sendersMobileNumber,
          bodyText: senderDetail.phoneNumber.formattedPhoneNumber,
          actionButton: PhoneButtonWidget(
            phoneNumber: senderDetail.phoneNumber,
          ),
        ),
        InfoLabel(
          title: context.localizations.createdOnHeader,
          bodyText: DateFormatter.exactTimeFormat(
            context,
          ).format(blockedRefundRequest.createdAt.toLocal()),
        ),
        InfoLabel(
          title: context.localizations.blockedOnHeader,
          bodyText: DateFormatter.exactTimeFormat(
            context,
          ).format(blockedRefundRequest.blockedAt.toLocal()),
        ),
      ],
    );
  }

  SectionWidget _getAgentDetails(BuildContext context) {
    final agentDetail = blockedRefundRequest.requestDetails.agentDetail;
    return SectionWidget(
      title: context.localizations.agentDetails,
      children: [
        InfoLabel(
          title: context.localizations.agentsName,
          bodyText: agentDetail.userName.text,
        ),
        InfoLabel(
          title: context.localizations.agentsMobileNumber,
          bodyText: agentDetail.phoneNumber.formattedPhoneNumber,
          actionButton: PhoneButtonWidget(phoneNumber: agentDetail.phoneNumber),
        ),
      ],
    );
  }

  Widget _buildAmountTransferredWidget(BuildContext context) {
    return Padding(
      padding: commonScreenPadding,
      child: Column(
        children: [
          const DottedDivider(),
          verticalGapSixteen,
          _buildAmountTransferred(context),
          verticalGapSixteen,
          const DottedDivider(),
        ],
      ),
    );
  }

  Widget _buildAmountTransferred(BuildContext context) {
    final textStyle = context.appTextStyles.normalBold.copyWith(
      color: context.appColors.neutralShade1Color,
    );
    return TransactionDetailsLabelWithValue(
      label: context.localizations.amountTransferred,
      value:
          blockedRefundRequest.requestDetails.amount.localisedFormattedAmount,
      isShownInDialog: false,
      labelStyle: textStyle,
      valueStyle: textStyle,
    );
  }
}
