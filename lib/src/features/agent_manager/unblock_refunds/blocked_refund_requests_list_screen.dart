import 'package:agency_banking_rpcs/agency/blocked_money_transfer_request_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/blocked_refund_request_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/generic_error_or_empty_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/pull_to_refresh_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/request_card.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'blocs/blocked_refund_requests_bloc.dart';

class BlockedRefundRequestsListScreen extends StatefulWidget {
  static const id = "/blocked-refund-requests-list-screen";

  const BlockedRefundRequestsListScreen({super.key});

  @override
  State<BlockedRefundRequestsListScreen> createState() =>
      _BlockedRefundRequestsListScreenState();
}

class _BlockedRefundRequestsListScreenState
    extends State<BlockedRefundRequestsListScreen> {
  late final BlockedRefundRequestsBloc _blockedRefundRequestsBloc =
      BlocProvider.of<BlockedRefundRequestsBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.unblockRefunds),
      body: BlocBuilder<BlockedRefundRequestsBloc, BlockedRefundRequestsState>(
        buildWhen: (_, BlockedRefundRequestsState newState) {
          return newState is! RefreshInProgress;
        },
        builder: (BuildContext context, BlockedRefundRequestsState state) {
          if (state is OnData) {
            return _getBlockedRefundRequestList(state.requests);
          } else if (state is TransientError) {
            return NoInternetWidget(
              onRetryButtonClicked:
                  () => _blockedRefundRequestsBloc.add(
                    const BlockedRefundRequestsEvent.getBlockedRefundRequests(),
                  ),
            );
          } else {
            return const Spinner();
          }
        },
      ),
    );
  }

  void _onRefresh() {
    _blockedRefundRequestsBloc.add(
      const BlockedRefundRequestsEvent.pullToRefresh(),
    );
  }

  Widget _getBlockedRefundRequestList(
    List<BlockedMoneyTransferRequest> requests,
  ) {
    return PullToRefreshWidget(
      onRefresh: _onRefresh,
      controller: _blockedRefundRequestsBloc.refreshController,
      child:
          requests.isEmpty
              ? GenericErrorOrEmptyWidget(
                labelText: context.localizations.noUnblockedRefundRequestsTitle,
                iconAssetPath: ABAssets.fileSlashIcon,
              )
              : ListView.separated(
                itemCount: requests.length,
                padding: commonScreenPadding,
                separatorBuilder: (_, __) => verticalGapSixteen,
                itemBuilder: (context, index) {
                  final blockedRefundRequest = requests[index];
                  return RequestCard(
                    requestCardModel: RequestCardModel.unblockRefunds(
                      blockedRefundRequest,
                    ),
                    onTap: () async {
                      await context.navigator.pushNamed(
                        BlockedRefundRequestDetailsScreen.id,
                        arguments: blockedRefundRequest,
                      );
                      _refreshScreen();
                    },
                  );
                },
              ),
    );
  }

  void _refreshScreen() {
    if (!mounted) return;
    _blockedRefundRequestsBloc.refreshController.requestRefresh(
      needMove: false,
    );
  }
}
