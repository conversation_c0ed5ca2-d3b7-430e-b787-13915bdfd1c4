import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockUnblockMoneyTransferRefundRequestRPCImpl
    extends UnblockMoneyTransferRefundRequestRPC {
  @override
  Future<
    LeoRPCResult<
      UnblockMoneyTransferRefundRequestResponse,
      UnblockMoneyTransferRefundRequestError
    >
  >
  execute(UnblockMoneyTransferRefundRequestRequest request) async {
    final response = UnblockMoneyTransferRefundRequestResponse();

    final error =
        UnblockMoneyTransferRefundRequestError.UnblockMoneyTransferRefundRequestErrorInvalidRequestIdError(
          errorCode: "",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return await Future.delayed(2.seconds, () => result);
  }
}
