import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetBlockedMoneyTransferRefundRequestsRPCImpl
    extends GetBlockedMoneyTransferRefundRequestsRPC {
  @override
  Future<LeoRPCResult<GetBlockedMoneyTransferRefundRequestsResponse, Never>>
  execute(GetBlockedMoneyTransferRefundRequestsRequest request) {
    final response = LeoRPCResult<
      GetBlockedMoneyTransferRefundRequestsResponse,
      Never
    >.response(
      GetBlockedMoneyTransferRefundRequestsResponse(
        blockedMoneyTransferRefund: [
          BlockedMoneyTransferRequest(
            requestDetails: MoneyTransferRequestDetails(
              requestId: ABRequestId(id: 'AE14n7xC'),
              senderDetail: ABUserDetails(
                userName: ABUserName(text: '<PERSON>'),
                phoneNumber: LeoPhoneNumber("+************"),
                nationalId: NationalId(id: 'M2X056ZH'),
              ),
              agentDetail: ABUserDetails(
                userName: ABUserName(text: 'Esther Howard'),
                phoneNumber: LeoPhoneNumber('+265996556381'),
                nationalId: NationalId(id: 'NFKDLE02'),
              ),
              amount: Amount(
                amount: 100000000,
                currency: Currency(currencyCode: 'MWK'),
              ),
            ),
            createdAt: DateTime(2020, 12, 16, 17, 30),
            blockedAt: DateTime(2021, 01, 03, 17, 30),
          ),
          BlockedMoneyTransferRequest(
            requestDetails: MoneyTransferRequestDetails(
              requestId: ABRequestId(id: 'Uy14n7xC'),
              senderDetail: ABUserDetails(
                userName: ABUserName(text: 'Kenneth Issac'),
                phoneNumber: LeoPhoneNumber('+************'),
                nationalId: NationalId(id: 'M2X056ZH'),
              ),
              agentDetail: ABUserDetails(
                userName: ABUserName(text: 'Jane Cooper'),
                phoneNumber: LeoPhoneNumber('+************'),
                nationalId: NationalId(id: 'NFKDLE02'),
              ),
              amount: Amount(
                amount: 80000000,
                currency: Currency(currencyCode: 'MWK'),
              ),
            ),
            createdAt: DateTime(2020, 12, 14, 17, 30),
            blockedAt: DateTime(2021, 01, 01, 14, 12),
          ),
          BlockedMoneyTransferRequest(
            requestDetails: MoneyTransferRequestDetails(
              requestId: ABRequestId(id: 'Ik76n7xC'),
              senderDetail: ABUserDetails(
                userName: ABUserName(text: 'Steve Colorado'),
                phoneNumber: LeoPhoneNumber('+265996530134'),
                nationalId: NationalId(id: 'M2X056ZH'),
              ),
              agentDetail: ABUserDetails(
                userName: ABUserName(text: 'Esther Howard'),
                phoneNumber: LeoPhoneNumber('+265996556381'),
                nationalId: NationalId(id: 'NFKDLE02'),
              ),
              amount: Amount(
                amount: 90000000,
                currency: Currency(currencyCode: 'MWK'),
              ),
            ),
            createdAt: DateTime(2020, 12, 16, 17, 30),
            blockedAt: DateTime(2021, 01, 03, 17, 30),
          ),
        ],
      ),
    );

    return Future.delayed(2.seconds, () => response);
  }
}
