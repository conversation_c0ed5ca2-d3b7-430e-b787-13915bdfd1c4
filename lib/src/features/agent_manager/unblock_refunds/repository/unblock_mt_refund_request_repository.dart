import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/mock_rpc_impls/mock_unblock_money_transfer_refund_request_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class UnblockMoneyTransferRefundRequestRepository {
  Future<
    LeoRPCResult<
      UnblockMoneyTransferRefundRequestResponse,
      UnblockMoneyTransferRefundRequestError
    >
  >
  unblockRefundRequest(ABRequestId requestId, ABComment comment) async {
    final UnblockMoneyTransferRefundRequestRPC
    unblockMoneyTransferRefundRequestRPCImpl =
        currentFlavor.isMock
            ? MockUnblockMoneyTransferRefundRequestRPCImpl()
            : UnblockMoneyTransferRefundRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );

    final UnblockMoneyTransferRefundRequestRequest
    unblockMoneyTransferRefundRequestRequest =
        UnblockMoneyTransferRefundRequestRequest(
          requestId: requestId,
          comment: comment,
        );

    return await unblockMoneyTransferRefundRequestRPCImpl.execute(
      unblockMoneyTransferRefundRequestRequest,
    );
  }
}
