import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/mock_rpc_impls/mock_get_blocked_money_transfer_refund_requests_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class RefundRequestRepository {
  Future<LeoRPCResult<GetBlockedMoneyTransferRefundRequestsResponse, Never>>
  getRefundRequests() async {
    final GetBlockedMoneyTransferRefundRequestsRPC
    getBlockedMoneyTransferRefundRequestsRPCImpl =
        currentFlavor.isMock
            ? MockGetBlockedMoneyTransferRefundRequestsRPCImpl()
            : GetBlockedMoneyTransferRefundRequestsRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final GetBlockedMoneyTransferRefundRequestsRequest
    getBlockedMoneyTransferRefundRequestsRequest =
        GetBlockedMoneyTransferRefundRequestsRequest();
    final LeoRPCResult<GetBlockedMoneyTransferRefundRequestsResponse, Never>
    getBlockedMoneyTransferRefundRequestsResponse =
        await getBlockedMoneyTransferRefundRequestsRPCImpl.execute(
          getBlockedMoneyTransferRefundRequestsRequest,
        );

    return getBlockedMoneyTransferRefundRequestsResponse;
  }
}
