import 'package:agency_banking_rpcs/agency/blocked_money_transfer_request_type.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/repository/refund_request_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../core/logger.dart';

part 'blocked_refund_requests_bloc.freezed.dart';

class BlockedRefundRequestsBloc
    extends Bloc<BlockedRefundRequestsEvent, BlockedRefundRequestsState>
    with RPC<PERSON>andler {
  final refreshController = RefreshController();
  final RefundRequestRepository _refundRequestRepository =
      RefundRequestRepository();

  BlockedRefundRequestsBloc() : super(const Initial()) {
    on<BlockedRefundRequestsEvent>((event, emit) async {
      switch (event) {
        case GetBlockedRefundRequests():
          emit(const BlockedRefundRequestsState.loading());
          await _getBlockedRefundRequests(emit);
        case PullToRefresh():
          l.d("Called Pull to refresh in BlockedRefundRequests");
          emit(const BlockedRefundRequestsState.refreshInProgress());
          await _getBlockedRefundRequests(emit);
      }
    });
  }

  Future<void> _getBlockedRefundRequests(
    Emitter<BlockedRefundRequestsState> emit,
  ) async {
    l.d("Getting BlockedRefundRequests");
    await rpcHandler(
      () async {
        final rpcResult = await _refundRequestRepository.getRefundRequests();
        l.d("Refund Requests: $rpcResult");
        rpcResult.when(
          response: (response) {
            emit(
              BlockedRefundRequestsState.onData(
                response.blockedMoneyTransferRefund,
              ),
            );
            refreshController.refreshCompleted();
          },
          error: (error) {
            // This should never happen since this RPC call does not
            // throw any errors.
            // Therefore, seeing this exception is a developer error.
            l.e("Error in BlockedRefundRequests");
            throw DeveloperError('Something went wrong: $error');
          },
        );
      },
      onTransientError: (String errorMessage) {
        l.d("Emitting Transient Error");
        refreshController.refreshFailed();
        emit(BlockedRefundRequestsState.transientError(errorMessage));
      },
    );
  }
}

@freezed
sealed class BlockedRefundRequestsState with _$BlockedRefundRequestsState {
  const factory BlockedRefundRequestsState.initial() = Initial;

  const factory BlockedRefundRequestsState.loading() = Loading;

  const factory BlockedRefundRequestsState.onData(
    List<BlockedMoneyTransferRequest> requests,
  ) = OnData;

  const factory BlockedRefundRequestsState.refreshInProgress() =
      RefreshInProgress;

  const factory BlockedRefundRequestsState.transientError(String errorMessage) =
      TransientError;
}

@freezed
sealed class BlockedRefundRequestsEvent with _$BlockedRefundRequestsEvent {
  const factory BlockedRefundRequestsEvent.getBlockedRefundRequests() =
      GetBlockedRefundRequests;

  const factory BlockedRefundRequestsEvent.pullToRefresh() = PullToRefresh;
}
