import 'dart:async';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/blocked_refund_requests_list_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/repository/unblock_mt_refund_request_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../core/logger.dart';
import '../../../session_pin/enter_session_pin/enter_session_pin_screen.dart';

part 'unblock_mt_refund_request_bloc.freezed.dart';

class UnblockMoneyTransferRefundRequestBloc
    extends
        Bloc<
          UnblockMoneyTransferRefundRequestEvent,
          UnblockMoneyTransferRefundRequestState
        >
    with RPCHandler {
  final UnblockMoneyTransferRefundRequestRepository
  _unblockMoneyTransferRefundRequestRepository =
      UnblockMoneyTransferRefundRequestRepository();

  UnblockMoneyTransferRefundRequestBloc() : super(const Initial()) {
    on<UnblockMoneyTransferRefundRequestEvent>((event, emit) async {
      switch (event) {
        case UnblockMoneyRequest(
          :final context,
          :final requestId,
          :final comment,
        ):
          await _attemptUnblockRefund(context, requestId, comment, emit);
      }
    });
  }

  Future<void> _attemptUnblockRefund(
    BuildContext context,
    ABRequestId requestId,
    ABComment comment,
    Emitter<UnblockMoneyTransferRefundRequestState> emit,
  ) async {
    l.d('''Attempting Unblock refund.
    requestId: $requestId,
    comment: $comment''');
    await rpcHandler(
      () async {
        final isUserAuthenticated = await _authenticateUserUsingSessionPIN(
          context,
        );
        if (isUserAuthenticated ?? false) {
          // This state is emitted to acknowledge that the authentication is performed and the call
          // to the RPC is made. Displays a loading dialog until the response is received.
          emit(const UnblockMoneyTransferRefundRequestState.loading());
          if (context.mounted) {
            AgencyAppDialog.showSpinnerDialog(context);
          }
          final rpcResult = await _unblockMoneyTransferRefundRequestRepository
              .unblockRefundRequest(requestId, comment);
          l.d("UnBlock Money transfer Refund Request RPC result: $rpcResult");
          if (context.mounted) {
            // Pop the spinner dialog before showing success/error response dialog.
            context.rootNavigator.pop();
          }
          rpcResult.when(
            response: (_) {
              _showSuccessDialog(context);
            },
            error: (error) {
              emit(
                const UnblockMoneyTransferRefundRequestState.prominentError(),
              );
              error.when(
                invalidRequestId: (error) {
                  // There will be no case of an invalid request id.
                  // Seeing this exception indicates a developer error.
                  throw DeveloperError(
                    'Something went wrong : ${error.toString()}',
                  );
                },
                requestAlreadyUnblocked: (_) {
                  AgencyAppDialog.showErrorDialog(
                    context: context,
                    contentText:
                        context.localizations.refundRequestUnblockSuccess,
                    onOkay: (context) => _navigateToRequestListScreen(context),
                    isDismissible: false,
                  );
                },
                requestAlreadyRefunded: (_) {
                  ProminentErrorHandler.requestAmountRefunded(
                    (context) => _navigateToRequestListScreen(context),
                  );
                },
              );
            },
          );
        }
      },
      onTransientError: (_) {
        // Pop the spinner dialog.
        context.rootNavigator.pop();
        emit(const UnblockMoneyTransferRefundRequestState.transientError());
      },
    );
  }

  Future<bool?> _authenticateUserUsingSessionPIN(BuildContext context) async {
    return await context.rootNavigator.pushNamed(EnterSessionPinScreen.id)
        as bool?;
  }

  void _navigateToRequestListScreen(BuildContext context) {
    context.navigator.popUntil(
      ModalRoute.withName(BlockedRefundRequestsListScreen.id),
    );
  }

  void _showSuccessDialog(BuildContext context) {
    AgencyAppDialog.showAppDialog(
      context: context,
      actions:
          (context) => [
            PrimaryTextButton(
              text: context.localizations.okay,
              onTap: () => _navigateToRequestListScreen(context),
            ),
          ],
      contentText: context.localizations.refundRequestUnblockSuccess,
    );
  }
}

@freezed
sealed class UnblockMoneyTransferRefundRequestEvent
    with _$UnblockMoneyTransferRefundRequestEvent {
  const factory UnblockMoneyTransferRefundRequestEvent.unblockMoneyRequest(
    BuildContext context,
    ABRequestId requestId,
    ABComment comment,
  ) = UnblockMoneyRequest;
}

@freezed
sealed class UnblockMoneyTransferRefundRequestState
    with _$UnblockMoneyTransferRefundRequestState {
  const factory UnblockMoneyTransferRefundRequestState.initial() = Initial;

  const factory UnblockMoneyTransferRefundRequestState.loading() = Loading;

  const factory UnblockMoneyTransferRefundRequestState.prominentError() =
      ProminentError;

  const factory UnblockMoneyTransferRefundRequestState.transientError() =
      TransientError;
}
