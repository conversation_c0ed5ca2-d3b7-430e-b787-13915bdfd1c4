import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/bloc/sign_in_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/bloc/sign_in_otp_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sign_in_otp_validation_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AgentManagerOtpScreen extends StatefulWidget {
  static const id = '/agent-manager-otp-screen';

  const AgentManagerOtpScreen({super.key});

  @override
  State<AgentManagerOtpScreen> createState() => _AgentManagerOtpScreenState();
}

class _AgentManagerOtpScreenState extends State<AgentManagerOtpScreen> {
  late final SignInOTPBloc _signInOTPBloc = BlocProvider.of<SignInOTPBloc>(
    context,
  );
  late final SignInBloc _signInBloc = BlocProvider.of<SignInBloc>(context);
  final GlobalKey<FormState> _otpFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateOtp = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SignInOTPBloc, SignInOTPState>(
      builder: (context, state) {
        return SignInOTPValidationScreen(
          formKey: _otpFormKey,
          autoValidateMode: _shouldValidateOtp,
          phoneNumber: _signInBloc.phoneNumber!,
          otpValidityDetails: _signInBloc.otpValidityDetails!,
          onOTPResend: () async {
            _signInOTPBloc.add(
              SignInOTPEvent.resendOTP(context, _signInBloc.otpId!),
            );

            /// Wait For Bloc to send some State.
            final latestState = await _signInOTPBloc.stream.firstOrNull;
            return switch (latestState) {
              OTPResentSuccessfully(:final resendDetails) => resendDetails,
              _ => null,
            };
          },
          onOTPChanged: (otp) {
            _signInOTPBloc.add(SignInOTPEvent.addOtp(otp));
          },
          validateOTPButton: PrimaryButton(
            labelText: context.localizations.continueText.toUpperCase(),
            onPressed: () {
              final isFormValidated =
                  _otpFormKey.currentState?.validate() ?? false;
              if (!isFormValidated) {
                setState(() {
                  _shouldValidateOtp = AutovalidateMode.onUserInteraction;
                });
                return;
              }
              _signInOTPBloc.add(
                SignInOTPEvent.confirmOTP(context, _signInBloc.otpId!),
              );
            },
          ),
        );
      },
    );
  }
}
