import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockResendSignInOTPRPCImpl extends BackOfficeResendSignInOTPRPC {
  @override
  Future<
    LeoRPCResult<
      BackOfficeResendSignInOTPResponse,
      BackOfficeResendSignInOTPError
    >
  >
  execute(BackOfficeResendSignInOTPRequest request) {
    final BackOfficeResendSignInOTPResponse response =
        BackOfficeResendSignInOTPResponse(
          nextResendAt: DateTime.now().add(5.minutes),
          expiresAt: DateTime.now().add(10.minutes),
          numberOfResendsLeft: 2,
        );

    final BackOfficeResendSignInOTPError error =
        BackOfficeResendSignInOTPError.BackOfficeResendSignInOTPErrorBoUserDisabledError(
          errorCode: "",
        );

    final LeoRPCResult<
      BackOfficeResendSignInOTPResponse,
      BackOfficeResendSignInOTPError
    >
    result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
