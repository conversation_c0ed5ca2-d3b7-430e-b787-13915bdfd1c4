import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockSubmitResetPasswordRPCImpl extends SubmitResetPasswordRPC {
  @override
  Future<LeoRPCResult<SubmitResetPasswordResponse, SubmitResetPasswordError>>
  execute(SubmitResetPasswordRequest request) async {
    final response = SubmitResetPasswordResponse(
      result: SubmitResetPasswordResponseResultEnum.LOGGED_IN_MOBILE(
        slt: "MOCKSLT",
        llt: "MOCKLLT",
      ),
    );

    final error =
        SubmitResetPasswordError.SubmitResetPasswordErrorBoUserDisabledError(
          errorCode: '',
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return await Future.delayed(2.seconds, () => result);
  }
}
