import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockConfirmSignInOTPRPCImpl extends BackOfficeConfirmSignInOTPRPC {
  @override
  Future<
    LeoRPCResult<
      BackOfficeConfirmSignInOTPResponse,
      BackOfficeConfirmSignInOTPError
    >
  >
  execute(BackOfficeConfirmSignInOTPRequest request) {
    final BackOfficeConfirmSignInOTPResponse response =
        BackOfficeConfirmSignInOTPResponse(
          result: BackOfficeConfirmSignInOTPResponseResultEnum.LOGGED_IN_MOBILE(
            llt: "MOCKLLT",
            slt: "MOCKSLT",
          ),
        );

    final BackOfficeConfirmSignInOTPError error =
        BackOfficeConfirmSignInOTPError.BackOfficeConfirmSignInOTPErrorBoUserDisabledError(
          errorCode: "",
        );

    final LeoRPCResult<
      BackOfficeConfirmSignInOTPResponse,
      BackOfficeConfirmSignInOTPError
    >
    result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
