import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetUserPrivilegesRPCImpl extends GetUserPrivilegesRPC {
  @override
  Future<LeoRPCResult<GetUserPrivilegesResponse, Never>> execute(
    GetUserPrivilegesRequest request,
  ) {
    final response = LeoRPCResult<GetUserPrivilegesResponse, Never>.response(
      GetUserPrivilegesResponse(
        userId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
        privileges: agentManagerPrivileges,
      ),
    );

    return Future.delayed(2.seconds, () => response);
  }
}
