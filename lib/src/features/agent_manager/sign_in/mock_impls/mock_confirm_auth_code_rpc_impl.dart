import 'dart:async';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockConfirmAuthCodeRPCImpl extends ConfirmAuthCodeRPC {
  @override
  Future<LeoRPCResult<ConfirmAuthCodeResponse, ConfirmAuthCodeError>> execute(
    ConfirmAuthCodeRequest request,
  ) {
    final ConfirmAuthCodeResponse response = ConfirmAuthCodeResponse(
      otpId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
      nextResendAt: DateTime.now().add(5.minutes),
      expiresAt: DateTime.now().add(10.minutes),
    );

    final ConfirmAuthCodeError error =
        ConfirmAuthCodeError.ConfirmAuthCodeErrorBoUserDisabledError(
          errorCode: "",
        );

    final LeoRPCResult<ConfirmAuthCodeResponse, ConfirmAuthCodeError> result =
        getLeoRPCResult(
          shouldThrowError: false,
          response: response,
          error: error,
        );

    return Future.delayed(2.seconds, () => result);
  }
}
