import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockSignInBOUserRPCImpl extends SignInBOUserRPC {
  @override
  Future<LeoRPCResult<SignInBOUserResponse, SignInBOUserError>> execute(
    SignInBOUserRequest request,
  ) {
    final SignInBOUserResponse response = SignInBOUserResponse(
      passwordValidatedToken: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
      authCodeLHS: "4444",
    );

    final SignInBOUserError error =
        SignInBOUserError.SignInBOUserErrorBoUserDisabledError(errorCode: "");

    final LeoRPCResult<SignInBOUserResponse, SignInBOUserError> result =
        getLeoRPCResult(
          shouldThrowError: false,
          response: response,
          error: error,
        );

    return Future.delayed(2.seconds, () => result);
  }
}
