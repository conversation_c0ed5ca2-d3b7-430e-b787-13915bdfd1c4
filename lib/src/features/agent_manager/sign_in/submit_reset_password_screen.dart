import 'package:agency_banking_rpcs/types/password_policy_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/bloc/submit_reset_password_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/password_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sub_screen_layout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../models/submit_reset_password_screen_arguments.dart';

class SubmitResetPasswordScreen extends StatefulWidget {
  static const id = '/reset-password-screen';

  final SubmitResetPasswordScreenArguments submitResetPasswordScreenArguments;

  const SubmitResetPasswordScreen({
    Key? key,
    required this.submitResetPasswordScreenArguments,
  }) : super(key: key);

  @override
  State<SubmitResetPasswordScreen> createState() =>
      _SubmitResetPasswordScreenState();
}

class _SubmitResetPasswordScreenState extends State<SubmitResetPasswordScreen> {
  late final SubmitResetPasswordBloc _submitResetPasswordBloc =
      BlocProvider.of<SubmitResetPasswordBloc>(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SubmitResetPasswordBloc, SubmitResetPasswordState>(
      builder: (context, state) {
        return SubScreenLayout(
          title: context.localizations.resetPassword,
          primaryButton: PrimaryButton(
            labelText: context.localizations.resetPassword,
            onPressed: () {
              _submitResetPasswordBloc.add(
                SubmitResetPasswordEvent.onResetButtonClick(
                  context,
                  widget.submitResetPasswordScreenArguments.otpValidatedToken,
                ),
              );
            },
          ),
          child: Column(
            children: [
              verticalGapEight,
              Text(
                context.localizations.resetPasswordSubtitle,
                style: context.appTextStyles.smallText1.copyWith(
                  color: context.appColors.neutralShadeDefaultColor,
                ),
              ),
              verticalGapForty,
              _getResetPasswordFields(),
              verticalGapForty,
              _getPasswordConditionsSection(
                widget.submitResetPasswordScreenArguments.passwordPolicy,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _getResetPasswordFields() {
    return Form(
      key: _submitResetPasswordBloc.formKey,
      autovalidateMode: _submitResetPasswordBloc.autoValidateMode,
      child: Column(
        children: [
          PasswordField(
            labelText: context.localizations.enterNewPassword,
            enableVisibility: true,
            onPasswordChanged: (newPasswordText) {
              _submitResetPasswordBloc.add(
                SubmitResetPasswordEvent.onNewPasswordChanged(newPasswordText),
              );
            },
          ),
          verticalGapSixteen,
          PasswordField(
            labelText: context.localizations.confirmNewPassword,
            enableVisibility: true,
            onPasswordChanged: (confirmPasswordText) {
              _submitResetPasswordBloc.add(
                SubmitResetPasswordEvent.onConfirmPasswordChanged(
                  confirmPasswordText,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _getPasswordConditionsSection(PasswordPolicy passwordPolicy) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          // This is part of sign-in-flow, We only use english in sign-in-flow.
          passwordPolicy.title.en,
          style: context.appTextStyles.smallText1Bold.copyWith(
            color: context.appColors.neutralShade1Color,
          ),
        ),
        verticalGapEight,
        ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          separatorBuilder: (context, index) => verticalGapEight,
          shrinkWrap: true,
          itemCount: passwordPolicy.description.length,
          itemBuilder: (BuildContext context, int index) {
            return _getPasswordCondition(
              // This is part of sign-in-flow, We only use english in sign-in-flow.
              passwordPolicy.description[index].en,
            );
          },
        ),
      ],
    );
  }

  Widget _getPasswordCondition(String conditionText) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        horizontalGapEight,
        Text(
          bulletPoint,
          style: context.appTextStyles.smallText2.copyWith(
            color: context.appColors.neutralShadeDefaultColor,
          ),
        ),
        horizontalGapEight,
        Expanded(
          child: Text(
            conditionText,
            style: context.appTextStyles.smallText2.copyWith(
              color: context.appColors.neutralShadeDefaultColor,
            ),
          ),
        ),
      ],
    );
  }
}
