import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_manager_home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/repository/sign_in_repository.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/repository/user_privileges_repository.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/submit_reset_password_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../models/submit_reset_password_screen_arguments.dart';

part 'sign_in_otp_bloc.freezed.dart';

class SignInOTPBloc extends Bloc<SignInOTPEvent, SignInOTPState>
    with RPCHandler {
  final SignInRepository _repository = SignInRepository();
  final UserPrivilegesRepository _userPrivilegesRepository =
      UserPrivilegesRepository();

  String? _otp;

  SignInOTPBloc() : super(const Initial()) {
    on<SignInOTPEvent>((event, emit) async {
      switch (event) {
        case AddOTP(:final otp):
          if (state is OTPLoading) return;
          _otp = otp;
        case ConfirmOTP(:final context, :final otpId):
          await _confirmOTP(context, emit, otpId);
        case ResendOTP(:final context, otpId: final recordId):
          if (state is OTPLoading) return;
          await _resendOTP(context, emit, recordId);
      }
    });
  }

  Future<void> _resendOTP(
    BuildContext context,
    Emitter<SignInOTPState> emit,
    LeoUUID otpId,
  ) async {
    await rpcHandler(
      () async {
        final result = await _repository.resendSignInOTP(otpId);
        if (context.mounted) {
          await _handleResendOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        emit(const SignInOTPState.transientError());
      },
    );
  }

  Future<void> _handleResendOTPResult(
    BuildContext context,
    Emitter<SignInOTPState> emit,
    LeoRPCResult<
      BackOfficeResendSignInOTPResponse,
      BackOfficeResendSignInOTPError
    >
    result,
  ) async {
    result.when(
      response: (response) {
        final OTPResendDetails otpResendDetails = OTPResendDetails(
          validityDetails: OTPValidityDetails(
            expiresAt: response.expiresAt,
            nextResendAt: response.nextResendAt,
          ),
          numberOfResendsLeft: response.numberOfResendsLeft,
        );
        emit(SignInOTPState.otpResentSuccessfully(otpResendDetails));
      },
      error: (error) {
        emit(const SignInOTPState.prominentError());
        error.when(
          // This can never happen as the otpId is received from the server.
          incorrectOtpId: (e) => throw DeveloperError(e.toString()),
          couldNotSendOtp: (_) => ProminentErrorHandler.couldNotSendOTP(),
          waitForResend: (_) => ProminentErrorHandler.waitForOTPResend(),
          boUserDisabled: (_) => ProminentErrorHandler.userDisabled(),
          signInTemporarilyBlocked:
              (_) => ProminentErrorHandler.signInTemporarilyBlocked(),
          inactiveState:
              (_) => ProminentErrorHandler.genericError(
                onOkay: navigateToLandingScreen,
              ),
          tooManyIncorrectAttempts:
              (_) => ProminentErrorHandler.tooManyRequests(),
        );
      },
    );
  }

  Future<void> _confirmOTP(
    BuildContext context,
    Emitter<SignInOTPState> emit,
    LeoUUID otpId,
  ) async {
    AgencyAppDialog.showSpinnerDialog(context);
    emit(const SignInOTPState.loading());
    await rpcHandler(
      () async {
        final result = await _repository.confirmSignInOTP(
          Otp(otp: _otp!),
          otpId,
        );
        if (context.mounted) {
          await _handleConfirmOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        emit(const SignInOTPState.transientError());
      },
    );
  }

  Future<void> _handleConfirmOTPResult(
    BuildContext context,
    Emitter<SignInOTPState> emit,
    LeoRPCResult<
      BackOfficeConfirmSignInOTPResponse,
      BackOfficeConfirmSignInOTPError
    >
    result,
  ) async {
    await result.when(
      response: (response) async {
        response.result.when(
          loggedInMobile: (result) async {
            await AuthProvider.instance.setCredentials(
              slt: result.slt,
              llt: result.llt,
              userType: UserType.agentManager,
            );
            final rpcResult =
                await _userPrivilegesRepository.getCurrentUserPrivileges();
            rpcResult.when(
              response: (response) async {
                final bool isUserAgentManager = Set.of(
                  response.privileges,
                ).containsAll(agentManagerPrivileges);
                if (!isUserAgentManager) {
                  // If BO User does not have Agent Manager privileges,
                  // sign out the user.
                  await forceSignOutUser(context, showSpinnerDialog: false);
                  ProminentErrorHandler.userNotAgentManager();
                } else {
                  context.rootNavigator.pop();
                  context.navigator.pushNamedAndRemoveUntil(
                    AgentManagerHomeScreen.id,
                    ModalRoute.withName('/'),
                  );
                }
              },
              error: (error) {
                // As the error type for this RPC is `Never`, this can never
                // be encountered unless a developer error occurs.
                throw DeveloperError("Something went wrong $error");
              },
            );
          },
          loggedInWeb: (_) {
            // This can never happen as the platform cannot be web.
            throw DeveloperError("Something went wrong. Platform is not web");
          },
          forceResetPassword: (forceResetPassword) {
            context.navigator.pushNamed(
              SubmitResetPasswordScreen.id,
              arguments: SubmitResetPasswordScreenArguments(
                otpValidatedToken: forceResetPassword.otpValidatedToken,
                passwordPolicy: forceResetPassword.passwordPolicy,
              ),
            );
          },
        );
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const SignInOTPState.prominentError());
        error.when(
          // This can never happen as the otpId is received from the server.
          incorrectOtpId: (e) => throw DeveloperError(e.toString()),
          otpExpired: (_) => ProminentErrorHandler.otpExpired(),
          incorrectOtp:
              (error) => ProminentErrorHandler.incorrectOTP(
                numberOfValidationsAttemptsLeft:
                    error.numberOfValidationAttemptsLeft,
                featureName: context.localizations.signIn,
                onOkay: navigateToLandingScreen,
              ),
          tooManyOtpAttempts:
              (_) => ProminentErrorHandler.tooManyOTPRequests(
                onOkay: navigateToLandingScreen,
              ),
          signInTemporarilyBlocked:
              (_) => ProminentErrorHandler.signInTemporarilyBlocked(),
          inactiveState:
              (_) => ProminentErrorHandler.genericError(
                onOkay: navigateToLandingScreen,
              ),
          boUserDisabled: (_) => ProminentErrorHandler.userDisabled(),
        );
      },
    );
  }
}

@freezed
sealed class SignInOTPState with _$SignInOTPState {
  const factory SignInOTPState.initial() = Initial;

  const factory SignInOTPState.loading() = OTPLoading;

  const factory SignInOTPState.invalidOTP() = InvalidOTP;

  const factory SignInOTPState.prominentError() = ProminentError;

  const factory SignInOTPState.transientError() = TransientError;

  const factory SignInOTPState.otpResentSuccessfully(
    OTPResendDetails resendDetails,
  ) = OTPResentSuccessfully;

  const factory SignInOTPState.otpConfirmed() = OTPConfirmed;
}

@freezed
sealed class SignInOTPEvent with _$SignInOTPEvent {
  const factory SignInOTPEvent.addOtp(String otp) = AddOTP;

  const factory SignInOTPEvent.confirmOTP(BuildContext context, LeoUUID otpId) =
      ConfirmOTP;

  const factory SignInOTPEvent.resendOTP(BuildContext context, LeoUUID otpId) =
      ResendOTP;
}
