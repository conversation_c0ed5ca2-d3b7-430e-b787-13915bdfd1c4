import 'package:bcn_agency_banking_flutter/src/core/auth/auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_manager_home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/repository/submit_reset_password_repository.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/repository/user_privileges_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

part 'submit_reset_password_bloc.freezed.dart';

class SubmitResetPasswordBloc
    extends Bloc<SubmitResetPasswordEvent, SubmitResetPasswordState>
    with RPCHandler {
  final SubmitResetPasswordRepository _resetPasswordRepository =
      SubmitResetPasswordRepository();
  final UserPrivilegesRepository _userPrivilegesRepository =
      UserPrivilegesRepository();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  AutovalidateMode autoValidateMode = AutovalidateMode.disabled;

  String? _newPassword;
  String? _confirmPassword;

  SubmitResetPasswordBloc() : super(const Initial()) {
    on<SubmitResetPasswordEvent>((event, emit) async {
      switch (event) {
        case OnNewPasswordChanged(:final newPassword):
          _newPassword = newPassword;
        case OnConfirmPasswordChanged(:final confirmPassword):
          _confirmPassword = confirmPassword;
        case OnResetButtonClick(:final context, :final otpValidatedToken):
          if (!(formKey.currentState?.validate() ?? false)) {
            autoValidateMode = AutovalidateMode.onUserInteraction;
            return;
          }
          await _attemptResetPassword(context, otpValidatedToken, emit);
      }
    });
  }

  Future<void> _attemptResetPassword(
    BuildContext context,
    LeoUUID otpValidatedToken,
    Emitter<SubmitResetPasswordState> emit,
  ) async {
    await rpcHandler(
      () async {
        final checkPasswordMatch = _validatePasswords();
        emit(const SubmitResetPasswordState.loading());
        if (checkPasswordMatch) {
          AgencyAppDialog.showSpinnerDialog(context);
          final rpcResult = await _resetPasswordRepository
              .submitResetPasswordRequest(otpValidatedToken, _newPassword!);
          rpcResult.when(
            response: (response) {
              response.result.when(
                loggedInMobile: (loggedInMobile) async {
                  await AuthProvider.instance.setCredentials(
                    slt: loggedInMobile.slt,
                    llt: loggedInMobile.llt,
                    userType: UserType.agentManager,
                  );
                  final rpcResult =
                      await _userPrivilegesRepository
                          .getCurrentUserPrivileges();
                  rpcResult.when(
                    response: (response) async {
                      final bool isUserAgentManager = Set.of(
                        response.privileges,
                      ).containsAll(agentManagerPrivileges);
                      if (!isUserAgentManager) {
                        // If BO User does not have Agent Manager privileges,
                        // sign out the user.
                        await forceSignOutUser(
                          context,
                          showSpinnerDialog: false,
                        );
                        ProminentErrorHandler.userNotAgentManager();
                      } else {
                        context.rootNavigator.pop();
                        context.navigator.pushNamedAndRemoveUntil(
                          AgentManagerHomeScreen.id,
                          ModalRoute.withName('/'),
                        );
                      }
                    },
                    error: (error) {
                      // As the error type for this RPC is `Never`, this can never
                      // be encountered unless a developer error occurs.
                      throw DeveloperError("Something went wrong $error");
                    },
                  );
                },
                loggedInWeb: (_) {
                  // This can never happen as the platform cannot be web.
                  throw DeveloperError(
                    'Something went wrong. Platform cannot be web.',
                  );
                },
              );
            },
            error: (error) {
              context.rootNavigator.pop();
              emit(const SubmitResetPasswordState.prominentError());
              error.when(
                inactiveState: (_) => _showGenericError(),
                otpValidatedTokenExpired: (_) => _showGenericError(),
                invalidOtpValidatedToken:
                    (error) => {
                      // There will be no case of an invalid otp validated token
                      // Seeing this indicates that this is a developer error.
                      throw DeveloperError(
                        'Something went wrong : ${error.toString()}',
                      ),
                    },
                insecurePassword:
                    (_) => _showErrorDialog(
                      context,
                      context.localizations.insecurePasswordText,
                      context.localizations.okay,
                    ),
                reusedPassword:
                    (_) => _showErrorDialog(
                      context,
                      context.localizations.reusedPasswordText,
                      context.localizations.okay,
                    ),
                boUserDisabled: (_) => ProminentErrorHandler.userDisabled(),
              );
            },
          );
        } else {
          _showErrorDialog(
            context,
            context.localizations.passwordUnmatchCondition,
            context.localizations.tryAgain.toUpperCase(),
          );
        }
      },
      onTransientError: (String errorMessage) {
        context.rootNavigator.pop();
        emit(const SubmitResetPasswordState.transientError());
      },
    );
  }

  void _showGenericError() =>
      ProminentErrorHandler.genericError(onOkay: navigateToLandingScreen);

  bool _validatePasswords() => _newPassword!.trim() == _confirmPassword!.trim();

  void _showErrorDialog(
    BuildContext context,
    String contentText,
    String buttonText,
  ) {
    if (!context.mounted) return;
    AgencyAppDialog.showAppDialog(
      context: context,
      contentText: contentText,
      actions:
          (context) => [
            PrimaryTextButton(
              text: buttonText,
              onTap: () => context.navigator.pop(),
            ),
          ],
    );
  }
}

@freezed
sealed class SubmitResetPasswordEvent with _$SubmitResetPasswordEvent {
  const factory SubmitResetPasswordEvent.onNewPasswordChanged(
    String newPassword,
  ) = OnNewPasswordChanged;

  const factory SubmitResetPasswordEvent.onConfirmPasswordChanged(
    String confirmPassword,
  ) = OnConfirmPasswordChanged;

  const factory SubmitResetPasswordEvent.onResetButtonClick(
    BuildContext context,
    LeoUUID otpValidatedToken,
  ) = OnResetButtonClick;
}

@freezed
sealed class SubmitResetPasswordState with _$SubmitResetPasswordState {
  const factory SubmitResetPasswordState.initial() = Initial;

  const factory SubmitResetPasswordState.loading() = Loading;

  const factory SubmitResetPasswordState.transientError() = TransientError;

  const factory SubmitResetPasswordState.prominentError() = ProminentError;
}
