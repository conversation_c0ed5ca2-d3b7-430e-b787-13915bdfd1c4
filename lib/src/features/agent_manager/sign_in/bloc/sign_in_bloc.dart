import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/route_generator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/agent_manager_auth_code_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/agent_manager_otp_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/bloc/sign_in_otp_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/repository/sign_in_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../core/logger.dart';
import '../../../../utils/constants.dart';

part 'sign_in_bloc.freezed.dart';

class SignInBloc extends Bloc<SignInEvent, SignInState> with RPCHandler {
  final SignInRepository _repository = SignInRepository();
  String? _phoneNumber;
  String? _password;
  LeoUUID? _passwordValidatedToken;
  String? _authCodeRHS;
  LeoUUID? _otpId;
  OTPValidityDetails? _otpValidityDetails;

  SignInBloc() : super(const Initial()) {
    on<SignInEvent>((event, emit) async {
      switch (event) {
        case OnPhoneNumberChange(:final phoneNumber):
          _phoneNumber = phoneNumber;
        case OnPasswordChange(:final password):
          _password = password;
        case OnAuthCodeEntered(:final authCode):
          _authCodeRHS = authCode.trim();
        case SignInUser(:final context):
          emit(const SignInState.loading());
          await _signInUser(context, emit);
        case ConfirmAuthCode(:final context):
          emit(const SignInState.loading());
          await _confirmAuthCode(context, emit);
      }
    });
  }

  Future<void> _showInvalidCredentialsError(BuildContext context) async {
    await AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.invalidCredentials,
      buttonText: context.localizations.tryAgain,
    );
  }

  Future<void> _signInUser(BuildContext context, Emitter emit) async {
    l.d("Doing AM Sign In. phoneNumber: $phoneNumber, password: $_password");
    AgencyAppDialog.showSpinnerDialog(context);
    final NavigatorState rootNavigator = context.rootNavigator;
    try {
      final LeoPhoneNumber phoneNumber = LeoPhoneNumber(_phoneNumber!);
      await rpcHandler(
        () async {
          final rpcResult = await _repository.signInAgentManager(
            phoneNumber,
            _password!,
          );
          l.d("Sign IN AM RPC result: $rpcResult");
          if (context.mounted) _handleSignInResponse(context, rpcResult, emit);
        },
        onTransientError: (_) {
          l.d("Emitting Transient Error");
          context.rootNavigator.pop();
          emit(const SignInState.transientError());
        },
      );
    } catch (e) {
      rootNavigator.pop();
      emit(const SignInState.prominentError());
      if (context.mounted) {
        _showInvalidCredentialsError(context);
      }
    }
  }

  void _handleSignInResponse(
    BuildContext context,
    LeoRPCResult<SignInBOUserResponse, SignInBOUserError> rpcResult,
    Emitter emit,
  ) {
    rpcResult.when(
      response: (response) {
        _passwordValidatedToken = response.passwordValidatedToken;
        context.rootNavigator.pop();
        context.navigator.push(
          Routes.getMaterialRoute(
            AgentManagerAuthCodeScreen.id,
            BlocProvider.value(
              value: this,
              child: AgentManagerAuthCodeScreen(
                authCodeLHS: response.authCodeLHS,
              ),
            ),
          ),
        );
        emit(const SignInState.initial());
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const SignInState.prominentError());
        error.when(
          invalidCredentials: (_) => _showInvalidCredentialsError(context),
          boUserDisabled: (_) => ProminentErrorHandler.userDisabled(),
          signInTemporarilyBlocked:
              (_) => ProminentErrorHandler.signInTemporarilyBlocked(),
        );
      },
    );
  }

  Future<void> _confirmAuthCode(BuildContext context, Emitter emit) async {
    l.d(
      "Confirming Auth Code. "
      "authCodeRHS: $_authCodeRHS"
      "passwordValidatedToken: $_passwordValidatedToken",
    );
    await rpcHandler(
      () async {
        if (_authCodeRHS == null || _authCodeRHS!.length < authCodeLength) {
          _showInvalidAuthCodeErrorDialog(context);
          return;
        }
        if (_passwordValidatedToken == null) {
          // This will never happen as the RPC call for sign in always returns
          // a passwordValidatedToken when user is signed in.
          l.e("passwordValidatedToken is null");
          throw DeveloperError("User credentials not validated");
        }
        AgencyAppDialog.showSpinnerDialog(context);
        final rpcResult = await _repository.confirmAuthCode(
          _authCodeRHS!,
          _passwordValidatedToken!,
        );
        l.d("Confirm AuthCode RHS: $rpcResult");
        if (context.mounted) {
          _handleConfirmAuthCodeResponse(context, emit, rpcResult);
        }
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        l.d("Emitting Transient Error");
        emit(const SignInState.transientError());
      },
    );
  }

  void _handleConfirmAuthCodeResponse(
    BuildContext context,
    Emitter emit,
    LeoRPCResult<ConfirmAuthCodeResponse, ConfirmAuthCodeError> rpcResult,
  ) {
    rpcResult.when(
      response: (response) {
        _otpId = response.otpId;
        _otpValidityDetails = OTPValidityDetails(
          nextResendAt: response.nextResendAt,
          expiresAt: response.expiresAt,
        );
        context.rootNavigator.pop();
        context.navigator.push(
          Routes.getMaterialRoute(
            AgentManagerOtpScreen.id,
            BlocProvider.value(
              value: this,
              child: BlocProvider(
                create: (context) => SignInOTPBloc(),
                child: const AgentManagerOtpScreen(),
              ),
            ),
          ),
        );
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const SignInState.prominentError());
        error.when(
          passwordValidatedTokenExpired:
              (_) => ProminentErrorHandler.genericError(
                onOkay: navigateToLandingScreen,
              ),
          invalidPasswordValidatedToken:
              (_) => ProminentErrorHandler.genericError(
                onOkay: navigateToLandingScreen,
              ),
          inactiveState:
              (_) => ProminentErrorHandler.genericError(
                onOkay: navigateToLandingScreen,
              ),
          invalidAuthCode: (_) => _showInvalidAuthCodeErrorDialog(context),
          couldNotSendOtp: (_) => ProminentErrorHandler.couldNotSendOTP(),
          boUserDisabled: (_) => ProminentErrorHandler.userDisabled(),
          signInTemporarilyBlocked:
              (_) => ProminentErrorHandler.signInTemporarilyBlocked(),
          tooManyIncorrectAuthCodeAttempts:
              (_) => ProminentErrorHandler.tooManyIncorrectAuthCodeAttempts(),
          tooManyRequests: (_) => ProminentErrorHandler.tooManyRequests(),
        );
      },
    );
  }

  void _showInvalidAuthCodeErrorDialog(BuildContext context) {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.invalidAuthCode,
      buttonText: context.localizations.tryAgain,
    );
  }

  String? get phoneNumber => _phoneNumber;

  LeoUUID? get otpId => _otpId;

  OTPValidityDetails? get otpValidityDetails => _otpValidityDetails;
}

@freezed
sealed class SignInState with _$SignInState {
  const factory SignInState.initial() = Initial;

  const factory SignInState.loading() = Loading;

  const factory SignInState.prominentError() = ProminentError;

  const factory SignInState.transientError() = TransientError;
}

@freezed
sealed class SignInEvent with _$SignInEvent {
  const factory SignInEvent.signInUser(BuildContext context) = SignInUser;

  const factory SignInEvent.confirmAuthCode(BuildContext context) =
      ConfirmAuthCode;

  const factory SignInEvent.onPasswordChange(String password) =
      OnPasswordChange;

  const factory SignInEvent.onPhoneNumberChange(String? phoneNumber) =
      OnPhoneNumberChange;

  const factory SignInEvent.onAuthCodeEntered(String authCode) =
      OnAuthCodeEntered;
}
