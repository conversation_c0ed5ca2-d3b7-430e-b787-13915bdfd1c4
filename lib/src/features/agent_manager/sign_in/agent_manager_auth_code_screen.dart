import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/bloc/sign_in_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/widgets/auth_code_input_field.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sub_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AgentManagerAuthCodeScreen extends StatefulWidget {
  static const id = "/auth_code_screen";

  final String authCodeLHS;

  const AgentManagerAuthCodeScreen({super.key, required this.authCodeLHS});

  @override
  State<AgentManagerAuthCodeScreen> createState() =>
      _AgentManagerAuthCodeScreenState();
}

class _AgentManagerAuthCodeScreenState
    extends State<AgentManagerAuthCodeScreen> {
  late final SignInBloc _signInBloc = BlocProvider.of<SignInBloc>(context);
  final TextEditingController _authPinController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _authPinController.text = widget.authCodeLHS;
  }

  @override
  Widget build(BuildContext context) {
    return SubScreenLayout(
      title: context.localizations.authenticateAccount,
      padding: commonScreenPadding.copyWith(left: dimenZero, right: dimenZero),
      primaryButton: BlocBuilder<SignInBloc, SignInState>(
        builder: (context, state) {
          return PrimaryButton(
            labelText: context.localizations.verify,
            onPressed: () {
              _signInBloc.add(SignInEvent.confirmAuthCode(context));
            },
          );
        },
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          verticalGapEight,
          Padding(
            padding: horizontalPaddingSixteen,
            child: Text(
              context.localizations.authenticateAccountMessage,
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShadeDefaultColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          verticalGapForty,
          Center(
            child: AuthCodeInputField(
              authCodeLHSController: _authPinController,
              onAuthCodeEntered: (String authCode) {
                _signInBloc.add(SignInEvent.onAuthCodeEntered(authCode));
              },
            ),
          ),
        ],
      ),
    );
  }
}
