import 'package:bcn_agency_banking_flutter/src/helpers/reg_exp_helper.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pinput/pinput.dart';

class AuthCodeInputField extends StatelessWidget {
  final void Function(String) onAuthCodeEntered;
  final TextEditingController authCodeLHSController;

  const AuthCodeInputField({
    Key? key,
    required this.onAuthCodeEntered,
    required this.authCodeLHSController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _getLHSAuthCode(context),
        horizontalGapSixteen,
        _getRHSAuthCode(context),
      ],
    );
  }

  PinTheme _getDefaultPinTheme(BuildContext context) {
    return PinTheme(
      textStyle: context.appTextStyles.welcomeHeading.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
      constraints: BoxConstraints.tight(const Size(_fieldWidth, _fieldHeight)),
    );
  }

  PinTheme _getSelectedPinTheme(BuildContext context) {
    return PinTheme(
      textStyle: context.appTextStyles.welcomeHeading.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
      constraints: BoxConstraints.tight(const Size(_fieldWidth, _fieldHeight)),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: _fieldBorderWidth,
            color: context.appColors.primaryLightColor,
          ),
        ),
      ),
    );
  }

  Widget _getLHSAuthCode(BuildContext context) {
    return Pinput(
      enabled: false,
      defaultPinTheme: _getDefaultPinTheme(context),
      length: authCodeLength,
      controller: authCodeLHSController,
      separatorBuilder: (_) => horizontalGapSixteen,
    );
  }

  Widget _getRHSAuthCode(BuildContext context) {
    return Pinput(
      enabled: true,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExpHelper.numberPattern),
      ],
      separatorBuilder: (_) => horizontalGapSixteen,
      defaultPinTheme: _getSelectedPinTheme(context),
      showCursor: true,
      length: authCodeLength,
      onChanged: onAuthCodeEntered,
    );
  }
}

const double _fieldWidth = dimenTwenty;
const double _fieldHeight = dimenForty;
const double _fieldBorderWidth = dimenTwo;
