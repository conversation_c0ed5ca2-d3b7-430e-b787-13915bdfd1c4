import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/bloc/sign_in_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/password_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_number_text_field/phone_number_field_with_icon.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sub_screen_layout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../widgets/bcn_logo.dart';

class AgentManagerPhoneNumberPasswordScreen extends StatefulWidget {
  static const id = "/phone-number-password-screen";

  const AgentManagerPhoneNumberPasswordScreen({super.key});

  @override
  State<AgentManagerPhoneNumberPasswordScreen> createState() =>
      _AgentManagerPhoneNumberPasswordScreenState();
}

class _AgentManagerPhoneNumberPasswordScreenState
    extends State<AgentManagerPhoneNumberPasswordScreen> {
  late final SignInBloc _signInBloc = BlocProvider.of<SignInBloc>(context);
  final GlobalKey<FormState> _agentManagerCredentialsFormKey =
      GlobalKey<FormState>();
  AutovalidateMode _shouldValidateAgentCredentials = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return SubScreenLayout(
      primaryButton: BlocBuilder<SignInBloc, SignInState>(
        builder: (context, state) {
          return PrimaryButton(
            labelText: context.localizations.next,
            onPressed: () {
              final isFormValidated =
                  _agentManagerCredentialsFormKey.currentState?.validate() ??
                  false;
              if (!isFormValidated) {
                setState(() {
                  _shouldValidateAgentCredentials =
                      AutovalidateMode.onUserInteraction;
                });
                return;
              }
              _signInBloc.add(SignInEvent.signInUser(context));
            },
          );
        },
      ),
      child: Form(
        key: _agentManagerCredentialsFormKey,
        autovalidateMode: _shouldValidateAgentCredentials,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalGapEight,
            const BCNLogo(),
            verticalGapTwentyFour,
            Text(
              context.localizations.signInAsAgentManager,
              style: context.appTextStyles.titleBold.copyWith(
                color: context.appColors.titleColor,
              ),
            ),
            verticalGapFour,
            Text(
              context.localizations.signInWithMobileNumber,
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShadeDefaultColor,
              ),
            ),
            verticalGapForty,
            PhoneNumberFieldWithIcon(
              onPhoneNumberValidated: (phoneNumber) {
                _signInBloc.add(SignInEvent.onPhoneNumberChange(phoneNumber));
              },
            ),
            verticalGapTwenty,
            PasswordField(
              enableVisibility: true,
              onPasswordChanged: (password) {
                _signInBloc.add(SignInEvent.onPasswordChange(password.trim()));
              },
            ),
          ],
        ),
      ),
    );
  }
}
