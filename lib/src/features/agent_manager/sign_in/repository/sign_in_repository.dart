import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/mock_impls/mock_confirm_auth_code_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/mock_impls/mock_confirm_sign_in_otp_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/mock_impls/mock_resend_sign_in_otp_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/mock_impls/mock_sign_in_bo_user_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class SignInRepository {
  Future<LeoRPCResult<SignInBOUserResponse, SignInBOUserError>>
  signInAgentManager(LeoPhoneNumber phoneNumber, String password) async {
    final SignInBOUserRPC signInBOUserRPCImpl =
        currentFlavor.isMock
            ? MockSignInBOUserRPCImpl()
            : SignInBOUserRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final SignInBOUserRequest signInBOUserRequest = SignInBOUserRequest(
      phoneNumber: phoneNumber,
      password: password,
    );
    final LeoRPCResult<SignInBOUserResponse, SignInBOUserError>
    signInBOUserResponse = await signInBOUserRPCImpl.execute(
      signInBOUserRequest,
    );
    return signInBOUserResponse;
  }

  Future<LeoRPCResult<ConfirmAuthCodeResponse, ConfirmAuthCodeError>>
  confirmAuthCode(String authCodeRHS, LeoUUID passwordValidatedToken) async {
    final ConfirmAuthCodeRPC confirmAuthCodeRPCImpl =
        currentFlavor.isMock
            ? MockConfirmAuthCodeRPCImpl()
            : ConfirmAuthCodeRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final ConfirmAuthCodeRequest confirmAuthCodeRequest =
        ConfirmAuthCodeRequest(
          authCodeRHS: authCodeRHS,
          passwordValidatedToken: passwordValidatedToken,
        );
    final LeoRPCResult<ConfirmAuthCodeResponse, ConfirmAuthCodeError>
    confirmAuthCodeResponse = await confirmAuthCodeRPCImpl.execute(
      confirmAuthCodeRequest,
    );
    return confirmAuthCodeResponse;
  }

  Future<
    LeoRPCResult<
      BackOfficeConfirmSignInOTPResponse,
      BackOfficeConfirmSignInOTPError
    >
  >
  confirmSignInOTP(Otp otp, LeoUUID otpId) async {
    final BackOfficeConfirmSignInOTPRPC confirmSignInOTPRPCImpl =
        currentFlavor.isMock
            ? MockConfirmSignInOTPRPCImpl()
            : BackOfficeConfirmSignInOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final BackOfficeConfirmSignInOTPRequest confirmSignInOTPRequest =
        BackOfficeConfirmSignInOTPRequest(
          otp: otp,
          otpId: otpId,
          boPlatform: BOPlatformEnum.MOBILE,
        );
    final LeoRPCResult<
      BackOfficeConfirmSignInOTPResponse,
      BackOfficeConfirmSignInOTPError
    >
    confirmSignInOTPResponse = await confirmSignInOTPRPCImpl.execute(
      confirmSignInOTPRequest,
    );
    return confirmSignInOTPResponse;
  }

  Future<
    LeoRPCResult<
      BackOfficeResendSignInOTPResponse,
      BackOfficeResendSignInOTPError
    >
  >
  resendSignInOTP(LeoUUID otpId) async {
    final BackOfficeResendSignInOTPRPC resendSignInOTPRPCImpl =
        currentFlavor.isMock
            ? MockResendSignInOTPRPCImpl()
            : BackOfficeResendSignInOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final BackOfficeResendSignInOTPRequest resendSignInOTPRequest =
        BackOfficeResendSignInOTPRequest(otpId: otpId);
    final LeoRPCResult<
      BackOfficeResendSignInOTPResponse,
      BackOfficeResendSignInOTPError
    >
    resendSignInOTPResponse = await resendSignInOTPRPCImpl.execute(
      resendSignInOTPRequest,
    );
    return resendSignInOTPResponse;
  }
}
