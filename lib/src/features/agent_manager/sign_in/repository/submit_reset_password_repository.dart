import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/mock_impls/mock_submit_reset_password_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class SubmitResetPasswordRepository {
  Future<LeoRPCResult<SubmitResetPasswordResponse, SubmitResetPasswordError>>
  submitResetPasswordRequest(
    LeoUUID otpValidatedToken,
    String newPassword,
  ) async {
    final SubmitResetPasswordRPC submitResetPasswordRPCImpl =
        currentFlavor.isMock
            ? MockSubmitResetPasswordRPCImpl()
            : SubmitResetPasswordRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );

    final SubmitResetPasswordRequest submitResetPasswordRequest =
        SubmitResetPasswordRequest(
          otpValidatedToken: otpValidatedToken,
          newPassword: newPassword,
          boPlatform: BOPlatformEnum.MOBILE,
        );

    return await submitResetPasswordRPCImpl.execute(submitResetPasswordRequest);
  }
}
