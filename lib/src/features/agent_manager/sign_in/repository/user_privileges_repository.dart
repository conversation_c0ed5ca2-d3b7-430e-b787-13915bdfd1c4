import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/mock_impls/mock_get_user_privileges_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class UserPrivilegesRepository {
  Future<LeoRPCResult<GetUserPrivilegesResponse, Never>>
  getCurrentUserPrivileges() async {
    final GetUserPrivilegesRPC getUserPrivilegesRPCImpl =
        currentFlavor.isMock
            ? MockGetUserPrivilegesRPCImpl()
            : GetUserPrivilegesRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final GetUserPrivilegesRequest request = GetUserPrivilegesRequest();
    final LeoRPCResult<GetUserPrivilegesResponse, Never> response =
        await getUserPrivilegesRPCImpl.execute(request);
    return response;
  }
}
