import 'package:bcn_agency_banking_flutter/src/core/auth/app_pin_provider.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/agent_application_requests_list_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/blocked_refund_requests_list_screen.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/list_tile_avatar_card.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sign_out_button.dart';
import 'package:flutter/material.dart';

import '../session_pin/setup_session_pin/setup_session_pin_screen.dart';
import 'refund_amount/refund_amount_request_screen.dart';

class AgentManagerHomeScreen extends StatefulWidget {
  static const id = "/agent-manager-home-screen";

  const AgentManagerHomeScreen({super.key});

  @override
  State<AgentManagerHomeScreen> createState() => _AgentManagerHomeScreenState();
}

class _AgentManagerHomeScreenState extends State<AgentManagerHomeScreen> {
  @override
  void initState() {
    super.initState();
    _checkIfAppPinSet();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(context.localizations.home),
            SignOutButton(
              buttonForegroundColor: context.appColors.genericWhiteColor,
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: commonScreenPadding,
          child: Column(
            children: [
              ListTileAvatarCard(
                title: context.localizations.agentApplications,
                subtitle: context.localizations.approveAgentApplications,
                iconPath: ABAssets.userPlusIcon,
                onTap:
                    () => context.navigator.pushNamed(
                      AgentApplicationRequestsListScreen.id,
                    ),
              ),
              verticalGapSixteen,
              ListTileAvatarCard(
                title: context.localizations.refundAmount,
                subtitle: context.localizations.refundAmountToTheAgent,
                iconPath: ABAssets.refundAmountIcon,
                onTap:
                    () => context.navigator.pushNamed(
                      AMRefundAmountRequestScreen.id,
                    ),
              ),
              verticalGapSixteen,
              ListTileAvatarCard(
                title: context.localizations.unblockRefunds,
                subtitle: context.localizations.unblockTheRefundRequests,
                iconPath: ABAssets.exchangeAltCircleIcon,
                onTap:
                    () => context.navigator.pushNamed(
                      BlockedRefundRequestsListScreen.id,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _checkIfAppPinSet() async {
    final isAppPinSet = await AppPinProvider.isAppPinSetup();
    if (!isAppPinSet && mounted) {
      context.navigator.pushNamed(SetupSessionPinScreen.id);
    }
  }
}
