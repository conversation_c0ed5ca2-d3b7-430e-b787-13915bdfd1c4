import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockEvaluateAgentApplicationRPCImpl extends EvaluateAgentApplicationRPC {
  @override
  Future<
    LeoRPCResult<
      EvaluateAgentApplicationResponse,
      EvaluateAgentApplicationError
    >
  >
  execute(EvaluateAgentApplicationRequest request) {
    final EvaluateAgentApplicationResponse response =
        EvaluateAgentApplicationResponse();
    final EvaluateAgentApplicationError error =
        EvaluateAgentApplicationError.EvaluateAgentApplicationErrorApplicationAlreadyApprovedError(
          errorCode: "",
        );
    final result = getLeoRPCResult<
      EvaluateAgentApplicationResponse,
      EvaluateAgentApplicationError
    >(response: response, error: error, shouldThrowError: false);

    return Future.delayed(2.seconds, () => result);
  }
}
