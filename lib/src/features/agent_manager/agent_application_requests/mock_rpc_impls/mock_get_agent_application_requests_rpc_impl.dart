import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetAgentApplicationRequestsRPCImpl
    extends GetAgentApplicationRequestsRPC {
  @override
  Future<LeoRPCResult<GetAgentApplicationRequestsResponse, Never>> execute(
    GetAgentApplicationRequestsRequest request,
  ) {
    final response = LeoRPCResult<
      GetAgentApplicationRequestsResponse,
      Never
    >.response(
      GetAgentApplicationRequestsResponse(
        agentApplications: [
          AgentApplication(
            id: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
            userId: LeoUUID("6b030092-e058-48fb-81be-6d7d60cd6a0d"),
            userPersonalDetails: UserPersonalDetails(
              firstName: 'Azibo',
              lastName: 'Dulani',
              gender: GenderEnum.MALE,
              dateOfBirth: Leo<PERSON><PERSON>("1999-01-01"),
              nationalId: "DVHTRW01",
            ),
            phoneNumber: LeoPhoneNumber("+************"),
            profileImage: MultiResolutionBitmapImage(
              mdpi: RemoteBitmapImage(
                imageURL: Uri.parse("https://picsum.photos/250?image=9"),
                imageType: BitmapImageTypeEnum.PNG,
                width: 80,
                height: 80,
              ),
              xhdpi: RemoteBitmapImage(
                imageURL: Uri.parse("https://picsum.photos/250?image=9"),
                imageType: BitmapImageTypeEnum.PNG,
                width: 80,
                height: 80,
              ),
              xxhdpi: RemoteBitmapImage(
                imageURL: Uri.parse("https://picsum.photos/250?image=9"),
                imageType: BitmapImageTypeEnum.PNG,
                width: 80,
                height: 80,
              ),
              xxxhdpi: RemoteBitmapImage(
                imageURL: Uri.parse("https://picsum.photos/250?image=9"),
                imageType: BitmapImageTypeEnum.PNG,
                width: 80,
                height: 80,
              ),
            ),
            shopName: "Dulani Shop",
            shopCoordinate: Coordinate(
              latitude: 12.92551,
              longitude: 77.5622317,
            ),
            locationRadius: 50,
            submittedAt: DateTime(2022, 12, 01),
            shopImageId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0f"),
            signatureImageId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0f"),
          ),
          AgentApplication(
            id: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
            userId: LeoUUID("6b030092-e058-48fb-81be-6d7d60cd6a0d"),
            userPersonalDetails: UserPersonalDetails(
              firstName: 'Willamson',
              lastName: 'Cameron James',
              gender: GenderEnum.MALE,
              dateOfBirth: LeoDate("1999-01-01"),
              nationalId: "DVHTRW01",
            ),
            phoneNumber: LeoPhoneNumber("+************"),
            profileImage: MultiResolutionBitmapImage(
              mdpi: RemoteBitmapImage(
                imageURL: Uri.parse("https://picsum.photos/250?image=9"),
                imageType: BitmapImageTypeEnum.PNG,
                width: 80,
                height: 80,
              ),
              xhdpi: RemoteBitmapImage(
                imageURL: Uri.parse("https://picsum.photos/250?image=9"),
                imageType: BitmapImageTypeEnum.PNG,
                width: 80,
                height: 80,
              ),
              xxhdpi: RemoteBitmapImage(
                imageURL: Uri.parse("https://picsum.photos/250?image=9"),
                imageType: BitmapImageTypeEnum.PNG,
                width: 80,
                height: 80,
              ),
              xxxhdpi: RemoteBitmapImage(
                imageURL: Uri.parse("https://picsum.photos/250?image=9"),
                imageType: BitmapImageTypeEnum.PNG,
                width: 80,
                height: 80,
              ),
            ),
            shopName: "Shop N Save",
            shopCoordinate: Coordinate(latitude: 12.9716, longitude: 77.5946),
            locationRadius: 5,
            submittedAt: DateTime(2022, 12, 01),
          ),
        ],
      ),
    );

    return Future.delayed(2.seconds, () => response);
  }
}
