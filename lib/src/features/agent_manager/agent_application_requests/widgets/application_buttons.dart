import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/bloc/evaluate_agent_application_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/bottomsheets.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_outlined_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:flutter/cupertino.dart';

class ApplicationButtons extends StatelessWidget {
  final String? primaryButtonText;
  final void Function(BuildContext context) onPrimaryButtonPressed;
  final EvaluateAgentApplicationBloc evaluateBloc;

  const ApplicationButtons({
    super.key,
    this.primaryButtonText,
    required this.onPrimaryButtonPressed,
    required this.evaluateBloc,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: PrimaryOutlinedButton(
            onPressed: () async {
              final ABComment? rejectionComment =
                  await BottomSheets.showCommentBottomSheet(
                    context: context,
                    commentReason: context.localizations.confirmRejectRequest,
                    ctaButtonText: context.localizations.reject,
                  );
              if (rejectionComment != null && context.mounted) {
                evaluateBloc.add(
                  EvaluateAgentApplicationEvent.evaluateApplication(
                    context,
                    EvaluateAgentApplicationRequestReviewStatusEnum.REJECTED(),
                    rejectionComment,
                  ),
                );
              }
            },
            labelText: context.localizations.reject,
          ),
        ),
        horizontalGapSixteen,
        Expanded(
          child: PrimaryButton(
            labelText: primaryButtonText ?? context.localizations.next,
            onPressed: () => onPrimaryButtonPressed(context),
          ),
        ),
      ],
    );
  }
}
