import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/bloc/edit_shop_details_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/location_picker_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/view_map_tile.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/reg_exp_helper.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/shop_details.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/validators.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/upload_photo_previewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../models/photo.dart';

class EditShopDetailsScreen extends StatefulWidget {
  static const id = "/edit-shop-details-screen";

  final ShopDetails shopDetails;

  const EditShopDetailsScreen({Key? key, required this.shopDetails})
    : super(key: key);

  @override
  EditShopDetailsScreenState createState() {
    return EditShopDetailsScreenState();
  }
}

class EditShopDetailsScreenState extends State<EditShopDetailsScreen> {
  late final EditShopDetailsBloc _editShopDetailsBloc =
      BlocProvider.of<EditShopDetailsBloc>(context);
  final GlobalKey<FormState> _editShopDetailsFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateEditShopDetails = AutovalidateMode.disabled;
  final LocationService _locationService = locator<LocationService>();

  @override
  void initState() {
    _editShopDetailsBloc.add(
      EditShopDetailsEvent.onShopLocationUpdated(
        LocationDetails(
          latitude: widget.shopDetails.latitude,
          longitude: widget.shopDetails.longitude,
          address: widget.shopDetails.address,
        ),
      ),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }
        final NavigatorState navigator = Navigator.of(context);
        if (await _shouldPopScreen()) {
          navigator.pop();
          return;
        }
      },
      child: BlocBuilder<EditShopDetailsBloc, EditShopDetailsState>(
        buildWhen: (_, EditShopDetailsState newState) {
          return newState !=
              const EditShopDetailsState.locationUpdateCompleted();
        },
        builder: (context, state) {
          return CommonScreenLayout(
            padding: EdgeInsets.zero,
            appBarTitle: context.localizations.editShopDetails,
            ctaWidget: _getSubmitShopDetailsButton(),
            onAppBarLeadingIconTapped: () async {
              final shouldPopScreen = await _shouldPopScreen();
              if (shouldPopScreen && mounted) {
                this.context.navigator.pop();
              }
            },
            disableBackGesture: false,
            child: _getBody(),
          );
        },
      ),
    );
  }

  Widget _getBody() {
    return Column(
      children: [
        verticalGapEight,
        _getShopNameField(),
        _getLocationAddressTile(),
        UploadPhotoPreviewer(
          title: context.localizations.shopPhotoTitle,
          placeholderText: context.localizations.shopPhoto,
          initialServerPhoto:
              widget.shopDetails.isImageDeleted
                  ? null
                  : widget.shopDetails.initialServerPhoto,
          initialPhoto: widget.shopDetails.photo?.photo,
          onPhotoDeleted: () {
            _editShopDetailsBloc.add(
              const EditShopDetailsEvent.onShopPhotoUpdated(null),
            );
          },
          onPhotoSelected: (Photo photo) {
            _editShopDetailsBloc.add(
              EditShopDetailsEvent.onShopPhotoUpdated(photo),
            );
          },
        ),
        verticalGapTwentyFour,
      ],
    );
  }

  Widget _getShopNameField() {
    return Form(
      key: _editShopDetailsFormKey,
      autovalidateMode: _shouldValidateEditShopDetails,
      child: Column(
        children: [
          TitleWidget(title: context.localizations.basicDetails),
          Padding(
            padding: commonScreenPadding,
            child: PrimaryTextField(
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  RegExpHelper.alphaNumericPatternWithSpace,
                ),
              ],
              maxLength: shopNameMaxLength,
              validator:
                  (shopName) => Validators.emptyValidator(
                    context,
                    context.localizations.shopName,
                    shopName,
                  ),
              initialValue: widget.shopDetails.name,
              labelText: context.localizations.shopName,
              onChanged: (name) {
                _editShopDetailsBloc.add(
                  EditShopDetailsEvent.onShopNameUpdated(name),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String? _getLocationLabel() {
    final shopLocation = _editShopDetailsBloc.shopLocation;
    if (shopLocation == null) {
      return null;
    } else if (shopLocation.address == null) {
      return "${shopLocation.latitude}, ${shopLocation.longitude}";
    } else {
      return shopLocation.address!.addressLabel;
    }
  }

  String? _getLocationInfo() {
    final shopLocation = _editShopDetailsBloc.shopLocation;
    if (shopLocation == null) {
      return null;
    } else if (shopLocation.address == null) {
      return context.localizations.locationSelected;
    } else {
      return shopLocation.address!.addressText;
    }
  }

  Widget _getLocationAddressTile() {
    return ViewMapTile(
      labelText: _getLocationLabel(),
      infoText: _getLocationInfo(),
      labelStyle: context.appTextStyles.labelText2Bold,
      onTap: () async {
        final shopLocationDetails =
            await context.navigator.pushNamed(
                  LocationPickerScreen.id,
                  arguments: _editShopDetailsBloc.shopLocation,
                )
                as LocationDetails?;
        if (shopLocationDetails != null) {
          _editShopDetailsBloc.add(
            EditShopDetailsEvent.onShopLocationUpdated(shopLocationDetails),
          );
        }
      },
    );
  }

  Widget _getSubmitShopDetailsButton() {
    return PrimaryButton(
      labelText: context.localizations.saveChanges,
      onPressed: () {
        if (!_locationService.checkLocationWithinAcceptableRange(
          _editShopDetailsBloc.shopLocation!,
          widget.shopDetails.initialShopLocation,
        )) {
          AgencyAppDialog.showErrorDialog(
            context: context,
            contentText: context.localizations.updatedLocationOutOfRange(
              allowedDistanceChangeInMeters,
            ),
            buttonText: context.localizations.tryAgain,
          );
          return;
        }
        if (!(_editShopDetailsFormKey.currentState?.validate() ?? false)) {
          setState(() {
            _shouldValidateEditShopDetails = AutovalidateMode.onUserInteraction;
          });
          return;
        }
        context.navigator.pop(
          ShopDetails(
            name: _editShopDetailsBloc.shopName ?? widget.shopDetails.name,
            latitude: _editShopDetailsBloc.shopLocation!.latitude,
            longitude: _editShopDetailsBloc.shopLocation!.longitude,
            address: _editShopDetailsBloc.shopLocation!.address,
            initialServerPhoto:
                _editShopDetailsBloc.isShopPhotoDeleted ?? false
                    ? null
                    : widget.shopDetails.initialServerPhoto,
            photo:
                _editShopDetailsBloc.shopPhoto ??
                (_editShopDetailsBloc.isShopPhotoDeleted ?? false
                    ? null
                    : widget.shopDetails.photo),
            initialShopLocation: widget.shopDetails.initialShopLocation,
            isImageDeleted:
                _editShopDetailsBloc.isShopPhotoDeleted ??
                widget.shopDetails.isImageDeleted,
          ),
        );
      },
    );
  }

  Future<bool> _shouldPopScreen() async {
    if (_editShopDetailsBloc.isDetailsUpdated(widget.shopDetails)) {
      final bool? shouldDiscardChanges =
          await AgencyAppDialog.showDiscardChangesConfirmation(context);
      return shouldDiscardChanges == true;
    } else {
      return true;
    }
  }
}
