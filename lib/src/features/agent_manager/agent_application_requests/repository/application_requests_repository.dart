import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/mock_rpc_impls/mock_evaluate_agent_application_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/mock_rpc_impls/mock_get_agent_application_requests_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class ApplicationRequestsRepository {
  Future<LeoRPCResult<GetAgentApplicationRequestsResponse, Never>>
  getApplicationRequests() async {
    final GetAgentApplicationRequestsRPC getAgentApplicationRequestsRPCImpl =
        currentFlavor.isMock
            ? MockGetAgentApplicationRequestsRPCImpl()
            : GetAgentApplicationRequestsRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final GetAgentApplicationRequestsRequest
    getAgentApplicationRequestsRequest = GetAgentApplicationRequestsRequest();
    final LeoRPCResult<GetAgentApplicationRequestsResponse, Never>
    getAgentApplicationRequestsResponse =
        await getAgentApplicationRequestsRPCImpl.execute(
          getAgentApplicationRequestsRequest,
        );
    return getAgentApplicationRequestsResponse;
  }

  Future<
    LeoRPCResult<
      EvaluateAgentApplicationResponse,
      EvaluateAgentApplicationError
    >
  >
  evaluateAgentApplication(
    LeoUUID applicationId,
    EvaluateAgentApplicationRequestReviewStatusEnum reviewStatus,
    ABComment comment,
  ) async {
    final EvaluateAgentApplicationRPC evaluateAgentApplicationRPCImpl =
        currentFlavor.isMock
            ? MockEvaluateAgentApplicationRPCImpl()
            : EvaluateAgentApplicationRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final EvaluateAgentApplicationRequest evaluateAgentApplicationRequest =
        EvaluateAgentApplicationRequest(
          id: applicationId,
          reviewStatus: reviewStatus,
          comment: comment,
        );
    final LeoRPCResult<
      EvaluateAgentApplicationResponse,
      EvaluateAgentApplicationError
    >
    evaluateAgentApplicationResponse = await evaluateAgentApplicationRPCImpl
        .execute(evaluateAgentApplicationRequest);
    return evaluateAgentApplicationResponse;
  }
}
