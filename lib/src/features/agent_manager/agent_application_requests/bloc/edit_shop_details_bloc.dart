import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:bcn_agency_banking_flutter/src/models/shop_details.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_shop_details_bloc.freezed.dart';

class EditShopDetailsBloc
    extends Bloc<EditShopDetailsEvent, EditShopDetailsState> {
  String? _shopName;
  Photo? _shopPhoto;
  bool? _isShopPhotoDeleted;
  LocationDetails? _shopLocationDetails;

  EditShopDetailsBloc() : super(const Initial()) {
    on<EditShopDetailsEvent>((event, emit) {
      switch (event) {
        case OnShopNameUpdated(:final name):
          _shopName = name.trim();
        case OnShopPhotoUpdated(:final photo):
          _shopPhoto = photo;
          _isShopPhotoDeleted = photo == null;
        case OnShopLocationUpdated(:final locationDetails):
          _shopLocationDetails = locationDetails;
          emit(const EditShopDetailsState.shopLocationUpdated());
          emit(const EditShopDetailsState.locationUpdateCompleted());
      }
    });
  }

  LocationDetails? get shopLocation => _shopLocationDetails;

  String? get shopName => _shopName;

  Photo? get shopPhoto => _shopPhoto;

  bool? get isShopPhotoDeleted => _isShopPhotoDeleted;

  bool isDetailsUpdated(ShopDetails details) {
    if (_shopName != null && details.name.trim() != _shopName) {
      return true;
    } else if (_shopPhoto != null || _isShopPhotoDeleted == true) {
      return true;
    } else if (details.latitude != _shopLocationDetails!.latitude ||
        details.longitude != _shopLocationDetails!.longitude) {
      return true;
    } else {
      return false;
    }
  }
}

@freezed
sealed class EditShopDetailsState with _$EditShopDetailsState {
  const factory EditShopDetailsState.initial() = Initial;

  const factory EditShopDetailsState.loading() = Loading;

  const factory EditShopDetailsState.shopLocationUpdated() =
      ShopLocationUpdated;

  const factory EditShopDetailsState.locationUpdateCompleted() =
      LocationUpdateCompleted;
}

@freezed
sealed class EditShopDetailsEvent with _$EditShopDetailsEvent {
  const factory EditShopDetailsEvent.onShopNameUpdated(String name) =
      OnShopNameUpdated;

  const factory EditShopDetailsEvent.onShopPhotoUpdated(Photo? photo) =
      OnShopPhotoUpdated;

  const factory EditShopDetailsEvent.onShopLocationUpdated(
    LocationDetails locationDetails,
  ) = OnShopLocationUpdated;
}
