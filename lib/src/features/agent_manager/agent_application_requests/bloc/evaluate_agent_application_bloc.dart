import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/data_source/document_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/agent_application_requests_list_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/repository/application_requests_repository.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/shop_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../core/logger.dart';

part 'evaluate_agent_application_bloc.freezed.dart';

class EvaluateAgentApplicationBloc
    extends Bloc<EvaluateAgentApplicationEvent, EvaluateAgentApplicationState>
    with RPCHandler {
  final AgentApplication application;
  final ApplicationRequestsRepository _repository =
      ApplicationRequestsRepository();
  final DocumentService _documentService = locator<DocumentService>();
  ShopDetails? _shopDetails;
  bool isImageLoaded = false;

  EvaluateAgentApplicationBloc({required this.application})
    : super(const Initial()) {
    on<EvaluateAgentApplicationEvent>((event, emit) async {
      switch (event) {
        case EvaluateApplication(
          :final context,
          :final reviewStatus,
          :final comment,
        ):
          emit(const EvaluateAgentApplicationState.loading());
          AgencyAppDialog.showSpinnerDialog(context);
          await _evaluateAgentApplication(context, emit, reviewStatus, comment);
        case OnShopImageLoaded(:final image):
          l.d("Shop Image loaded");
          _shopDetails = ShopDetails(
            name: _shopDetails!.name,
            latitude: _shopDetails!.latitude,
            longitude: _shopDetails!.longitude,
            isImageDeleted: _shopDetails!.isImageDeleted,
            address: _shopDetails!.address,
            initialShopLocation: _shopDetails!.initialShopLocation,
            initialServerPhoto: image,
            photo: _shopDetails!.photo,
          );
          isImageLoaded = true;
          emit(const EvaluateAgentApplicationState.shopImageLoaded());
        case OnShopDetailsUpdated(:final details):
          l.d("Shop details updated: $shopDetails");
          emit(const EvaluateAgentApplicationState.loading());
          _shopDetails = details;
          emit(const EvaluateAgentApplicationState.shopDetailsUpdated());
      }
    });
  }

  ShopDetails? get shopDetails => _shopDetails;

  Future<void> setShopDetails() async {
    final FormattedAddress? formattedAddress = await getReverseGeocode(
      application.shopCoordinate.latitude,
      application.shopCoordinate.longitude,
    );
    _shopDetails = ShopDetails(
      name: application.shopName,
      latitude: application.shopCoordinate.latitude,
      longitude: application.shopCoordinate.longitude,
      address: formattedAddress,
      initialShopLocation: LocationDetails(
        latitude: application.shopCoordinate.latitude,
        longitude: application.shopCoordinate.longitude,
      ),
      isImageDeleted: false,
    );
  }

  bool isShopDetailsUpdated() {
    if (_shopDetails!.name.trim() != application.shopName.trim()) {
      return true;
    } else if (_shopDetails!.photo != null ||
        _shopDetails!.isImageDeleted == true) {
      return true;
    } else if (_shopDetails!.latitude != application.shopCoordinate.latitude ||
        _shopDetails!.longitude != application.shopCoordinate.longitude) {
      return true;
    } else {
      return false;
    }
  }

  Future<void> _evaluateAgentApplication(
    BuildContext context,
    Emitter<EvaluateAgentApplicationState> emit,
    EvaluateAgentApplicationRequestReviewStatusEnum reviewStatus,
    ABComment comment,
  ) async {
    l.d('''Evaluating Agent Application
    applicationId: ${application.id},
    reviewStatus: $reviewStatus,
    comment: $comment,
    ''');
    await rpcHandler(
      () async {
        late EvaluateAgentApplicationRequestReviewStatusEnum
        applicationReviewStatus;
        if (reviewStatus.isApproved() && _shopDetails != null) {
          LeoUUID? photoId = application.shopImageId;
          if (_shopDetails!.photo != null) {
            photoId =
                currentFlavor.isMock
                    ? await Future.delayed(
                      1.seconds,
                      () => LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
                    )
                    : await _documentService.uploadPhoto(_shopDetails!.photo!);
          }
          applicationReviewStatus =
              EvaluateAgentApplicationRequestReviewStatusEnum.APPROVED(
                shopImageId: _shopDetails!.isImageDeleted ? null : photoId,
                shopName: _shopDetails!.name,
                shopCoordinate: Coordinate(
                  latitude: _shopDetails!.latitude,
                  longitude: _shopDetails!.longitude,
                ),
              );
        } else {
          applicationReviewStatus = reviewStatus;
        }
        final result = await _repository.evaluateAgentApplication(
          application.id,
          applicationReviewStatus,
          comment,
        );
        if (context.mounted) {
          await _handleEvaluateAgentApplicationResult(
            emit,
            result,
            reviewStatus,
            context,
          );
        }
      },
      onTransientError: (_) {
        l.d("Emitting Transient Error");
        context.rootNavigator.pop();
        emit(const EvaluateAgentApplicationState.transientError());
      },
    );
  }

  void _onReviewSubmitted(
    EvaluateAgentApplicationRequestReviewStatusEnum reviewStatus,
    BuildContext context,
  ) {
    final String agentName =
        application.userPersonalDetails.lastName != null
            ? "${application.userPersonalDetails.firstName} ${application.userPersonalDetails.lastName}"
            : application.userPersonalDetails.firstName;
    final String contentText =
        reviewStatus ==
                EvaluateAgentApplicationRequestReviewStatusEnum.REJECTED()
            ? context.localizations.requestRejected(agentName)
            : context.localizations.requestApproved(agentName);
    AgencyAppDialog.showAppDialog(
      context: context,
      contentText: contentText,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.okay,
              onTap: () {
                dialogContext.navigator.pop();
                _navigateToRequestListScreen(context);
              },
            ),
          ],
    );
  }

  Future<void> _handleEvaluateAgentApplicationResult(
    Emitter<EvaluateAgentApplicationState> emit,
    LeoRPCResult<
      EvaluateAgentApplicationResponse,
      EvaluateAgentApplicationError
    >
    result,
    EvaluateAgentApplicationRequestReviewStatusEnum reviewStatus,
    BuildContext context,
  ) async {
    result.when(
      response: (response) {
        context.rootNavigator.pop();
        _onReviewSubmitted(reviewStatus, context);
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const EvaluateAgentApplicationState.prominentError());
        error.when(
          inactiveUser: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.inactiveUser,
              onOkay: _navigateToRequestListScreen,
            );
          },
          invalidShopImageId: (_) {
            ProminentErrorHandler.invalidShopImage();
          },
          invalidAgentApplicationId: (_) {
            ProminentErrorHandler.genericError(
              onOkay: _navigateToRequestListScreen,
            );
          },
          applicationAlreadyApproved: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.applicationAlreadyApproved,
              onOkay: _navigateToRequestListScreen,
            );
          },
          applicationAlreadyRejected: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.applicationAlreadyRejected,
              onOkay: _navigateToRequestListScreen,
            );
          },
        );
      },
    );
  }

  void _navigateToRequestListScreen(BuildContext context) {
    context.navigator.popUntil(
      ModalRoute.withName(AgentApplicationRequestsListScreen.id),
    );
  }
}

@freezed
sealed class EvaluateAgentApplicationState
    with _$EvaluateAgentApplicationState {
  const factory EvaluateAgentApplicationState.initial() = Initial;

  const factory EvaluateAgentApplicationState.loading() = Loading;

  const factory EvaluateAgentApplicationState.applicationEvaluationSuccessful(
    EvaluateAgentApplicationRequestReviewStatusEnum reviewStatus,
  ) = ApplicationEvaluationSuccessful;

  const factory EvaluateAgentApplicationState.shopImageLoaded() =
      ShopImageLoaded;

  const factory EvaluateAgentApplicationState.prominentError() = ProminentError;

  const factory EvaluateAgentApplicationState.transientError() = TransientError;

  const factory EvaluateAgentApplicationState.shopDetailsUpdated() =
      ShopDetailsUpdated;
}

@freezed
sealed class EvaluateAgentApplicationEvent
    with _$EvaluateAgentApplicationEvent {
  const factory EvaluateAgentApplicationEvent.evaluateApplication(
    BuildContext context,
    EvaluateAgentApplicationRequestReviewStatusEnum reviewStatus,
    ABComment comment,
  ) = EvaluateApplication;

  const factory EvaluateAgentApplicationEvent.onShopImageLoaded(
    ImageProvider? image,
  ) = OnShopImageLoaded;

  const factory EvaluateAgentApplicationEvent.onShopDetailsUpdated(
    ShopDetails details,
  ) = OnShopDetailsUpdated;
}
