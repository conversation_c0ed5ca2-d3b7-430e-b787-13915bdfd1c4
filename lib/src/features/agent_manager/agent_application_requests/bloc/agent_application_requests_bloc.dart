import 'package:agency_banking_rpcs/agency/agent_application_type.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/repository/application_requests_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../core/logger.dart';

part 'agent_application_requests_bloc.freezed.dart';

class AgentApplicationsRequestsBloc
    extends Bloc<AgentApplicationsRequestsEvent, AgentApplicationsRequestsState>
    with R<PERSON><PERSON><PERSON><PERSON> {
  final refreshController = RefreshController();
  final ApplicationRequestsRepository _repository =
      ApplicationRequestsRepository();

  AgentApplicationsRequestsBloc() : super(const Initial()) {
    on<AgentApplicationsRequestsEvent>((event, emit) async {
      switch (event) {
        case GetApplications():
          emit(const AgentApplicationsRequestsState.loading());
          await _getAgentApplicationRequests(emit);
        case PullToRefresh():
          l.d("Called Pull to refresh");
          emit(const AgentApplicationsRequestsState.refreshInProgress());
          await _getAgentApplicationRequests(emit);
      }
    });
  }

  Future<void> _getAgentApplicationRequests(
    Emitter<AgentApplicationsRequestsState> emit,
  ) async {
    l.d("Getting Application Requests");
    await rpcHandler(
      () async {
        final rpcResult = await _repository.getApplicationRequests();
        l.d("GetApplicationRequests result: $rpcResult");
        rpcResult.when(
          response: (response) {
            emit(
              AgentApplicationsRequestsState.onData(response.agentApplications),
            );
            refreshController.refreshCompleted();
          },
          error: (error) {
            // This should never happen since this RPC call does not
            // throw any errors.
            // Therefore, seeing this exception is a developer error.
            throw DeveloperError("Something went wrong: $error");
          },
        );
      },
      onTransientError: (String errorMessage) {
        refreshController.refreshFailed();
        emit(AgentApplicationsRequestsState.transientError(errorMessage));
      },
    );
  }
}

@freezed
sealed class AgentApplicationsRequestsState
    with _$AgentApplicationsRequestsState {
  const factory AgentApplicationsRequestsState.initial() = Initial;

  const factory AgentApplicationsRequestsState.loading() = Loading;

  const factory AgentApplicationsRequestsState.onData(
    List<AgentApplication> requests,
  ) = OnData;

  const factory AgentApplicationsRequestsState.refreshInProgress() =
      RefreshInProgress;

  const factory AgentApplicationsRequestsState.transientError(
    String errorMessage,
  ) = TransientError;
}

@freezed
sealed class AgentApplicationsRequestsEvent
    with _$AgentApplicationsRequestsEvent {
  const factory AgentApplicationsRequestsEvent.getApplications() =
      GetApplications;

  const factory AgentApplicationsRequestsEvent.pullToRefresh() = PullToRefresh;
}
