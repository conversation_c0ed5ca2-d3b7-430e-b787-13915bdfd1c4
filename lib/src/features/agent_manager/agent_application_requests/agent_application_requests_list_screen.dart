import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/agent_application_request_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/bloc/agent_application_requests_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/agent_application_info_card.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/generic_error_or_empty_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/pull_to_refresh_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AgentApplicationRequestsListScreen extends StatefulWidget {
  static const id = "/agent-application-requests-list-screen";

  const AgentApplicationRequestsListScreen({super.key});

  @override
  State<AgentApplicationRequestsListScreen> createState() =>
      _AgentApplicationRequestsListScreenState();
}

class _AgentApplicationRequestsListScreenState
    extends State<AgentApplicationRequestsListScreen> {
  late final AgentApplicationsRequestsBloc _requestBloc =
      BlocProvider.of<AgentApplicationsRequestsBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.agentApplications),
      body: BlocBuilder<
        AgentApplicationsRequestsBloc,
        AgentApplicationsRequestsState
      >(
        buildWhen: (_, AgentApplicationsRequestsState currentState) {
          return currentState is! RefreshInProgress;
        },
        builder: (BuildContext context, AgentApplicationsRequestsState state) {
          if (state is OnData) {
            return _getRequestList(state.requests);
          } else if (state is TransientError) {
            return NoInternetWidget(
              onRetryButtonClicked:
                  () => _requestBloc.add(
                    const AgentApplicationsRequestsEvent.getApplications(),
                  ),
            );
          } else {
            return const Spinner();
          }
        },
      ),
    );
  }

  void _onRefresh() {
    _requestBloc.add(const AgentApplicationsRequestsEvent.pullToRefresh());
  }

  Widget _getRequestList(List<AgentApplication> requests) {
    return PullToRefreshWidget(
      onRefresh: _onRefresh,
      controller: _requestBloc.refreshController,
      child:
          requests.isEmpty
              ? GenericErrorOrEmptyWidget(
                labelText: context.localizations.noApplicationsTitle,
                iconAssetPath: ABAssets.fileTimesAltIcon,
              )
              : ListView.separated(
                padding: verticalPaddingEight,
                itemCount: requests.length,
                separatorBuilder:
                    (_, __) => const Divider(
                      thickness: dividerThickness,
                      height: dividerThickness,
                    ),
                itemBuilder: (context, index) {
                  final agentApplication = requests[index];
                  final userDetails = agentApplication.userPersonalDetails;

                  final String agentFirstName = userDetails.firstName;
                  final String agentLastName = userDetails.lastName ?? "";
                  return AgentApplicationInfoCard(
                    name: "$agentFirstName $agentLastName",
                    phoneNumber:
                        agentApplication.phoneNumber.formattedPhoneNumber,
                    shopName: agentApplication.shopName,
                    createdAt: agentApplication.submittedAt,
                    imageURL: agentApplication.profileImage.getNetworkPhotoURL(
                      context,
                    ),
                    onTap: () async {
                      await context.navigator.pushNamed(
                        AgentApplicationRequestDetailsScreen.id,
                        arguments: agentApplication,
                      );
                      _refreshScreen();
                    },
                  );
                },
              ),
    );
  }

  void _refreshScreen() {
    if (!mounted) return;
    _requestBloc.refreshController.requestRefresh(needMove: false);
  }
}
