import 'package:bcn_agency_banking_flutter/src/core/route_generator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/bloc/evaluate_agent_application_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/shop_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/widgets/application_buttons.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/gender_helper.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/image_display_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/image_placeholder_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_button_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/photo_viewer.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/section_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class AgentApplicationRequestDetailsScreen extends StatefulWidget {
  static const id = "/agent-application-details-screen";

  const AgentApplicationRequestDetailsScreen({super.key});

  @override
  State<AgentApplicationRequestDetailsScreen> createState() =>
      _AgentApplicationRequestDetailsScreenState();
}

class _AgentApplicationRequestDetailsScreenState
    extends State<AgentApplicationRequestDetailsScreen> {
  late final _evaluateBloc = BlocProvider.of<EvaluateAgentApplicationBloc>(
    context,
  );
  late final _application = _evaluateBloc.application;
  late final _agentFirstName = _application.userPersonalDetails.firstName;
  late final _agentLastName = _application.userPersonalDetails.lastName ?? "";

  @override
  Widget build(BuildContext context) {
    return ImageDisplayLayout(
      appBarTitle: context.localizations.agentRequest,
      photoId: _application.signatureImageId,
      requestId: _application.id,
      onImageLoaded: (context, image) {
        return _buildScreenContents(context, image);
      },
    );
  }

  Widget _buildScreenContents(BuildContext context, ImageProvider? image) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(child: _buildBody(context, image)),
        ),
        Padding(
          padding: commonScreenPadding,
          child: _buildApplicationButtons(context),
        ),
      ],
    );
  }

  Widget _buildBody(BuildContext context, ImageProvider? image) {
    return Column(
      children: [
        verticalGapEight,
        SectionWidget(
          title: context.localizations.userDetails,
          children: [
            InfoLabel(
              title: context.localizations.fullName,
              bodyText: "$_agentFirstName $_agentLastName",
            ),
            if (_application.userPersonalDetails.otherName != null)
              InfoLabel(
                title: context.localizations.otherName,
                bodyText: _application.userPersonalDetails.otherName,
              ),
            InfoLabel(
              title: context.localizations.dateOfBirth,
              bodyText: _getFormattedDate(
                _application.userPersonalDetails.dateOfBirth,
              ),
            ),
            InfoLabel(
              title: context.localizations.nationalID,
              bodyText: _application.userPersonalDetails.nationalId,
            ),
            InfoLabel(
              title: context.localizations.gender,
              bodyText: GenderHelper.getGenderString(
                _application.userPersonalDetails.gender,
                context,
              ),
            ),
            InfoLabel(
              title: context.localizations.mobileNumber,
              bodyText: _application.phoneNumber.formattedPhoneNumber,
              actionButton: PhoneButtonWidget(
                phoneNumber: _application.phoneNumber,
              ),
            ),
          ],
        ),
        TitleWidget(title: context.localizations.signaturePhoto),
        Padding(
          padding: allPaddingSixteen,
          child: PhotoViewer(
            image:
                image != null
                    ? Image(image: image, fit: BoxFit.cover)
                    : ImagePlaceholderWidget(
                      placeholderText:
                          context.localizations.signatureImageNotUploaded,
                    ),
          ),
        ),
        verticalGapEight,
      ],
    );
  }

  ApplicationButtons _buildApplicationButtons(BuildContext context) {
    return ApplicationButtons(
      evaluateBloc: _evaluateBloc,
      onPrimaryButtonPressed: (_) async {
        AgencyAppDialog.showSpinnerDialog(context);
        await _evaluateBloc.setShopDetails();
        if (context.mounted) {
          context.navigator.pop();
          context.navigator.push(
            Routes.getMaterialRoute(
              ShopDetailsScreen.id,
              BlocProvider.value(
                value: _evaluateBloc,
                child: const ShopDetailsScreen(),
              ),
            ),
          );
        }
      },
    );
  }

  String _getFormattedDate(LeoDate leoDate) {
    final dateString = leoDate.date;
    final DateTime date = DateTime.parse(dateString);
    final formatter = DateFormatter.dobFormat();
    return formatter.format(date);
  }
}
