import 'package:agency_banking_rpcs/agency/ab_comment_type.dart';

import 'package:agency_banking_rpcs/agency/evaluate_agent_application_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/exceptions/url_destination_missing.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/bloc/evaluate_agent_application_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/edit_shop_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/widgets/application_buttons.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/view_map_tile.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/models/shop_details.dart';
import 'package:bcn_agency_banking_flutter/src/resources/app_colors.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/bottomsheets.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/image_display_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/image_placeholder_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/photo_viewer.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/section_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sprintf/sprintf.dart';

import '../../../core/location_services/location_service.dart';
import '../../../core/service_locator.dart';
import '../../../models/location_details.dart';
import '../../../widgets/dialogs.dart';

class ShopDetailsScreen extends StatefulWidget {
  static const id = "/shop-details-screen";

  const ShopDetailsScreen({super.key});

  @override
  State<ShopDetailsScreen> createState() => _ShopDetailsScreenState();
}

class _ShopDetailsScreenState extends State<ShopDetailsScreen> {
  late final EvaluateAgentApplicationBloc _evaluateBloc =
      BlocProvider.of<EvaluateAgentApplicationBloc>(context);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }
        final NavigatorState navigator = Navigator.of(context);
        if (await _shouldPopScreen()) {
          navigator.pop();
          return;
        }
      },
      child: ImageDisplayLayout(
        appBar: _buildAppBar(),
        photoId: _evaluateBloc.application.shopImageId,
        requestId: _evaluateBloc.application.id,
        onImageLoaded: (context, image) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _evaluateBloc.add(
              EvaluateAgentApplicationEvent.onShopImageLoaded(image),
            );
          });
          return _buildScreenContents(context, image);
        },
      ),
    );
  }

  Widget _buildScreenContents(BuildContext context, ImageProvider? image) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(child: _buildBody(context, image)),
        ),
        Padding(
          padding: commonScreenPadding,
          child: _buildApplicationButtons(context),
        ),
      ],
    );
  }

  Widget _buildBody(BuildContext context, ImageProvider? image) {
    return BlocBuilder<
      EvaluateAgentApplicationBloc,
      EvaluateAgentApplicationState
    >(
      buildWhen: (_, EvaluateAgentApplicationState newState) {
        // This state only updates the edit button on the app bar.
        return newState !=
            const EvaluateAgentApplicationState.shopImageLoaded();
      },
      builder: (context, state) {
        return Column(
          children: [
            verticalGapEight,
            SectionWidget(
              title: context.localizations.basicDetails,
              children: [
                InfoLabel(
                  title: context.localizations.shopName,
                  bodyText: _evaluateBloc.shopDetails!.name,
                ),
              ],
            ),
            _getLocationAddressTile(),
            TitleWidget(title: context.localizations.shopPhoto),
            Padding(
              padding: allPaddingSixteen,
              child: PhotoViewer(
                image:
                    _evaluateBloc.shopDetails!.photo != null
                        ? Image(
                          image: FileImage(
                            _evaluateBloc.shopDetails!.photo!.photo,
                          ),
                          fit: BoxFit.cover,
                        )
                        : image != null &&
                            !_evaluateBloc.shopDetails!.isImageDeleted
                        ? Image(image: image, fit: BoxFit.cover)
                        : ImagePlaceholderWidget(
                          placeholderText:
                              _evaluateBloc.shopDetails!.isImageDeleted
                                  ? context.localizations.shopPhoto
                                  : context.localizations.shopPhotoNotUploaded,
                        ),
              ),
            ),
            verticalGapEight,
          ],
        );
      },
    );
  }

  PrimaryAppBar _buildAppBar() {
    return PrimaryAppBar(
      onLeadingIconTapped: () async {
        final shouldPopScreen = await _shouldPopScreen();
        if (shouldPopScreen && mounted) context.navigator.pop();
      },
      title: context.localizations.verifyShopDetails,
      actionIcon: BlocBuilder<
        EvaluateAgentApplicationBloc,
        EvaluateAgentApplicationState
      >(
        builder: (context, state) {
          return IconButton(
            icon: IconWidget(
              assetName: ABAssets.editIcon,
              iconColor:
                  _evaluateBloc.isImageLoaded
                      ? context.appColors.genericWhiteColor
                      : neutralDarkLighter,
            ),
            onPressed:
                _evaluateBloc.isImageLoaded
                    ? () async {
                      final ShopDetails? shopDetails =
                          await context.navigator.pushNamed(
                                EditShopDetailsScreen.id,
                                arguments: _evaluateBloc.shopDetails,
                              )
                              as ShopDetails?;
                      if (shopDetails != null) {
                        _evaluateBloc.add(
                          EvaluateAgentApplicationEvent.onShopDetailsUpdated(
                            shopDetails,
                          ),
                        );
                      }
                    }
                    : null,
          );
        },
      ),
    );
  }

  String _getLocationLabel() {
    final shopDetails = _evaluateBloc.shopDetails!;
    if (shopDetails.address == null) {
      return "${shopDetails.latitude}, ${shopDetails.longitude}";
    } else {
      return shopDetails.address!.addressLabel;
    }
  }

  String? _getLocationInfo() {
    final shopDetails = _evaluateBloc.shopDetails!;
    if (shopDetails.address == null) {
      return null;
    } else {
      return shopDetails.address!.addressText;
    }
  }

  Widget _getLocationAddressTile() {
    return ViewMapTile(
      labelText: _getLocationLabel(),
      infoText: _getLocationInfo(),
      labelStyle: context.appTextStyles.labelText2Bold,
      onTap: _handleOpenMaps,
      assetName: ABAssets.directionsIcon,
    );
  }

  ApplicationButtons _buildApplicationButtons(BuildContext context) {
    return ApplicationButtons(
      evaluateBloc: _evaluateBloc,
      primaryButtonText: context.localizations.approve,
      onPrimaryButtonPressed: (context) async {
        final LocationService locationService = locator<LocationService>();
        final bool isShopInRange = locationService
            .checkCurrentLocationWithinAcceptableRange(
              allowedDistance: _evaluateBloc.application.locationRadius,
              location: LocationDetails(
                latitude: _evaluateBloc.shopDetails!.latitude,
                longitude: _evaluateBloc.shopDetails!.longitude,
              ),
            );
        if (!isShopInRange) {
          AgencyAppDialog.showErrorDialog(
            context: context,
            contentText: context.localizations.approveApplicationLocationCheck(
              _evaluateBloc.application.locationRadius,
            ),
            buttonText: context.localizations.tryAgain,
          );
        } else {
          final ABComment? approvedComment =
              await BottomSheets.showCommentBottomSheet(
                context: context,
                commentReason: context.localizations.confirmApproveRequest,
                ctaButtonText: context.localizations.approve,
              );
          if (approvedComment != null && context.mounted) {
            _evaluateBloc.add(
              EvaluateAgentApplicationEvent.evaluateApplication(
                context,
                EvaluateAgentApplicationRequestReviewStatusEnum.APPROVED(),
                approvedComment,
              ),
            );
          }
        }
      },
    );
  }

  Future<void> _handleOpenMaps() async {
    final shopDetails = _evaluateBloc.shopDetails!;
    try {
      await launchExternalUrl(
        sprintf(googleLocationQueryURL, [
          shopDetails.latitude,
          shopDetails.longitude,
        ]),
      );
    } on UrlDestinationMissingException catch (_) {
      if (mounted) {
        _communicateMissingDestination(context);
      }
    }
  }

  void _communicateMissingDestination(BuildContext context) {
    AgencyAppDialog.showAppDialog(
      context: context,
      contentText: context.localizations.unableToOpenGoogleMaps,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.dismiss,
              onTap: () => dialogContext.navigator.pop(),
            ),
          ],
    );
  }

  Future<bool> _shouldPopScreen() async {
    if (_evaluateBloc.isShopDetailsUpdated()) {
      final bool? shouldDiscardChanges =
          await AgencyAppDialog.showDiscardChangesConfirmation(context);
      return shouldDiscardChanges == true;
    } else {
      return true;
    }
  }
}
