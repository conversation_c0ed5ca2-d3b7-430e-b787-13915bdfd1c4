import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/refund_amount/blocs/refund_amount_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../widgets/bottomsheets.dart';
import '../../../widgets/phone_button_widget.dart';
import '../../../widgets/section_widget.dart';

class AMRefundAmountDetailsScreen extends StatefulWidget {
  static const id = "/refund-amount-confirm-screen";

  const AMRefundAmountDetailsScreen({Key? key}) : super(key: key);

  @override
  State<AMRefundAmountDetailsScreen> createState() =>
      _AMRefundAmountDetailsScreenState();
}

class _AMRefundAmountDetailsScreenState
    extends State<AMRefundAmountDetailsScreen> {
  late final _refundAmountBloc = BlocProvider.of<RefundAmountBloc>(context);
  late final GetMoneyTransferRefundRequestDetailsResponse
  _moneyTransferRequestResponse =
      _refundAmountBloc.getMoneyTransferRefundRequestDetailsResponse;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.requestDetails,
      disableBackGesture: false,
      padding: EdgeInsets.zero,
      ctaWidget: _buildCTA(),
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalGapEight,
        _getRequestDetails(),
        _getAgentDetails(),
        _getTransactionDetails(),
        verticalGapThirtySix,
      ],
    );
  }

  Widget _getRequestDetails() {
    final ABUserDetails senderDetails = _refundAmountBloc.senderDetails;
    final MoneyTransferRequestDetails requestDetails =
        _moneyTransferRequestResponse.requestDetails;
    return SectionWidget(
      title: context.localizations.requestDetails,
      children: [
        InfoLabel(
          title: context.localizations.sendersName,
          bodyText: senderDetails.userName.text,
        ),
        InfoLabel(
          title: context.localizations.senderNationalID,
          bodyText: senderDetails.nationalId.id,
        ),
        InfoLabel(
          title: context.localizations.requestID,
          bodyText: requestDetails.requestId.id,
        ),
      ],
    );
  }

  Widget _getAgentDetails() {
    final ABUserDetails agentDetails = _refundAmountBloc.agentDetails;
    return SectionWidget(
      title: context.localizations.agentDetails,
      children: [
        InfoLabel(
          title: context.localizations.agentsName,
          bodyText: agentDetails.userName.text,
        ),
        InfoLabel(
          title: context.localizations.agentsMobileNumber,
          bodyText: agentDetails.phoneNumber.formattedPhoneNumber,
          actionButton: PhoneButtonWidget(
            phoneNumber: agentDetails.phoneNumber,
          ),
        ),
      ],
    );
  }

  Widget _getTransactionDetails() {
    return TransactionDetailsWidget(
      transactionAmountLabel: context.localizations.amountTransferred,
      transactionAmount: _moneyTransferRequestResponse.requestDetails.amount,
      transactionFee: _moneyTransferRequestResponse.transactionFee,
      receivingAmountLabel: context.localizations.refundAmount,
      receivingAmount: _moneyTransferRequestResponse.refundAmount,
    );
  }

  Widget _buildCTA() {
    return PrimaryButton(
      labelText: context.localizations.refundAmount,
      onPressed: () async {
        final ABComment? refundAmountComment =
            await BottomSheets.showCommentBottomSheet(
              context: context,
              commentReason: context.localizations.refundAmountCommentReason,
              ctaButtonText: context.localizations.refundAmount,
            );
        if (refundAmountComment != null && mounted) {
          _refundAmountBloc.add(
            RefundAmountEvent.refundAmount(context, refundAmountComment),
          );
        }
      },
    );
  }
}
