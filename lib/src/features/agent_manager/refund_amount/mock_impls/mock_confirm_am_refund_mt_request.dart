import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../helpers/mock_helpers.dart';

class MockConfirmAMRefundMoneyTransferRequestRPCImpl
    extends AMRefundMoneyTransferRequestRPC {
  @override
  Future<
    LeoRPCResult<
      AMRefundMoneyTransferRequestResponse,
      AMRefundMoneyTransferRequestError
    >
  >
  execute(AMRefundMoneyTransferRequestRequest request) {
    final response = AMRefundMoneyTransferRequestResponse();
    final error =
        AMRefundMoneyTransferRequestError.AMRefundMoneyTransferRequestErrorAgentMonetaryTransactionLimitExceededError(
          errorCode: "",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return Future.delayed(1.seconds, () => result);
  }
}
