import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../helpers/mock_helpers.dart';

class MockGetMoneyTransferRefundRequestDetailsRPCImpl
    extends GetMoneyTransferRefundRequestDetailsRPC {
  @override
  Future<
    LeoRPCResult<
      GetMoneyTransferRefundRequestDetailsResponse,
      GetMoneyTransferRefundRequestDetailsError
    >
  >
  execute(GetMoneyTransferRefundRequestDetailsRequest request) {
    final response = GetMoneyTransferRefundRequestDetailsResponse(
      requestDetails: MoneyTransferRequestDetails(
        requestId: ABRequestId(id: 'asd'),
        senderDetail: ABUserDetails(
          userName: ABUserName(text: "<PERSON>"),
          phoneNumber: LeoPhoneNumber("+91 **********"),
          nationalId: NationalId(id: 'AHXCB71'),
        ),
        agentDetail: ABUserDetails(
          userName: ABUserName(text: "Esther Howard"),
          phoneNumber: LeoPhoneNumber("+91 **********"),
          nationalId: NationalId(id: 'BHXCB71'),
        ),
        amount: Amount(
          amount: 15000000,
          currency: Currency(currencyCode: 'MWK'),
        ),
      ),
      transactionFee: Amount(
        amount: 150000,
        currency: Currency(currencyCode: 'MWK'),
      ),
      refundAmount: Amount(
        amount: 14850000,
        currency: Currency(currencyCode: 'MWK'),
      ),
    );
    final error =
        GetMoneyTransferRefundRequestDetailsError.GetMoneyTransferRefundRequestDetailsErrorInvalidRequestIdError(
          errorCode: "",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return Future.delayed(1.seconds, () => result);
  }
}
