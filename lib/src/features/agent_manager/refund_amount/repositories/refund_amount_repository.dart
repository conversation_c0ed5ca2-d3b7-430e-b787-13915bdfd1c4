import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/refund_amount/mock_impls/mock_confirm_am_refund_mt_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/refund_amount/mock_impls/mock_get_money_transfer_refund_request_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import '../../../../core/auth/rpc_auth_provider.dart';
import '../../../../core/service_locator.dart';

class RefundAmountRepository {
  Future<
    LeoRPCResult<
      GetMoneyTransferRefundRequestDetailsResponse,
      GetMoneyTransferRefundRequestDetailsError
    >
  >
  getMoneyTransferRefundRequestDetails(
    NationalId senderNationalId,
    ABRequestId requestId,
  ) async {
    final request = GetMoneyTransferRefundRequestDetailsRequest(
      senderNationalId: senderNationalId,
      requestId: requestId,
    );
    final GetMoneyTransferRefundRequestDetailsRPC impl =
        currentFlavor.isMock
            ? MockGetMoneyTransferRefundRequestDetailsRPCImpl()
            : GetMoneyTransferRefundRequestDetailsRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      AMRefundMoneyTransferRequestResponse,
      AMRefundMoneyTransferRequestError
    >
  >
  confirmAMRefundMoneyTransferRequest(
    NationalId senderNationalId,
    ABRequestId requestId,
    ABComment comment,
  ) async {
    final request = AMRefundMoneyTransferRequestRequest(
      senderNationalId: senderNationalId,
      requestId: requestId,
      comment: comment,
    );
    final AMRefundMoneyTransferRequestRPC impl =
        currentFlavor.isMock
            ? MockConfirmAMRefundMoneyTransferRequestRPCImpl()
            : AMRefundMoneyTransferRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
