import 'package:bcn_agency_banking_flutter/src/features/agent_manager/refund_amount/blocs/refund_amount_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../utils/constants.dart';
import '../../../utils/validators.dart';

class AMRefundAmountRequestScreen extends StatefulWidget {
  static const id = "/refund-amount-request-screen";

  const AMRefundAmountRequestScreen({super.key});

  @override
  State<AMRefundAmountRequestScreen> createState() =>
      _AMRefundAmountRequestScreenState();
}

class _AMRefundAmountRequestScreenState
    extends State<AMRefundAmountRequestScreen> {
  late final _refundAmountBloc = BlocProvider.of<RefundAmountBloc>(context);
  final _refundAmountFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldAutoValidateRefundAmount = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.refundAmount,
      disableBackGesture: false,
      ctaWidget: _buildCTA(context),
      child: Form(
        key: _refundAmountFormKey,
        autovalidateMode: _shouldAutoValidateRefundAmount,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalGapTwentyFour,
            PrimaryTextField(
              maxLength: nationalIdMaxLength,
              inputFormatters: [nationalIdFormatter],
              onChanged: (String value) {
                _refundAmountBloc.add(
                  RefundAmountEvent.nationalIDChanged(value),
                );
              },
              labelText: context.localizations.senderNationalID,
              validator:
                  (nationalID) => Validators.emptyValidator(
                    context,
                    context.localizations.senderNationalID,
                    nationalID,
                  ),
            ),
            verticalGapSixteen,
            PrimaryTextField(
              maxLength: requestIDMaxLength,
              inputFormatters: [requestIdFormatter],
              onChanged: (String value) {
                _refundAmountBloc.add(
                  RefundAmountEvent.requestIDChanged(value),
                );
              },
              labelText: context.localizations.requestIdHeader,
              validator:
                  (requestID) => Validators.emptyValidator(
                    context,
                    context.localizations.requestIdHeader,
                    requestID,
                  ),
            ),
            verticalGapThirtySix,
          ],
        ),
      ),
    );
  }

  Widget _buildCTA(BuildContext context) {
    return PrimaryButton(
      labelText: context.localizations.continueText,
      onPressed: () {
        final isFormValidated =
            _refundAmountFormKey.currentState?.validate() ?? false;
        if (!isFormValidated) {
          setState(() {
            _shouldAutoValidateRefundAmount =
                AutovalidateMode.onUserInteraction;
          });
          return;
        }
        _refundAmountBloc.add(RefundAmountEvent.fetchRequestDetails(context));
      },
    );
  }
}
