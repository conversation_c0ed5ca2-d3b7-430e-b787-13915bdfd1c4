import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/refund_amount/refund_amount_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/refund_amount/repositories/refund_amount_repository.dart';
import 'package:bcn_agency_banking_flutter/src/features/session_pin/enter_session_pin/enter_session_pin_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../core/logger.dart';
import '../../../../errors/developer_error.dart';
import '../../../../helpers/helpers.dart';

part 'refund_amount_bloc.freezed.dart';

class RefundAmountBloc extends Bloc<RefundAmountEvent, RefundAmountState>
    with RPCHandler {
  final _refundAmountRepository = RefundAmountRepository();
  String? _senderNationalId;
  String? _requestId;
  late GetMoneyTransferRefundRequestDetailsResponse
  _getMoneyTransferRefundRequestDetailsResponse;

  RefundAmountBloc() : super(const Initial()) {
    on<RefundAmountEvent>((event, emit) async {
      switch (event) {
        case FetchRequestDetails(:final context):
          await _fetchRefundRequestDetails(emit, context);
        case NationalIDChanged(:final nationalId):
          _senderNationalId = nationalId.trim();
        case RequestIDChanged(:final requestId):
          _requestId = requestId.trim();
        case RefundAmountTapped(:final context, :final comment):
          await _onRefundAmountEvent(emit, context, comment);
      }
    });
  }

  GetMoneyTransferRefundRequestDetailsResponse
  get getMoneyTransferRefundRequestDetailsResponse =>
      _getMoneyTransferRefundRequestDetailsResponse;

  ABUserDetails get senderDetails =>
      _getMoneyTransferRefundRequestDetailsResponse.requestDetails.senderDetail;

  ABUserDetails get agentDetails =>
      _getMoneyTransferRefundRequestDetailsResponse.requestDetails.agentDetail;

  Future<void> _fetchRefundRequestDetails(
    Emitter<RefundAmountState> emit,
    BuildContext context,
  ) async {
    l.d('''Fetching Refund Request details: 
    nationalId: $_senderNationalId,
    requestId: $_requestId''');
    emit(const RefundAmountState.loading());
    AgencyAppDialog.showSpinnerDialog(context);
    await rpcHandler(
      () async {
        final rpcResult = await _refundAmountRepository
            .getMoneyTransferRefundRequestDetails(
              NationalId(id: _senderNationalId!),
              ABRequestId(id: _requestId!),
            );
        l.d("Refund Amount Request details result: $rpcResult");
        // Pop the loader dialog.
        if (context.mounted) {
          context.rootNavigator.pop();
        }
        rpcResult.when(
          response: (response) {
            _getMoneyTransferRefundRequestDetailsResponse = response;
            emit(const RefundAmountState.requestDetailsFetched());
            context.navigator.pushNamed(
              AMRefundAmountDetailsScreen.id,
              arguments: this,
            );
          },
          error: (error) {
            emit(const RefundAmountState.prominentError());
            error.when(
              invalidRequestId: (_) {
                _showRequestErrorDialog(context);
              },
              invalidSenderNationalId: (_) {
                _showRequestErrorDialog(context);
              },
            );
          },
        );
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        emit(const RefundAmountState.transientError());
      },
    );
  }

  Future<void> _onRefundAmountEvent(
    Emitter<RefundAmountState> emit,
    BuildContext context,
    ABComment comment,
  ) async {
    l.d('''Refund Amount event: 
    nationalId: $_senderNationalId,
    requestId: $_requestId,
    comment: $comment''');
    final bool isAMAuthenticated = await _authenticateUsingSessionPIN(
      context,
      emit,
    );
    if (!isAMAuthenticated) return;
    if (context.mounted) {
      AgencyAppDialog.showSpinnerDialog(context);
    }
    await rpcHandler(
      () async {
        final rpcResult = await _refundAmountRepository
            .confirmAMRefundMoneyTransferRequest(
              NationalId(id: _senderNationalId!),
              ABRequestId(id: _requestId!),
              comment,
            );
        l.d("Confirm AM Refund MT Request: $rpcResult");
        if (context.mounted) {
          // Pop the loader dialog.
          context.rootNavigator.pop();
        }
        rpcResult.when(
          response: (response) {
            AgencyAppDialog.showAppDialog(
              context: context,
              contentText: context.localizations.refundSuccessMessage,
              actions:
                  (dialogContext) => [
                    PrimaryTextButton(
                      text: context.localizations.okay,
                      onTap: () {
                        dialogContext.navigator.pop();
                        navigateToAgentManagerHomeScreen(context);
                      },
                    ),
                  ],
            );
          },
          error: (error) {
            emit(const RefundAmountState.prominentError());
            error.when(
              invalidRequestId:
                  (e) => throw DeveloperError("Invalid Request ID $e"),
              invalidSenderNationalId:
                  (e) => throw DeveloperError("Invalid Sender National ID $e"),
              requestAlreadyRefunded: (_) {
                ProminentErrorHandler.requestAmountRefunded(
                  navigateToAgentManagerHomeScreen,
                );
              },
              agentPeriodicTransactionLimitExceeded: (_) async {
                await ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
              },
              agentMonetaryTransactionLimitExceeded: (_) async {
                await ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
              },
              receivingAccountWouldCrossLimit: (_) async {
                await ProminentErrorHandler.receivingAccountWouldCrossLimit();
              },
            );
          },
        );
      },
      onTransientError: (_) {
        // Pop the loader dialog.
        context.rootNavigator.pop();
        l.d("Emitting Transient error");
        emit(const RefundAmountState.transientError());
      },
    );
  }

  void _showRequestErrorDialog(BuildContext context) {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.incorrectNationalOrRequestID,
      buttonText: context.localizations.tryAgain,
    );
  }

  Future<bool> _authenticateUsingSessionPIN(
    BuildContext context,
    Emitter<RefundAmountState> emit,
  ) async {
    final bool? isAMAuthenticated =
        await context.rootNavigator.pushNamed(EnterSessionPinScreen.id)
            as bool?;
    return isAMAuthenticated ?? false;
  }
}

@freezed
sealed class RefundAmountState with _$RefundAmountState {
  const factory RefundAmountState.initial() = Initial;

  const factory RefundAmountState.loading() = Loading;

  const factory RefundAmountState.requestDetailsFetched() =
      RequestDetailsFetched;

  const factory RefundAmountState.prominentError() = ProminentError;

  const factory RefundAmountState.transientError() = TransientError;
}

@freezed
sealed class RefundAmountEvent with _$RefundAmountEvent {
  const factory RefundAmountEvent.nationalIDChanged(String nationalId) =
      NationalIDChanged;

  const factory RefundAmountEvent.requestIDChanged(String requestId) =
      RequestIDChanged;

  const factory RefundAmountEvent.fetchRequestDetails(BuildContext context) =
      FetchRequestDetails;

  const factory RefundAmountEvent.refundAmount(
    BuildContext context,
    ABComment comment,
  ) = RefundAmountTapped;
}
