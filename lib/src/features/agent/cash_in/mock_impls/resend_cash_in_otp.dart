import 'package:agency_banking_rpcs/agency/resend_cash_in_otp_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

import '../../../../helpers/mock_helpers.dart';

class MockResendCashInOTP extends ResendCashInOTPRPC {
  @override
  Future<LeoRPCResult<ResendCashInOTPResponse, ResendCashInOTPError>> execute(
    ResendCashInOTPRequest request,
  ) {
    final response = ResendCashInOTPResponse(
      otpDetails: OTPConstants.resendDetails,
    );

    final error =
        ResendCashInOTPError.ResendCashInOTPErrorTooManyResendRequestsError(
          errorCode: "123",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
