import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../helpers/mock_helpers.dart';

class MockCreateCashInRequest extends CreateCashInRequestRPC {
  @override
  Future<LeoRPCResult<CreateCashInRequestResponse, CreateCashInRequestError>>
  execute(CreateCashInRequestRequest request) async {
    final response = CreateCashInRequestResponse(
      recordId: LeoUUID(null),
      bcnRecipient: BCNUserDisplayInfo(
        displayName: 'Zikomo Malawa',
        profileImage: mockImage,
      ),
      claimedTransactionFee: Amount(
        amount: 1236,
        currency: Currency(currencyCode: "MWK"),
      ),
      feeExpiringAt: DateTime.now().add(10.minutes),
    );

    final error =
        CreateCashInRequestError.CreateCashInRequestErrorAgentDisabledForSpecificIntervalError(
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return await Future.delayed(1.seconds, () => result);
  }
}
