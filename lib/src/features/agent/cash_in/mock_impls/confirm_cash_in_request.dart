import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

import '../../../../helpers/mock_helpers.dart';

class MockConfirmCashInRequest extends ConfirmCashInRequestRPC {
  @override
  Future<LeoRPCResult<ConfirmCashInRequestResponse, ConfirmCashInRequestError>>
  execute(ConfirmCashInRequestRequest request) {
    final response = ConfirmCashInRequestResponse(
      succeededAt: DateTime.now(),
      transactionDetail: TransactionStatusDetail(
        itemDetail: [
          TransactionStatusItemDetail(
            label: LocalizedText(en: "Debited From"),
            valueType:
                TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                  title: "Zikomo Malawa",
                  image: LocalizedImage(
                    en: ThemedImage(dark: mockImage, light: mockImage),
                  ),
                ),
          ),
        ],
      ),
    );

    final error =
        ConfirmCashInRequestError.ConfirmCashInRequestErrorIncorrectOtpError(
          errorCode: "123",
          numberOfValidationAttemptsLeft: 0,
        );
    final result = getLeoRPCResult(
      shouldThrowError: !checkWhetherOTPValid(request.otp),
      response: response,
      error: error,
    );
    return Future.delayed(2.seconds, () => result);
  }
}
