import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

import '../../../../helpers/mock_helpers.dart';

class MockRequestCashInOTPRPC extends RequestCashInOTPRPC {
  @override
  Future<LeoRPCResult<RequestCashInOTPResponse, RequestCashInOTPError>> execute(
    RequestCashInOTPRequest request,
  ) {
    final response = RequestCashInOTPResponse(
      otpDetails: OTPConstants.otpValidityDetails,
    );
    final error =
        RequestCashInOTPError.RequestCashInOTPErrorTransactionFeeConfirmationTimeoutError(
          errorCode: "012",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return Future.delayed(1.seconds, () => result);
  }
}
