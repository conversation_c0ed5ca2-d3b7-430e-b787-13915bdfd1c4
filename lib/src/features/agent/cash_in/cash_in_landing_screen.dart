import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_text_input_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/amount_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_number_text_field/phone_number_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/logger.dart';
import 'blocs/cash_in_bloc.dart';

class CashInLandingScreen extends StatefulWidget {
  static const id = "/cash-in-landing-screen";

  const CashInLandingScreen({Key? key}) : super(key: key);

  @override
  State<CashInLandingScreen> createState() => _CashInLandingScreenState();
}

class _CashInLandingScreenState extends State<CashInLandingScreen> {
  late final _cashInBloc = BlocProvider.of<CashInBloc>(context);
  final GlobalKey<FormState> _cashInDetailsFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateCashInDetails = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.depositToOwnWalletTitle,
      ctaWidget: _buildCTA(context),
      padding: commonScreenPadding.copyWith(top: dimenForty),
      child: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Form(
      key: _cashInDetailsFormKey,
      autovalidateMode: _shouldValidateCashInDetails,
      child: Column(
        children: [
          PhoneNumberTextField(
            onPhoneNumberValidated: (phoneNumber) {
              l.d("Cash In: Enter Phone Number $phoneNumber");
              _cashInBloc.add(CashInEvent.addPhoneNumber(phoneNumber));
            },
          ),
          verticalGapSixteen,
          AmountTextField(
            amountTextInputFormatter: AmountTextInputFormatter(
              currency: _cashInBloc.defaultCurrency,
            ),
            onChanged: (amount) {
              l.d("Cash In: Enter Amount $amount");
              _cashInBloc.add(CashInEvent.addAmount(amount));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCTA(BuildContext context) {
    return PrimaryButton(
      labelText: context.localizations.continueText,
      onPressed: () {
        l.v(
          "Cash In Form State validated: ${(_cashInDetailsFormKey.currentState?.validate() ?? false)}",
        );
        if (!(_cashInDetailsFormKey.currentState?.validate() ?? false)) {
          setState(() {
            l.v("Setting Auto validate to onUserInteraction");
            _shouldValidateCashInDetails = AutovalidateMode.onUserInteraction;
          });
          return;
        }
        _cashInBloc.add(CashInEvent.createCashInRequest(context));
      },
    );
  }
}

class CashInScreenArguments {
  final Currency defaultCurrency;
  final Coordinate shopCoordinate;

  CashInScreenArguments({
    required this.defaultCurrency,
    required this.shopCoordinate,
  });
}
