import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/phone_number_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../widgets/otp_widget.dart';
import '../../../widgets/primary_button.dart';
import '../../../widgets/user_details_card.dart';
import 'blocs/cash_in_bloc.dart';
import 'blocs/cash_in_otp_bloc.dart';

class CashInOTPScreen extends StatefulWidget {
  static const id = "/cash-in-otp-screen";

  const CashInOTPScreen({Key? key}) : super(key: key);

  @override
  State<CashInOTPScreen> createState() => _CashInOTPScreenState();
}

class _CashInOTPScreenState extends State<CashInOTPScreen> {
  late final _cashInBloc = BlocProvider.of<CashInBloc>(context);
  late final _cashInOtpBloc = BlocProvider.of<CashInOTPBloc>(context);

  late final _userInfo = _cashInBloc.createCashInResponse.bcnRecipient;
  late final _response = _cashInBloc.createCashInResponse;

  final GlobalKey<FormState> _otpFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateCashInDetails = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.depositToOwnWalletTitle,
      isCancellable: true,
      ctaWidget: _buildCTA(),
      padding: EdgeInsets.zero,
      child: _getFormBody(context),
    );
  }

  Widget _buildCTA() {
    return PrimaryButton(
      labelText: context.localizations.attemptTransfer,
      onPressed: () {
        if (!(_otpFormKey.currentState?.validate() ?? false)) {
          setState(() {
            _shouldValidateCashInDetails = AutovalidateMode.onUserInteraction;
          });
          return;
        }
        _cashInOtpBloc.add(
          CashInOTPEvent.attemptTransfer(
            context,
            _response.recordId,
            _cashInBloc.transactionAmount!,
          ),
        );
      },
    );
  }

  Widget _getFormBody(BuildContext context) {
    final formattedPhoneNumber = _getPhoneNumberString(_cashInBloc.phoneNumber);
    return Column(
      children: [
        verticalGapEight,
        TitleWidget(title: context.localizations.userDetails),
        Padding(
          padding: verticalPaddingSixteen,
          child: UserDetailsCard(
            image: _userInfo.profileImage,
            name: _userInfo.displayName,
            subTitle: formattedPhoneNumber!,
          ),
        ),
        TransactionDetailsWidget(
          transactionAmountLabel: context.localizations.depositAmount,
          transactionAmount: _cashInBloc.transactionAmount!,
          transactionFee: _response.claimedTransactionFee,
          receivingAmountLabel: context.localizations.customerCredit,
          receivingAmount: _cashInBloc.receivingAmount!,
        ),
        TitleWidget(title: context.localizations.otpVerification),
        Form(
          key: _otpFormKey,
          autovalidateMode: _shouldValidateCashInDetails,
          child: Padding(
            padding: allPaddingSixteen,
            child: OTPWidget(
              otpValidityDetails: _cashInBloc.otpValidityDetails,
              onChanged: (otp) {
                _cashInOtpBloc.add(CashInOTPEvent.addOtp(otp));
              },
              onResend: () async {
                _cashInOtpBloc.add(
                  CashInOTPEvent.resendOTP(
                    context,
                    _cashInBloc.createCashInResponse.recordId,
                  ),
                );

                /// Wait For Bloc to send some State.
                final latestState = await _cashInOtpBloc.stream.firstOrNull;
                return switch (latestState) {
                  OTPResentSuccessfully(:final resendDetails) => resendDetails,
                  _ => null,
                };
              },
            ),
          ),
        ),
        verticalGapEight,
      ],
    );
  }

  String? _getPhoneNumberString(String? phoneNumber) {
    if (phoneNumber != null) {
      return PhoneNumberFormatter.format(phoneNumber);
    } else {
      return phoneNumber;
    }
  }
}
