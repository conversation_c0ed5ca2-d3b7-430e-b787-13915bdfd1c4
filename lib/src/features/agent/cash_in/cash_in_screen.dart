import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/route_generator.dart';
import '../../../helpers/helpers.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/nested_navigator_observer.dart';
import 'blocs/cash_in_otp_bloc.dart';
import 'cash_in_landing_screen.dart';
import 'cash_in_otp_screen.dart';

/// A Nested Navigator CashIn Flow screen.
/// All screens will come under this Navigator and
/// routes for the same will be added in [Navigator.onGenerateRoute]
/// See this for more info: https://docs.flutter.dev/cookbook/effects/nested-nav
class CashInScreen extends StatelessWidget {
  static const id = "/cash-in";

  const CashInScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final nestedNavigationObserver = NestedNavigatorObserver();
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }
        final NavigatorState navigator = Navigator.of(context);
        if (isCurrentRouteInitialRoute(
          nestedNavigationObserver,
          CashInLandingScreen.id,
        )) {
          navigator.pop();
          return;
        }
        final bool? shouldPop = await AgencyAppDialog.showCancelConfirmation(
          context,
          context.localizations.transaction,
        );
        if (shouldPop ?? false) {
          navigator.pop();
          return;
        }
      },
      child: Navigator(
        onGenerateRoute: (settings) {
          switch (settings.name) {
            case CashInLandingScreen.id:
              return _getCashInLandingScreenRoute();
            case CashInOTPScreen.id:
              return Routes.getMaterialRoute(
                CashInOTPScreen.id,
                BlocProvider(
                  create: (context) => CashInOTPBloc(),
                  child: const CashInOTPScreen(),
                ),
              );
            default:
              return _getCashInLandingScreenRoute();
          }
        },
        initialRoute: CashInLandingScreen.id,
        observers: [nestedNavigationObserver],
      ),
    );
  }

  MaterialPageRoute<dynamic> _getCashInLandingScreenRoute() {
    return Routes.getMaterialRoute(
      CashInLandingScreen.id,
      const CashInLandingScreen(),
    );
  }
}
