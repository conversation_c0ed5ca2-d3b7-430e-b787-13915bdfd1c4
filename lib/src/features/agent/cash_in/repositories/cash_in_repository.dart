import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_in/mock_impls/cash_in_request.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../core/auth/rpc_auth_provider.dart';
import '../../../../core/logger.dart';
import '../../../../core/service_locator.dart';
import '../mock_impls/confirm_cash_in_request.dart';
import '../mock_impls/request_otp.dart';
import '../mock_impls/resend_cash_in_otp.dart';

class CashInRepository {
  Future<LeoRPCResult<CreateCashInRequestResponse, CreateCashInRequestError>>
  createCashInRequest(LeoPhoneNumber phoneNumber, Amount amount) async {
    final request = CreateCashInRequestRequest(
      phoneNumber: phoneNumber,
      amount: amount,
    );
    l.d("Creating Cash In Request $request");
    final CreateCashInRequestRPC impl =
        currentFlavor.isMock
            ? MockCreateCashInRequest()
            : CreateCashInRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<LeoRPCResult<RequestCashInOTPResponse, RequestCashInOTPError>>
  requestOTP(LeoUUID recordId) async {
    final request = RequestCashInOTPRequest(recordId: recordId);
    l.d("Request Cash In OTP $request");
    final RequestCashInOTPRPC impl =
        currentFlavor.isMock
            ? MockRequestCashInOTPRPC()
            : RequestCashInOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<LeoRPCResult<ResendCashInOTPResponse, ResendCashInOTPError>> resendOTP(
    LeoUUID recordId,
  ) async {
    final request = ResendCashInOTPRequest(recordId: recordId);
    l.d("Resend Cash In OTP $request");
    final ResendCashInOTPRPC impl =
        currentFlavor.isMock
            ? MockResendCashInOTP()
            : ResendCashInOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<LeoRPCResult<ConfirmCashInRequestResponse, ConfirmCashInRequestError>>
  confirmCashInRequest({required Otp otp, required LeoUUID recordId}) async {
    final request = ConfirmCashInRequestRequest(recordId: recordId, otp: otp);
    l.d("Confirm Cash In Request: $request");
    final ConfirmCashInRequestRPC impl =
        currentFlavor.isMock
            ? MockConfirmCashInRequest()
            : ConfirmCashInRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
