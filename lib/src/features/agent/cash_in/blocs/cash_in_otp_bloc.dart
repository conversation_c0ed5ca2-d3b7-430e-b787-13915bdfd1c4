import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../core/location_services/location_service.dart';
import '../../../../core/logger.dart';
import '../../../../helpers/helpers.dart';
import '../../../../helpers/rpc_handler.dart';
import '../../../../models/location_details.dart';
import '../../../../models/transaction_success_screen_arguments.dart';
import '../../../../widgets/transaction_success_screen.dart';
import '../repositories/cash_in_repository.dart';
import 'cash_in_bloc.dart';

part 'cash_in_otp_bloc.freezed.dart';

class CashInOTPBloc extends Bloc<CashInOTPEvent, CashInOTPState>
    with RPCHandler {
  String? _otp;
  final CashInRepository _cashInRepository = CashInRepository();
  final LocationService _locationService = locator<LocationService>();

  CashInOTPBloc() : super(const Initial()) {
    on<CashInOTPEvent>((event, emit) async {
      switch (event) {
        case AddOTP(:final otp):
          if (state is OTPLoading) return;
          _otp = otp;
        case AttemptTransfer(:final context, :final recordId, :final amount):
          l.d('''Confirming Cash In Request 
          recordId: $recordId,
          amount: $amount
          ''');
          await _attemptCashInRequest(context, emit, recordId, amount);
        case ResendOTP(:final context, :final recordId):
          if (state is OTPLoading) return;
          await _resendOTP(context, emit, recordId);
      }
    });
  }

  Future<void> _attemptCashInRequest(
    BuildContext context,
    Emitter<CashInOTPState> emit,
    LeoUUID recordId,
    Amount amount,
  ) async {
    if (context.mounted) {
      AgencyAppDialog.showSpinnerDialog(context);
    }
    emit(const CashInOTPState.loading());
    if (context.mounted && !_isAgentLocationValid(context, emit)) return;
    l.d("Location Valid");
    await rpcHandler(
      () async {
        final result = await _cashInRepository.confirmCashInRequest(
          otp: Otp(otp: _otp!),
          recordId: recordId,
        );
        l.d("Confirm Cash In Request Result $result");
        if (context.mounted) {
          _handleConfirmCashInRequestResult(
            context,
            emit,
            result,
            recordId,
            amount,
          );
        }
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        emit(const CashInOTPState.transientError());
      },
    );
  }

  Future<void> _resendOTP(
    BuildContext context,
    Emitter<CashInOTPState> emit,
    LeoUUID recordId,
  ) async {
    l.d("ResendOTP Cash In Request for recordId: $recordId");
    if (!_isAgentLocationValid(context, emit)) return;
    await rpcHandler(
      () async {
        final result = await _cashInRepository.resendOTP(recordId);
        l.d("Resend OTP Result: $result");
        if (context.mounted) {
          await _handleResendOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        emit(const CashInOTPState.transientError());
      },
    );
  }

  Future<void> _handleResendOTPResult(
    BuildContext context,
    Emitter<CashInOTPState> emit,
    LeoRPCResult<ResendCashInOTPResponse, ResendCashInOTPError> result,
  ) async {
    l.v("Handling Resend OTP Result");
    result.when(
      response: (response) {
        l.d("Resend Cash In OTP Response: $response");
        emit(CashInOTPState.otpResentSuccessfully(response.otpDetails));
      },
      error: (error) {
        l.d("Resend Cash In OTP error: $error");
        emit(const CashInOTPState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          waitForResend: (_) => ProminentErrorHandler.waitForOTPResend(),
          tooManyResendRequests: (_) {
            ProminentErrorHandler.tooManyResendRequests();
          },
          inactiveRecipient: (_) {
            ProminentErrorHandler.inactiveRecipient();
          },
          cashInConfirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.cashIn,
            );
          },
          otpNeverSent: (e) => throw DeveloperError(e.toString()),
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
        );
      },
    );
  }

  void _handleConfirmCashInRequestResult(
    BuildContext context,
    Emitter<CashInOTPState> emit,
    LeoRPCResult<ConfirmCashInRequestResponse, ConfirmCashInRequestError>
    result,
    LeoUUID recordId,
    Amount amount,
  ) {
    l.v("Handling Confirm Cash In Request");
    result.when(
      response: (response) {
        l.d("Response in Confirm Cash In Request: $response");
        context.rootNavigator.pop();
        context.rootNavigator.pushNamed(
          TransactionSuccessScreen.id,
          arguments: TransactionSuccessScreenArguments(
            transactionStatusDetail: response.transactionDetail,
            succeededAt: response.succeededAt,
            recordId: recordId.uuid,
            amount: amount,
          ),
        );
      },
      error: (error) {
        l.d("Error in Confirm Cash In Request: $error");
        context.rootNavigator.pop();
        emit(const CashInOTPState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          otpExpired: (_) {
            ProminentErrorHandler.otpExpired();
          },
          incorrectOtp: (error) {
            ProminentErrorHandler.incorrectOTP(
              numberOfValidationsAttemptsLeft:
                  error.numberOfValidationAttemptsLeft,
              featureName: context.localizations.cashIn,
            );
          },
          tooManyOtpAttempts: (_) {
            ProminentErrorHandler.tooManyOTPRequests();
          },
          inactiveRecipient: (_) {
            ProminentErrorHandler.inactiveRecipient();
          },
          periodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.periodicRequestTransactionLimitExceeded();
          },
          monetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.monetaryTransactionLimitExceededErrorMessage();
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          insufficientBalance: (e) {
            ProminentErrorHandler.inSufficientBalance(
              onOkay: navigateToHomeScreen,
              buttonText: context.localizations.okay,
              transactionFee: e.transactionFee,
            );
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          cashInConfirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.cashIn,
            );
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
        );
      },
    );
  }

  /// Checks whether agent location is near to their shop location.
  /// if not returns false and shows appropriate dialog.
  bool _isAgentLocationValid(
    BuildContext context,
    Emitter<CashInOTPState> emit,
  ) {
    l.v("Checking for location");
    final cashInBloc = BlocProvider.of<CashInBloc>(context);
    final LocationDetails locationDetails =
        cashInBloc.shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    l.v("IsLocationValid $isAgentLocationValid");
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const CashInOTPState.prominentError());
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class CashInOTPState with _$CashInOTPState {
  const factory CashInOTPState.initial() = Initial;

  const factory CashInOTPState.loading() = OTPLoading;

  const factory CashInOTPState.prominentError() = ProminentError;

  const factory CashInOTPState.transientError() = TransientError;

  const factory CashInOTPState.otpResentSuccessfully(
    OTPResendDetails resendDetails,
  ) = OTPResentSuccessfully;

  const factory CashInOTPState.cashInSuccessful() = CashInSuccessful;
}

@freezed
sealed class CashInOTPEvent with _$CashInOTPEvent {
  const factory CashInOTPEvent.addOtp(String otp) = AddOTP;

  const factory CashInOTPEvent.attemptTransfer(
    BuildContext context,
    LeoUUID recordId,
    Amount amount,
  ) = AttemptTransfer;

  const factory CashInOTPEvent.resendOTP(
    BuildContext context,
    LeoUUID recordId,
  ) = ResendOTP;
}
