import 'dart:async';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/session_pin/enter_session_pin/enter_session_pin_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/phone_number_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_phone_number.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../core/location_services/location_service.dart';
import '../../../../core/logger.dart';
import '../../../../core/service_locator.dart';
import '../../../../helpers/rpc_handler.dart';
import '../../../../models/location_details.dart';
import '../cash_in_otp_screen.dart';
import '../repositories/cash_in_repository.dart';

part 'cash_in_bloc.freezed.dart';

class CashInBloc extends Bloc<CashInEvent, CashInState> with RPCHandler {
  String? _phoneNumber;
  Amount? _amount;
  Amount? _receivingAmount;
  final Coordinate shopCoordinate;
  final Currency defaultCurrency;
  CreateCashInRequestResponse? _createCashInRequestResponse;
  OTPValidityDetails? _otpValidityDetails;
  final CashInRepository _cashInRepository = CashInRepository();
  final _locationService = locator<LocationService>();

  CashInBloc({required this.shopCoordinate, required this.defaultCurrency})
    : super(const CashInState.initial()) {
    on<CashInEvent>((event, emit) async {
      switch (event) {
        case AddPhoneNumber(:final phoneNumber):
          // Don't take up the data if current state is Loading
          if (state is Loading) return;
          _phoneNumber = phoneNumber;
        case AddAmount(:final amount):
          // Don't take up the data if current state is Loading
          if (state is Loading) return;
          _amount = amount;
        case CreateCashInRequest(:final context):
          await _createCashInRequest(emit, context);
      }
    });
  }

  String? get phoneNumber => _phoneNumber;

  Amount? get transactionAmount => _amount;

  Amount? get receivingAmount => _receivingAmount;

  CreateCashInRequestResponse get createCashInResponse =>
      _createCashInRequestResponse!;

  OTPValidityDetails get otpValidityDetails => _otpValidityDetails!;

  Future<void> _createCashInRequest(
    Emitter<CashInState> emit,
    BuildContext context,
  ) async {
    final LocationDetails locationDetails = shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const CashInState.prominentError());
      return;
    }
    final isAgentAuthenticated =
        await context.rootNavigator.pushNamed(EnterSessionPinScreen.id)
            as bool?;
    if (!(isAgentAuthenticated ?? false)) {
      return;
    }
    if (context.mounted) {
      l.d('''Creating Cash In Request: 
        phoneNumber: $phoneNumber
        amount: $transactionAmount
      ''');
      emit(const CashInState.loading());
      l.v("Showing Loader Dialog");
      _showLoaderDialog(context);
      await rpcHandler(
        () async {
          final rpcResult = await _cashInRepository.createCashInRequest(
            LeoPhoneNumber(_phoneNumber!),
            _amount!,
          );
          l.v("rpcResult for Create Cash In request $rpcResult");
          if (context.mounted) {
            await _handleCreateCashInRequestRPCResult(context, rpcResult, emit);
          }
        },
        onTransientError: (_) {
          l.v("Emitting Transient Error");
          context.rootNavigator.pop();
          emit(const CashInState.transientError());
        },
      );
    }
  }

  void _showLoaderDialog(BuildContext context) {
    final formattedPhoneNumber = PhoneNumberFormatter.format(_phoneNumber!);
    AgencyAppDialog.showSpinnerDialog(
      context,
      contentText: context.localizations.lookingUpDetailsPhoneNumber(
        formattedPhoneNumber,
      ),
    );
  }

  Future<void> _handleCreateCashInRequestRPCResult(
    BuildContext context,
    LeoRPCResult<CreateCashInRequestResponse, CreateCashInRequestError>
    rpcResult,
    Emitter<CashInState> emit,
  ) async {
    // Pop the loader dialog.
    context.rootNavigator.pop();
    await rpcResult.when(
      response: (response) async {
        l.d("Create Cash In Request response: $response");
        if (response.receivingAmount != null) {
          l.e("response.receivingAmount should not be null");
          ProminentErrorHandler.exchangeRateNotSupported();
        }
        emit(const CashInState.createdCashInRequest());
        _createCashInRequestResponse = response;
        _receivingAmount =
            response.receivingAmount ??
            AmountFormatter.getDifferenceBetweenAmounts(
              _amount!,
              response.claimedTransactionFee,
            );
        final bool? shouldProceed = await TransactionDialog.show(
          context: context,
          transactionAmountLabel: context.localizations.depositAmount,
          transactionAmount: _amount!,
          transactionFee: response.claimedTransactionFee,
          receivingAmountLabel: context.localizations.customerCredit,
          receivingAmount: _receivingAmount!,
          additionalDetailLabel: context.localizations.customer,
          additionalDetailValue: response.bcnRecipient.displayName,
        );
        l.d("Should Proceed Cash In Request: $shouldProceed");
        if (shouldProceed == true) {
          emit(const CashInState.loading());
          if (context.mounted) {
            await _requestOTP(context, emit, response.recordId);
          }
        }
      },
      error: (error) {
        l.d("Got an error while creating cash in request: $error");
        emit(const CashInState.prominentError());
        error.when(
          phoneNumberUnknown: (_) {
            ProminentErrorHandler.phoneNumberUnknown(_phoneNumber!);
          },
          currencyMismatchForAgentAccountAndAmount:
              (e) => throw DeveloperError(e.toString()),
          periodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.periodicRequestTransactionLimitExceeded();
          },
          monetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.monetaryTransactionLimitExceededErrorMessage();
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          amountTooLess: (error) {
            ProminentErrorHandler.amountTooLess(error.minimumAllowedAmount);
          },
          amountTooLarge: (error) {
            ProminentErrorHandler.amountTooHigh(error.maximumAllowedAmount);
          },
          insufficientBalance: (e) {
            ProminentErrorHandler.inSufficientBalance(
              transactionFee: e.transactionFee,
            );
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          recipientCannotBeSelf: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.cashInRecipientCannotBeSelf,
            );
          },
          recipientCannotBeAgent: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.cashInRecipientCannotBeAgent,
            );
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
        );
      },
    );
  }

  Future<void> _requestOTP(
    BuildContext context,
    Emitter<CashInState> emit,
    LeoUUID recordId,
  ) async {
    l.v("Requesting OTP");
    final LocationDetails locationDetails = shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    l.v("isAgentLocationValid $isAgentLocationValid");
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const CashInState.prominentError());
      return;
    }
    AgencyAppDialog.showSpinnerDialog(context);
    await rpcHandler(
      () async {
        final result = await _cashInRepository.requestOTP(recordId);
        l.d("Request Cash In OTP result: $result");
        if (context.mounted) {
          _handlerRequestOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        l.v("Emitting Transient Error");
        context.rootNavigator.pop();
        emit(const CashInState.transientError());
      },
    );
  }

  Future<void> _handlerRequestOTPResult(
    BuildContext context,
    Emitter<CashInState> emit,
    LeoRPCResult<RequestCashInOTPResponse, RequestCashInOTPError> result,
  ) async {
    // Pop the loader dialog.
    context.rootNavigator.pop();
    result.when(
      response: (response) {
        l.d("OTP Details Request Cash IN OTP: $response");
        _otpValidityDetails = response.otpDetails;
        context.navigator.pushNamed(CashInOTPScreen.id);
      },
      error: (error) {
        l.d("Error while requesting OTP: $error");
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          transactionFeeConfirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.cashIn,
            );
          },
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          tooManyOtpRequests: (_) {
            ProminentErrorHandler.tooManyOTPRequests();
          },
          inactiveRecipient: (_) {
            ProminentErrorHandler.inactiveRecipient();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
        );
      },
    );
  }
}

@freezed
sealed class CashInState with _$CashInState {
  const factory CashInState.initial() = Initial;

  const factory CashInState.loading() = Loading;

  const factory CashInState.prominentError() = ProminentError;

  const factory CashInState.transientError() = TransientError;

  const factory CashInState.createdCashInRequest() = CreatedCashInRequest;
}

@freezed
sealed class CashInEvent with _$CashInEvent {
  const factory CashInEvent.addPhoneNumber(String? phoneNumber) =
      AddPhoneNumber;

  const factory CashInEvent.addAmount(Amount? amount) = AddAmount;

  const factory CashInEvent.createCashInRequest(BuildContext context) =
      CreateCashInRequest;
}
