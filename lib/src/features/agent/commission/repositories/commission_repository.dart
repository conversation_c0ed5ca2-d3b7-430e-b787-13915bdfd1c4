import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/mock_impls/mock_get_accrued_commission_transactions.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/mock_impls/mock_get_completed_commission_transactions.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/mock_impls/mock_get_completed_transaction_breakdown.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import '../../../../core/auth/rpc_auth_provider.dart';
import '../../../../core/service_locator.dart';

class CommissionRepository {
  Future<LeoRPCResult<GetAccruedCommissionTransactionsResponse, Never>>
  getAccruedCommissionTransactions() async {
    final request = GetAccruedCommissionTransactionsRequest();
    final GetAccruedCommissionTransactionsRPC impl =
        currentFlavor.isMock
            ? MockGetAccruedCommissionTransactionsRPCImpl()
            : GetAccruedCommissionTransactionsRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<LeoRPCResult<GetCommissionTransactionsResponse, Never>>
  getCompletedCommissionTransactions() async {
    final request = GetCommissionTransactionsRequest();
    final GetCommissionTransactionsRPC impl =
        currentFlavor.isMock
            ? MockGetCompletedCommissionTransactionsRPCImpl()
            : GetCommissionTransactionsRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      GetCommissionTransactionBreakdownResponse,
      GetCommissionTransactionBreakdownError
    >
  >
  getCommissionTransactionBreakdown(LeoUUID recordId) async {
    final request = GetCommissionTransactionBreakdownRequest(
      recordId: recordId,
    );
    final GetCommissionTransactionBreakdownRPC impl =
        currentFlavor.isMock
            ? MockGetCommissionTransactionBreakdownRPCImpl()
            : GetCommissionTransactionBreakdownRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
