import 'package:bcn_agency_banking_flutter/src/features/agent/commission/accrued_commissions_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/blocs/accrued_commission_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/blocs/completed_commission_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/completed_commissions_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/widgets/commissions_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CommissionPage extends StatelessWidget {
  static const id = "/commission-page";

  const CommissionPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: const CommissionsAppBar(),
        body: _buildBody(context),
      ),
    );
  }

  TabBarView _buildBody(BuildContext context) {
    return TabBarView(
      children: [
        BlocProvider(
          create:
              (context) =>
                  AccruedCommissionBloc()
                    ..add(const AccruedCommissionEvent.fetchCommissionList()),
          child: const AccruedCommissionsTabPage(),
        ),
        BlocProvider(
          create:
              (context) =>
                  CompletedCommissionBloc()
                    ..add(const CompletedCommissionEvent.fetchCommissionList()),
          child: const CompletedCommissionsTabPage(),
        ),
      ],
    );
  }
}
