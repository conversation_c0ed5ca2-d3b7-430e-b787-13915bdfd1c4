import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetCommissionTransactionBreakdownRPCImpl
    extends GetCommissionTransactionBreakdownRPC {
  @override
  Future<
    LeoRPCResult<
      GetCommissionTransactionBreakdownResponse,
      GetCommissionTransactionBreakdownError
    >
  >
  execute(GetCommissionTransactionBreakdownRequest request) {
    final response = GetCommissionTransactionBreakdownResponse(
      commissionTransactionList: [
        CashTransaction(
          recordId: LeoUUID("0015642a-5fbc-47cf-ae32-7af6daf99565"),
          amount: Amount(
            amount: ********,
            currency: Currency(currencyCode: 'MWK'),
          ),
          transactionFee: Amount(
            amount: 900000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          commissionEarned: Amount(
            amount: 400000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          succeededAt: DateTime.now(),
          title: LocalizedText(en: "Cash In"),
          image: LocalizedImage(
            en: ThemedImage(dark: mockImage, light: mockImage),
          ),
          transactionDetail: TransactionStatusDetail(
            itemDetail: [
              TransactionStatusItemDetail(
                label: LocalizedText(en: "Label 6"),
                valueType:
                    TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                      title: 'title',
                      image: LocalizedImage(
                        en: ThemedImage(dark: mockImage, light: mockImage),
                      ),
                    ),
              ),
            ],
          ),
        ),
        CashTransaction(
          recordId: LeoUUID("253a28aa-8c25-44d8-a1a1-9c69f90c13e4"),
          amount: Amount(
            amount: 12000000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          transactionFee: Amount(
            amount: 900000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          commissionEarned: Amount(
            amount: 300000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          succeededAt: DateTime.now(),
          title: LocalizedText(en: "Cash Out"),
          image: LocalizedImage(
            en: ThemedImage(dark: mockImage, light: mockImage),
          ),
          transactionDetail: TransactionStatusDetail(
            itemDetail: [
              TransactionStatusItemDetail(
                label: LocalizedText(en: "Label 7"),
                valueType:
                    TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                      title: 'title',
                      image: LocalizedImage(
                        en: ThemedImage(dark: mockImage, light: mockImage),
                      ),
                    ),
              ),
            ],
          ),
        ),
        CashTransaction(
          recordId: LeoUUID("91834e1d-a6f4-4929-a5a6-dc096d575bbc"),
          amount: Amount(
            amount: 13000000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          transactionFee: Amount(
            amount: 900000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          commissionEarned: Amount(
            amount: 300000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          succeededAt: DateTime(2022, 12, 1),
          title: LocalizedText(en: "Money Transfer"),
          image: LocalizedImage(
            en: ThemedImage(dark: mockImage, light: mockImage),
          ),
          transactionDetail: TransactionStatusDetail(
            itemDetail: [
              TransactionStatusItemDetail(
                label: LocalizedText(en: "Label 8"),
                valueType:
                    TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                      title: 'title',
                      image: LocalizedImage(
                        en: ThemedImage(dark: mockImage, light: mockImage),
                      ),
                    ),
              ),
            ],
          ),
        ),
        CashTransaction(
          recordId: LeoUUID("cd63ae92-deec-45c1-b804-1e63fdf93f0e"),
          amount: Amount(
            amount: 14000000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          transactionFee: Amount(
            amount: 900000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          commissionEarned: Amount(
            amount: 300000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          succeededAt: DateTime(2021, 8, 6),
          title: LocalizedText(en: "Cash In"),
          image: LocalizedImage(
            en: ThemedImage(dark: mockImage, light: mockImage),
          ),
          transactionDetail: TransactionStatusDetail(
            itemDetail: [
              TransactionStatusItemDetail(
                label: LocalizedText(en: "Label 9"),
                valueType:
                    TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                      title: 'title',
                      image: LocalizedImage(
                        en: ThemedImage(dark: mockImage, light: mockImage),
                      ),
                    ),
              ),
            ],
          ),
        ),
        CashTransaction(
          recordId: LeoUUID("821bf916-df52-43bf-9708-87040df772cf"),
          amount: Amount(
            amount: 15000000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          transactionFee: Amount(
            amount: 900000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          commissionEarned: Amount(
            amount: 300000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          succeededAt: DateTime(2020, 8, 6, 3, 13),
          title: LocalizedText(en: "Cash In"),
          image: LocalizedImage(
            en: ThemedImage(dark: mockImage, light: mockImage),
          ),
          transactionDetail: TransactionStatusDetail(
            itemDetail: [
              TransactionStatusItemDetail(
                label: LocalizedText(en: "Label 10"),
                valueType:
                    TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                      title: 'title',
                      image: LocalizedImage(
                        en: ThemedImage(dark: mockImage, light: mockImage),
                      ),
                    ),
              ),
            ],
          ),
        ),
      ],
    );
    final error =
        GetCommissionTransactionBreakdownError.GetCommissionTransactionBreakdownErrorInvalidRecordIdError(
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return Future.delayed(1.seconds, () => result);
  }
}
