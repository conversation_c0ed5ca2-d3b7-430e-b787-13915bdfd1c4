import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetCompletedCommissionTransactionsRPCImpl
    extends GetCommissionTransactionsRPC {
  @override
  Future<LeoRPCResult<GetCommissionTransactionsResponse, Never>> execute(
    GetCommissionTransactionsRequest request,
  ) {
    final response =
        LeoRPCResult<GetCommissionTransactionsResponse, Never>.response(
          GetCommissionTransactionsResponse(
            commissionTransactions: [
              CommissionTransaction(
                recordId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
                amount: Amount(
                  amount: 800000,
                  currency: Currency(currencyCode: 'MWK'),
                ),
                succeededAt: DateTime.now(),
                title: LocalizedText(en: "Commission Transferred"),
              ),
              CommissionTransaction(
                recordId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
                amount: Amount(
                  amount: 32000000,
                  currency: Currency(currencyCode: 'MWK'),
                ),
                succeededAt: DateTime.now(),
                title: LocalizedText(en: "Commission Transferred"),
              ),
              CommissionTransaction(
                recordId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
                amount: Amount(
                  amount: 1500000,
                  currency: Currency(currencyCode: 'MWK'),
                ),
                succeededAt: DateTime.now(),
                title: LocalizedText(en: "Commission Transferred"),
              ),
            ],
          ),
        );
    return Future.delayed(1.seconds, () => response);
  }
}
