import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetAccruedCommissionTransactionsRPCImpl
    extends GetAccruedCommissionTransactionsRPC {
  @override
  Future<LeoRPCResult<GetAccruedCommissionTransactionsResponse, Never>> execute(
    GetAccruedCommissionTransactionsRequest request,
  ) {
    final response = LeoRPCResult<
      GetAccruedCommissionTransactionsResponse,
      Never
    >.response(
      GetAccruedCommissionTransactionsResponse(
        accruedCommissionTransactions: [
          CashTransaction(
            recordId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
            amount: Amount(
              amount: ********,
              currency: Currency(currencyCode: 'MWK'),
            ),
            transactionFee: Amount(
              amount: 900000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            commissionEarned: Amount(
              amount: 300000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            succeededAt: DateTime.now(),
            title: LocalizedText(en: "Cash In"),
            image: LocalizedImage(
              en: ThemedImage(dark: mockImage, light: mockImage),
            ),
            transactionDetail: TransactionStatusDetail(
              itemDetail: [
                TransactionStatusItemDetail(
                  label: LocalizedText(en: "Label 1"),
                  valueType:
                      TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                        title: 'title',
                        image: LocalizedImage(
                          en: ThemedImage(dark: mockImage, light: mockImage),
                        ),
                      ),
                ),
              ],
            ),
          ),
          CashTransaction(
            recordId: LeoUUID("cf85993a-55ca-4df2-a36f-a9ca6704030a"),
            amount: Amount(
              amount: 20000000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            transactionFee: Amount(
              amount: 900000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            commissionEarned: Amount(
              amount: 300000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            succeededAt: DateTime.now(),
            title: LocalizedText(en: "Cash Out"),
            image: LocalizedImage(
              en: ThemedImage(dark: mockImage, light: mockImage),
            ),
            transactionDetail: TransactionStatusDetail(
              itemDetail: [
                TransactionStatusItemDetail(
                  label: LocalizedText(en: "Label 2"),
                  valueType:
                      TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                        title: 'title',
                        image: LocalizedImage(
                          en: ThemedImage(dark: mockImage, light: mockImage),
                        ),
                      ),
                ),
              ],
            ),
          ),
          CashTransaction(
            recordId: LeoUUID("e274dfb9-5177-4ddf-8551-59506a60de25"),
            amount: Amount(
              amount: 30000000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            transactionFee: Amount(
              amount: 900000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            commissionEarned: Amount(
              amount: 300000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            succeededAt: DateTime.now().subtract(const Duration(days: 380)),
            title: LocalizedText(en: "Money Transfer"),
            image: LocalizedImage(
              en: ThemedImage(dark: mockImage, light: mockImage),
            ),
            transactionDetail: TransactionStatusDetail(
              itemDetail: [
                TransactionStatusItemDetail(
                  label: LocalizedText(en: "Label 3"),
                  valueType:
                      TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                        title: 'title',
                        image: LocalizedImage(
                          en: ThemedImage(dark: mockImage, light: mockImage),
                        ),
                      ),
                ),
              ],
            ),
          ),
          CashTransaction(
            recordId: LeoUUID("85938afc-25bd-4e20-babd-da0b64d298ff"),
            amount: Amount(
              amount: 40000000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            transactionFee: Amount(
              amount: 900000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            commissionEarned: Amount(
              amount: 300000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            succeededAt: DateTime.now().subtract(const Duration(days: 480)),
            title: LocalizedText(en: "Cash In"),
            image: LocalizedImage(
              en: ThemedImage(dark: mockImage, light: mockImage),
            ),
            transactionDetail: TransactionStatusDetail(
              itemDetail: [
                TransactionStatusItemDetail(
                  label: LocalizedText(en: "Label 4"),
                  valueType:
                      TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                        title: 'title',
                        image: LocalizedImage(
                          en: ThemedImage(dark: mockImage, light: mockImage),
                        ),
                      ),
                ),
              ],
            ),
          ),
          CashTransaction(
            recordId: LeoUUID("ecdcb157-ee09-4b1f-a0c6-bd44340d4a26"),
            amount: Amount(
              amount: 50000000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            transactionFee: Amount(
              amount: 900000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            commissionEarned: Amount(
              amount: 300000,
              currency: Currency(currencyCode: 'MWK'),
            ),
            succeededAt: DateTime(2020, 8, 6, 3, 13),
            title: LocalizedText(en: "Cash In"),
            image: LocalizedImage(
              en: ThemedImage(dark: mockImage, light: mockImage),
            ),
            transactionDetail: TransactionStatusDetail(
              itemDetail: [
                TransactionStatusItemDetail(
                  label: LocalizedText(en: "Label 5"),
                  valueType:
                      TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                        title: 'title',
                        image: LocalizedImage(
                          en: ThemedImage(dark: mockImage, light: mockImage),
                        ),
                      ),
                ),
              ],
            ),
          ),
        ],
        totalAccruedCommission: TotalAccruedCommission(
          totalAccruedCommission: Amount(
            amount: ********0,
            currency: Currency(currencyCode: 'MWK'),
          ),
          nextCommissionTransferredOn: DateTime.now().add(
            const Duration(days: 3),
          ),
        ),
      ),
    );

    return Future.delayed(1.seconds, () => response);
  }
}
