import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:flutter/material.dart';

class DateSeparator extends StatelessWidget {
  final DateTime date;

  const DateSeparator({Key? key, required this.date}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    late final String dayText;
    if (date.isToday) {
      dayText = context.localizations.today;
    } else if (date.isYesterday) {
      dayText = context.localizations.yesterday;
    } else if (date.isWithinAWeek) {
      dayText = date.getWeekdayInString(context);
    } else {
      dayText = DateFormatter.dobFormat().format(date.toLocal());
    }
    return TitleWidget(title: dayText);
  }
}
