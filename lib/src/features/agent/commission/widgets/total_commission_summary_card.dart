import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class TotalCommissionSummaryCard extends StatelessWidget {
  final Amount totalCommission;
  final DateTime transferDate;

  const TotalCommissionSummaryCard({
    Key? key,
    required this.totalCommission,
    required this.transferDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: commonScreenPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.localizations.totalCommissionAccruedThisCycleTillDate,
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.neutralShade7Color,
            ),
          ),
          verticalGapEight,
          Text(
            totalCommission.localisedFormattedAmount,
            style: context.appTextStyles.welcomeHeadingBold.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
          ),
          verticalGapEight,
          Text(
            context.localizations.commissionTransferHelper(
              getDaysLeftFromTodayString(context, transferDate),
            ),
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.neutralShade7Color,
            ),
          ),
        ],
      ),
    );
  }
}
