import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class CommissionsAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CommissionsAppBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      centerTitle: false,
      // Add default AppBar elevation.
      elevation: dimenFour,
      title: Text(
        context.localizations.commissionEarned,
        overflow: TextOverflow.ellipsis,
      ),
      automaticallyImplyLeading: false,
      bottom: TabBar(
        indicatorColor: context.appColors.genericWhiteColor,
        indicator: const UnderlineTabIndicator(),
        unselectedLabelColor: context.appColors.tabBarUnselectedLabelColor,
        tabs: [
          Tab(text: context.localizations.accrued),
          Tab(text: context.localizations.completed),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(104);
}
