import 'package:agency_banking_rpcs/agency/commission_transaction_type.dart';
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:flutter/material.dart';

/// The card is used to display the summary and commission
/// of a completed transaction.
class CompletedCommissionSummaryCard extends StatelessWidget {
  final CommissionTransaction commissionTransaction;
  final VoidCallback onTap;

  /// A padding of 8px is added at the top of the card if the card is the first
  /// [CompletedCommissionSummaryCard] in the list.
  final bool isFirstIndex;

  const CompletedCommissionSummaryCard({
    Key? key,
    required this.commissionTransaction,
    required this.onTap,
    this.isFirstIndex = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: isFirstIndex ? dimenEight : dimenZero),
      child: InkWell(
        onTap: onTap,
        child: Ink(
          padding: const EdgeInsets.symmetric(
            vertical: dimenTwelve,
            horizontal: dimenSixteen,
          ),
          color: context.appColors.backgroundColor,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _getTransactionDetails(context),
              horizontalGapFour,
              Text(
                DateFormatter.dobFormat().format(
                  commissionTransaction.succeededAt.toLocal(),
                ),
                style: context.appTextStyles.smallText2.copyWith(
                  color: context.appColors.neutralShade2Color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _getTransactionDetails(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            commissionTransaction.amount.localisedFormattedAmount,
            style: context.appTextStyles.labelText2Bold.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
          ),
          verticalGapFour,
          Text(
            BCNLocale.getLocalisedString(context, commissionTransaction.title),
            style: context.appTextStyles.labelText3.copyWith(
              color: context.appColors.neutralShade7Color,
            ),
          ),
        ],
      ),
    );
  }
}
