import 'package:agency_banking_rpcs/agency/cash_transaction_type.dart';
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

/// Card used to display transaction details on [AccruedCommissionsTabPage].
class IndividualTransactionSummaryCard extends StatelessWidget {
  /// Details of the transaction to be displayed.
  final CashTransaction cashTransaction;

  /// The callback function when the card is tapped.
  final VoidCallback onTap;

  const IndividualTransactionSummaryCard({
    Key? key,
    required this.cashTransaction,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Ink(
        padding: allPaddingSixteen,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _getTransactionDetails(context),
            horizontalGapFour,
            Text(
              DateFormatter.transactionClockTimeFormat(
                context,
              ).format(cashTransaction.succeededAt.toLocal()),
              style: context.appTextStyles.smallText2.copyWith(
                color: context.appColors.neutralShade2Color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Expanded _getTransactionDetails(BuildContext context) {
    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CachedNetworkImage(
            imageUrl: BCNLocale.getLocalisedImageUrl(
              context,
              cashTransaction.image,
            ),
            imageBuilder:
                (_, imageProvider) => Container(
                  width: dimenForty,
                  height: dimenForty,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
            placeholder: (_, __) => _getLoaderWidget(context),
            errorWidget: (_, __, ___) => _imageErrorPlaceholder(context),
          ),
          horizontalGapSixteen,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cashTransaction.commissionEarned.localisedFormattedAmount,
                  style: context.appTextStyles.smallText1Bold.copyWith(
                    color: context.appColors.neutralShade1Color,
                  ),
                ),
                verticalGapFour,
                Text(
                  BCNLocale.getLocalisedString(context, cashTransaction.title),
                  style: _secondaryTextStyle(context),
                ),
                verticalGapFour,
                Text(
                  '${context.localizations.amountTransferred}: '
                  '${cashTransaction.amount.localisedFormattedAmount}',
                  style: _secondaryTextStyle(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Container _getCircularContainer(BuildContext context, Widget child) {
    return Container(
      height: dimenForty,
      width: dimenForty,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: context.appColors.neutralShade4Color,
      ),
      child: child,
    );
  }

  Widget _getLoaderWidget(BuildContext context) {
    return _getCircularContainer(
      context,
      const Center(
        child: SizedBox(
          height: dimenTwenty,
          width: dimenTwenty,
          child: Spinner(strokeWidth: dimenTwo),
        ),
      ),
    );
  }

  Widget _imageErrorPlaceholder(BuildContext context) {
    return _getCircularContainer(
      context,
      IconWidget(
        assetName: ABAssets.failedToFetchIcon,
        height: dimenTwenty,
        width: dimenTwenty,
      ),
    );
  }

  TextStyle _secondaryTextStyle(BuildContext context) {
    return context.appTextStyles.smallText2.copyWith(
      color: context.appColors.neutralShade7Color,
    );
  }
}
