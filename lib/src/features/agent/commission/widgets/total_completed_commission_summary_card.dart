import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TotalCompletedCommissionSummaryCard extends StatelessWidget {
  final Amount totalCommission;
  final DateTime payOutDate;

  const TotalCompletedCommissionSummaryCard({
    Key? key,
    required this.totalCommission,
    required this.payOutDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final DateFormat payOutDateFormatter = DateFormatter.dobFormat();
    return Padding(
      padding: allPaddingSixteen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            totalCommission.localisedFormattedAmount,
            style: context.appTextStyles.welcomeHeadingBold.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
          ),
          verticalGapEight,
          Text(
            context.localizations.totalCommissionEarnedBetween(
              payOutDateFormatter.format(payOutDate.toLocal().firstDayOfCycle),
              payOutDateFormatter.format(payOutDate.toLocal()),
            ),
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.neutralShade7Color,
            ),
          ),
        ],
      ),
    );
  }
}
