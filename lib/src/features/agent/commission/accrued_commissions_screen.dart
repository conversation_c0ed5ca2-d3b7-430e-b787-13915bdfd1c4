import 'package:agency_banking_rpcs/agency/cash_transaction_type.dart';
import 'package:agency_banking_rpcs/agency/total_accrued_commission_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/blocs/accrued_commission_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/widgets/date_separator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/widgets/individual_transaction_summary_card.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/widgets/total_commission_summary_card.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/generic_error_or_empty_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/pull_to_refresh_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../core/logger.dart';
import '../../../utils/constants.dart';

class AccruedCommissionsTabPage extends StatefulWidget {
  const AccruedCommissionsTabPage({Key? key}) : super(key: key);

  @override
  State<AccruedCommissionsTabPage> createState() =>
      _AccruedCommissionsTabPageState();
}

class _AccruedCommissionsTabPageState extends State<AccruedCommissionsTabPage>
    with AutomaticKeepAliveClientMixin {
  static const id = 'accrued-commission-screen';
  late final AccruedCommissionBloc _accruedCommissionBloc =
      BlocProvider.of<AccruedCommissionBloc>(context);

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return VisibilityDetector(
      key: const Key(id),
      onVisibilityChanged: (visibilityInfo) {
        l.v("Visibility Changed: $visibilityInfo");
        final visiblePercentage = visibilityInfo.visibleFraction * 100;
        if (visiblePercentage == maxVisibilityPercentage) {
          _accruedCommissionBloc.add(
            const AccruedCommissionEvent.fetchCommissionList(),
          );
        }
      },
      child: Scaffold(body: _buildBody(context)),
    );
  }

  BlocBuilder _buildBody(BuildContext context) {
    return BlocBuilder<AccruedCommissionBloc, AccruedCommissionState>(
      buildWhen: (_, AccruedCommissionState currentState) {
        return currentState is! RefreshInProgress;
      },
      builder: (BuildContext context, AccruedCommissionState state) {
        if (state is FetchedCommissionList) {
          _accruedCommissionBloc.refreshController.refreshCompleted();
          return PullToRefreshWidget(
            onRefresh: _onRefresh,
            controller: _accruedCommissionBloc.refreshController,
            child:
                state.accruedCommissionTransactions.isEmpty
                    ? GenericErrorOrEmptyWidget(
                      labelText: context.localizations.noTransactionsYet,
                      iconAssetPath: ABAssets.exchangeAltIcon,
                    )
                    : _getRequestList(
                      state.totalAccruedCommission,
                      state.accruedCommissionTransactions,
                      state.accruedCommissionTransactionsDateIndexMap,
                    ),
          );
        } else if (state is TransientError) {
          return NoInternetWidget(
            onRetryButtonClicked:
                () => _accruedCommissionBloc.add(
                  const AccruedCommissionEvent.fetchCommissionList(),
                ),
          );
        } else {
          return const Spinner();
        }
      },
    );
  }

  void _onRefresh() {
    _accruedCommissionBloc.add(const AccruedCommissionEvent.pullToRefresh());
  }

  Widget _getRequestList(
    TotalAccruedCommission totalAccruedCommission,
    List<CashTransaction> accruedCommissionTransactions,
    Map<DateTime, int> accruedCommissionTransactionsDateIndexMap,
  ) {
    return ListView.separated(
      // Item count is set as the length of the transactions list plus 1 to
      // accommodate the summary card.
      itemCount: accruedCommissionTransactions.length + 1,
      separatorBuilder: (BuildContext context, int index) {
        final bool shouldDisplayDateAtThisIndex =
            accruedCommissionTransactionsDateIndexMap.containsValue(index);
        if (!shouldDisplayDateAtThisIndex) {
          return const Divider(height: dimenOne, thickness: dividerThickness);
        }
        // Get the date to be displayed by the current position
        final DateTime date = accruedCommissionTransactionsDateIndexMap.keys
            .firstWhere(
              (key) => accruedCommissionTransactionsDateIndexMap[key] == index,
            );
        return Padding(
          padding:
              index > 1
                  ? verticalPaddingEight
                  : const EdgeInsets.only(bottom: dimenEight),
          child: DateSeparator(date: date),
        );
      },
      itemBuilder: (context, cardIndex) {
        //First element will be the total commission summary card
        if (cardIndex == 0) {
          return TotalCommissionSummaryCard(
            totalCommission: totalAccruedCommission.totalAccruedCommission,
            transferDate: totalAccruedCommission.nextCommissionTransferredOn,
          );
        } else {
          // The first index is given to TotalCommissionSummaryCard that is
          // constantly present. Therefore in this block of code
          // we always operate over the `1...n` elements of the listView, which
          // is the commissions list, however, the list still works over
          // `0...n-1` indexing.
          final commissionsListIndex = cardIndex - 1;
          return IndividualTransactionSummaryCard(
            cashTransaction:
                accruedCommissionTransactions[commissionsListIndex],
            onTap: () {
              _accruedCommissionBloc.add(
                AccruedCommissionEvent.tappedTransactionCard(
                  context,
                  commissionsListIndex,
                ),
              );
            },
          );
        }
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
