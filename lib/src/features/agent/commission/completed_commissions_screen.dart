import 'package:agency_banking_rpcs/agency/commission_transaction_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/blocs/completed_commission_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/commissions_breakdown_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/widgets/completed_commission_summary_card.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/generic_error_or_empty_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/pull_to_refresh_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CompletedCommissionsTabPage extends StatefulWidget {
  const CompletedCommissionsTabPage({Key? key}) : super(key: key);

  @override
  State<CompletedCommissionsTabPage> createState() =>
      _CompletedCommissionsTabPageState();
}

class _CompletedCommissionsTabPageState
    extends State<CompletedCommissionsTabPage>
    with AutomaticKeepAliveClientMixin {
  late final CompletedCommissionBloc _completedCommissionBloc =
      BlocProvider.of<CompletedCommissionBloc>(context);

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(body: _buildBody(context));
  }

  BlocBuilder _buildBody(BuildContext context) {
    return BlocBuilder<CompletedCommissionBloc, CompletedCommissionState>(
      bloc: _completedCommissionBloc,
      buildWhen: (_, CompletedCommissionState currentState) {
        return currentState is! RefreshInProgress;
      },
      builder: (BuildContext context, CompletedCommissionState state) {
        if (state is FetchedCommissionList) {
          _completedCommissionBloc.refreshController.refreshCompleted();
          return PullToRefreshWidget(
            onRefresh: _onRefresh,
            controller: _completedCommissionBloc.refreshController,
            child:
                state.commissionTransactions.isEmpty
                    ? GenericErrorOrEmptyWidget(
                      labelText: context.localizations.cycleNotCompletedYet,
                      iconAssetPath: ABAssets.stopwatchSlashIcon,
                    )
                    : _getRequestList(state.commissionTransactions),
          );
        } else if (state is TransientError) {
          return NoInternetWidget(
            onRetryButtonClicked:
                () => _completedCommissionBloc.add(
                  const CompletedCommissionEvent.fetchCommissionList(),
                ),
          );
        } else {
          return const Spinner();
        }
      },
    );
  }

  void _onRefresh() {
    _completedCommissionBloc.add(
      const CompletedCommissionEvent.pullToRefresh(),
    );
  }

  Widget _getRequestList(List<CommissionTransaction> commissionTransactions) {
    return ListView.builder(
      itemCount: commissionTransactions.length,
      itemBuilder: (_, index) {
        return CompletedCommissionSummaryCard(
          commissionTransaction: commissionTransactions[index],
          isFirstIndex: index == 0,
          onTap: () async {
            final CommissionTransaction commissionTransaction =
                _completedCommissionBloc
                    .getCommissionTransactionsResponse
                    .commissionTransactions[index];
            _completedCommissionBloc.currentTotalCommissionAmount =
                commissionTransaction.amount;
            _completedCommissionBloc.currentCyclePayoutDate =
                commissionTransaction.succeededAt;
            _completedCommissionBloc.currentCompletedTransactionRecordId =
                commissionTransaction.recordId;
            await context.navigator.pushNamed(
              CommissionsBreakdownScreen.id,
              arguments: _completedCommissionBloc,
            );
            _completedCommissionBloc.refreshController.requestRefresh();
          },
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
