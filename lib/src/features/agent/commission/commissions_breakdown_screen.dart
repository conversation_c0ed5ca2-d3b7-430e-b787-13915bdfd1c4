import 'package:agency_banking_rpcs/agency/cash_transaction_type.dart';
import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/blocs/commissions_breakdown_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/blocs/completed_commission_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/widgets/date_separator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/widgets/individual_transaction_summary_card.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/widgets/total_completed_commission_summary_card.dart';
import 'package:bcn_agency_banking_flutter/src/models/transaction_success_screen_arguments.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/pull_to_refresh_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_success_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CommissionsBreakdownScreen extends StatefulWidget {
  static const id = "/commissions-breakdown-screen";

  const CommissionsBreakdownScreen({Key? key}) : super(key: key);

  @override
  State<CommissionsBreakdownScreen> createState() =>
      _CommissionsBreakdownScreenState();
}

class _CommissionsBreakdownScreenState
    extends State<CommissionsBreakdownScreen> {
  late final CompletedCommissionBloc _completedCommissionBloc =
      BlocProvider.of<CompletedCommissionBloc>(context);
  late final CommissionsBreakdownBloc _commissionBreakdownBloc =
      BlocProvider.of<CommissionsBreakdownBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.commissionDetails),
      body: _buildBody(context),
    );
  }

  BlocBuilder _buildBody(BuildContext context) {
    return BlocBuilder<CommissionsBreakdownBloc, CommissionsBreakdownState>(
      buildWhen: (_, CommissionsBreakdownState currentState) {
        return currentState is! BreakdownRefreshInProgress;
      },
      builder: (BuildContext context, CommissionsBreakdownState state) {
        if (state is FetchedBreakdownList) {
          return _getRequestList(
            state.breakdownTransactionList,
            state.totalCommission,
            state.completedCommissionTransactionsBreakdownDateIndexMap,
          );
        } else if (state is TransientError) {
          return NoInternetWidget(
            onRetryButtonClicked:
                () => _commissionBreakdownBloc.add(
                  CommissionsBreakdownEvent.fetchBreakdownList(
                    _completedCommissionBloc
                        .currentCompletedTransactionRecordId,
                    _completedCommissionBloc.currentTotalCommissionAmount,
                  ),
                ),
          );
        } else {
          return const Spinner();
        }
      },
    );
  }

  void _onRefresh() {
    _commissionBreakdownBloc.add(
      CommissionsBreakdownEvent.breakdownPullToRefresh(
        _completedCommissionBloc.currentCompletedTransactionRecordId,
        _completedCommissionBloc.currentTotalCommissionAmount,
      ),
    );
  }

  Widget _getRequestList(
    List<CashTransaction> breakdownCommissionTransactions,
    Amount totalCommission,
    Map<DateTime, int> breakdownCommissionTransactionsDateIndexMap,
  ) {
    return PullToRefreshWidget(
      onRefresh: _onRefresh,
      controller: _commissionBreakdownBloc.refreshController,
      child: ListView.separated(
        // Item count is set as the length of the transactions list plus 1 to
        // accommodate the summary card.
        itemCount: breakdownCommissionTransactions.length + 1,
        separatorBuilder: (BuildContext context, int index) {
          final bool shouldDisplayDateAtThisIndex =
              breakdownCommissionTransactionsDateIndexMap.containsValue(index);
          if (!shouldDisplayDateAtThisIndex) {
            return const Divider(height: dimenOne, thickness: dividerThickness);
          }
          // Get the date to be displayed by the current position
          final DateTime date = breakdownCommissionTransactionsDateIndexMap.keys
              .firstWhere(
                (key) =>
                    breakdownCommissionTransactionsDateIndexMap[key] == index,
              );
          return Padding(
            padding:
                index > 1
                    ? verticalPaddingEight
                    : const EdgeInsets.only(bottom: dimenEight),
            child: DateSeparator(date: date),
          );
        },
        itemBuilder: (context, cardIndex) {
          //First element will be the total commission summary card
          if (cardIndex == 0) {
            return TotalCompletedCommissionSummaryCard(
              totalCommission: totalCommission,
              payOutDate: _completedCommissionBloc.currentCyclePayoutDate,
            );
          } else {
            // The first index is given to TotalCommissionSummaryCard that is
            // constantly present. Therefore in this block of code
            // we always operate over the `1...n` elements of the listView, which
            // is the breakdown commissions list, however, the list still works
            // over '0...n-1` indexing.
            final commissionsListIndex = cardIndex - 1;
            return IndividualTransactionSummaryCard(
              cashTransaction:
                  breakdownCommissionTransactions[commissionsListIndex],
              onTap: () async {
                final CashTransaction cashTransaction =
                    _commissionBreakdownBloc
                        .getCommissionTransactionBreakdownResponse
                        .commissionTransactionList[commissionsListIndex];
                await context.rootNavigator.pushNamed(
                  TransactionSuccessScreen.id,
                  arguments: TransactionSuccessScreenArguments(
                    transactionStatusDetail: cashTransaction.transactionDetail,
                    succeededAt: cashTransaction.succeededAt,
                    recordId: cashTransaction.recordId.uuid,
                    amount: cashTransaction.amount,
                    onDone: context.navigator.pop,
                  ),
                );
                _refreshScreen();
              },
            );
          }
        },
      ),
    );
  }

  void _refreshScreen() {
    if (!mounted) return;
    _commissionBreakdownBloc.refreshController.requestRefresh();
  }
}
