import 'package:agency_banking_rpcs/agency/cash_transaction_type.dart';
import 'package:agency_banking_rpcs/agency/get_commission_transaction_breakdown_rpc.dart';
import 'package:agency_banking_rpcs/agency/get_commission_transactions_rpc.dart';
import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/repositories/commission_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../core/logger.dart';

part 'commissions_breakdown_bloc.freezed.dart';

class CommissionsBreakdownBloc
    extends Bloc<CommissionsBreakdownEvent, CommissionsBreakdownState>
    with RPCHandler {
  final refreshController = RefreshController();
  final CommissionRepository _commissionRepository = CommissionRepository();

  late GetCommissionTransactionsResponse _getCommissionTransactionsResponse;
  late GetCommissionTransactionBreakdownResponse
  _getCommissionTransactionBreakdownResponse;

  GetCommissionTransactionsResponse get getCommissionTransactionsResponse =>
      _getCommissionTransactionsResponse;

  GetCommissionTransactionBreakdownResponse
  get getCommissionTransactionBreakdownResponse =>
      _getCommissionTransactionBreakdownResponse;

  CommissionsBreakdownBloc() : super(const Initial()) {
    on<CommissionsBreakdownEvent>((event, emit) async {
      switch (event) {
        case FetchBreakdownList(
          :final completedTransactionRecordId,
          :final totalCommissionAmount,
        ):
          emit(const CommissionsBreakdownState.breakdownLoading());
          await _fetchCommissionBreakdownList(
            emit,
            completedTransactionRecordId,
            totalCommissionAmount,
          );
        case BreakdownPullToRefresh(
          :final completedTransactionRecordId,
          :final totalCommissionAmount,
        ):
          l.v("Called Pull to refresh for Commission breakdown");
          emit(const CommissionsBreakdownState.breakdownRefreshInProgress());
          await _fetchCommissionBreakdownList(
            emit,
            completedTransactionRecordId,
            totalCommissionAmount,
          );
      }
    });
  }

  Future<void> _fetchCommissionBreakdownList(
    Emitter<CommissionsBreakdownState> emit,
    LeoUUID recordId,
    Amount totalCommissionAmount,
  ) async {
    l.d('''Fetching Commission Breakdown list. 
    recordId $recordId
    totalCommissionAmount: $totalCommissionAmount''');
    await rpcHandler(
      () async {
        final rpcResult = await _commissionRepository
            .getCommissionTransactionBreakdown(recordId);
        l.d("Commission Breakdown List RPC result: $rpcResult");
        rpcResult.when(
          response: (response) {
            _getCommissionTransactionBreakdownResponse = response;
            final Map<DateTime, int>
            commissionTransactionsBreakdownDateIndexMap =
                _getDateSeparatedCashTransactionsDateIndexMap(
                  response.commissionTransactionList,
                );
            emit(
              CommissionsBreakdownState.fetchedBreakdownList(
                response.commissionTransactionList,
                totalCommissionAmount,
                commissionTransactionsBreakdownDateIndexMap,
              ),
            );
            refreshController.refreshCompleted();
          },
          error: (error) {
            refreshController.refreshFailed();
            emit(const CommissionsBreakdownState.breakdownProminentError());
            error.when(
              invalidRecordId: (e) => throw DeveloperError(e.toString()),
            );
          },
        );
      },
      onTransientError: (_) {
        l.d("Emitting Transient error");
        refreshController.refreshFailed();
        emit(const CommissionsBreakdownState.breakdownTransientError());
      },
    );
  }

  // To determine where all the dates should be displayed in the UI.
  // A date will only be added to the map if it is not already present
  // and at the first position it comes.
  Map<DateTime, int> _getDateSeparatedCashTransactionsDateIndexMap(
    List<CashTransaction> breakdownCommissionTransactions,
  ) {
    final Map<DateTime, int> breakdownCommissionTransactionsDateIndexMap = {};
    for (
      int index = 0;
      index < breakdownCommissionTransactions.length;
      index++
    ) {
      final CashTransaction transaction =
          breakdownCommissionTransactions[index];
      //If date is not in the map then add it.
      if (!breakdownCommissionTransactionsDateIndexMap.containsKey(
        transaction.succeededAt.onlyDate,
      )) {
        breakdownCommissionTransactionsDateIndexMap[transaction
                .succeededAt
                .onlyDate] =
            index;
      }
    }
    return breakdownCommissionTransactionsDateIndexMap;
  }
}

@freezed
sealed class CommissionsBreakdownState with _$CommissionsBreakdownState {
  const factory CommissionsBreakdownState.initial() = Initial;

  const factory CommissionsBreakdownState.breakdownLoading() = BreakdownLoading;

  const factory CommissionsBreakdownState.breakdownProminentError() =
      BreakdownProminentError;

  const factory CommissionsBreakdownState.breakdownTransientError() =
      BreakdownTransientError;

  const factory CommissionsBreakdownState.fetchedBreakdownList(
    List<CashTransaction> breakdownTransactionList,
    Amount totalCommission,
    Map<DateTime, int> completedCommissionTransactionsBreakdownDateIndexMap,
  ) = FetchedBreakdownList;

  const factory CommissionsBreakdownState.breakdownRefreshInProgress() =
      BreakdownRefreshInProgress;
}

@freezed
sealed class CommissionsBreakdownEvent with _$CommissionsBreakdownEvent {
  const factory CommissionsBreakdownEvent.fetchBreakdownList(
    LeoUUID completedTransactionRecordId,
    Amount totalCommissionAmount,
  ) = FetchBreakdownList;

  const factory CommissionsBreakdownEvent.breakdownPullToRefresh(
    LeoUUID completedTransactionRecordId,
    Amount totalCommissionAmount,
  ) = BreakdownPullToRefresh;
}
