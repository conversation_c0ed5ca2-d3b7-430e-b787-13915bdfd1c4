import 'package:agency_banking_rpcs/agency/commission_transaction_type.dart';
import 'package:agency_banking_rpcs/agency/get_commission_transaction_breakdown_rpc.dart';
import 'package:agency_banking_rpcs/agency/get_commission_transactions_rpc.dart';
import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/repositories/commission_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../core/logger.dart';

part 'completed_commission_bloc.freezed.dart';

class CompletedCommissionBloc
    extends Bloc<CompletedCommissionEvent, CompletedCommissionState>
    with R<PERSON><PERSON>andler {
  final CommissionRepository _commissionRepository = CommissionRepository();
  final RefreshController refreshController = RefreshController();

  late GetCommissionTransactionsResponse _getCommissionTransactionsResponse;
  late GetCommissionTransactionBreakdownResponse
  _getCommissionTransactionBreakdownResponse;

  late Amount currentTotalCommissionAmount;
  late LeoUUID currentCompletedTransactionRecordId;
  late DateTime currentCyclePayoutDate;

  GetCommissionTransactionsResponse get getCommissionTransactionsResponse =>
      _getCommissionTransactionsResponse;

  GetCommissionTransactionBreakdownResponse
  get getCommissionTransactionBreakdownResponse =>
      _getCommissionTransactionBreakdownResponse;

  CompletedCommissionBloc() : super(const Initial()) {
    on<CompletedCommissionEvent>((event, emit) async {
      switch (event) {
        case FetchCommissionList():
          emit(const CompletedCommissionState.loading());
          await _fetchCompletedCommissionList(emit);
        case PullToRefresh():
          l.v("Called Pull to refresh");
          emit(const CompletedCommissionState.refreshInProgress());
          await _fetchCompletedCommissionList(emit);
      }
    });
  }

  Future<void> _fetchCompletedCommissionList(
    Emitter<CompletedCommissionState> emit,
  ) async {
    l.v("Calling Completed Commission List");
    await rpcHandler(
      () async {
        final rpcResult =
            await _commissionRepository.getCompletedCommissionTransactions();
        l.d("Completed Commission transactions: $rpcResult");
        rpcResult.when(
          response: (response) {
            _getCommissionTransactionsResponse = response;
            emit(
              CompletedCommissionState.fetchedCommissionList(
                response.commissionTransactions,
              ),
            );
          },
          error: (error) {
            // This should never happen since this RPC call does not
            // throw any errors.
            // Therefore, seeing this exception is a developer error.
            throw DeveloperError("Something went wrong: $error");
          },
        );
      },
      onTransientError: (_) {
        refreshController.refreshFailed();
        emit(const CompletedCommissionState.transientError());
      },
      onServerError: () {
        // If the Bloc is closed, don't call the RPC.
        if (isClosed) return;
        refreshController.refreshFailed();
        // If server error exists, retry the RPC call.
        add(const CompletedCommissionEvent.fetchCommissionList());
      },
    );
  }
}

@freezed
sealed class CompletedCommissionState with _$CompletedCommissionState {
  const factory CompletedCommissionState.initial() = Initial;

  const factory CompletedCommissionState.loading() = Loading;

  const factory CompletedCommissionState.prominentError() = ProminentError;

  const factory CompletedCommissionState.transientError() = TransientError;

  const factory CompletedCommissionState.emptyCompletedCommissionsList() =
      EmptyCompletedCommissionsList;

  const factory CompletedCommissionState.fetchedCommissionList(
    List<CommissionTransaction> commissionTransactions,
  ) = FetchedCommissionList;

  const factory CompletedCommissionState.refreshInProgress() =
      RefreshInProgress;
}

@freezed
sealed class CompletedCommissionEvent with _$CompletedCommissionEvent {
  const factory CompletedCommissionEvent.fetchCommissionList() =
      FetchCommissionList;

  const factory CompletedCommissionEvent.pullToRefresh() = PullToRefresh;
}
