import 'package:agency_banking_rpcs/agency/cash_transaction_type.dart';
import 'package:agency_banking_rpcs/agency/get_accrued_commission_transactions_rpc.dart';
import 'package:agency_banking_rpcs/agency/total_accrued_commission_type.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/repositories/commission_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/transaction_success_screen_arguments.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_success_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../core/logger.dart';
import '../../../../utils/constants.dart';

part 'accrued_commission_bloc.freezed.dart';

class AccruedCommissionBloc
    extends Bloc<AccruedCommissionEvent, AccruedCommissionState>
    with RPCHandler {
  final CommissionRepository _commissionRepository = CommissionRepository();
  DateTime? _dataLastUpdated;
  bool _isRPCCallInProgress = false;

  final RefreshController refreshController = RefreshController(
    initialRefresh: false,
  );
  late GetAccruedCommissionTransactionsResponse _response;

  AccruedCommissionBloc() : super(const Initial()) {
    on<AccruedCommissionEvent>((event, emit) async {
      switch (event) {
        case FetchCommissionList():
          // This check prevents the unnecessary RPC call triggered from
          // the `VisibilityDetector` widget when the previous RPC call is already in progress.
          if (_isRPCCallInProgress) return;
          // When there is no internet, state is set to TransientError. When the
          // app is back online, it has to be treated like this is the very
          // first time it's fetching the data, as it can't update an empty state.
          if (_dataLastUpdated == null || state is TransientError) {
            // This is the first time the data is loaded.
            // Thus the loading state is displayed.
            emit(const AccruedCommissionState.loading());
            await _fetchCommissionList(emit);
          } else {
            // Here, we already have the data from the previous RPC call.
            // Thus the pull to refresh loading state is displayed.
            final now = DateTime.now();
            l.d("Data last updated: $_dataLastUpdated");
            final bool shouldFetchData =
                now.difference(_dataLastUpdated!).inSeconds >=
                secondsOfInActivityAllowed;
            l.d("Should Fetch data is $shouldFetchData");
            if (!shouldFetchData) return;
            _requestRefresh();
          }
        case PullToRefresh():
          l.d("Called Pull to reresh in Accrued Commission");
          emit(const AccruedCommissionState.refreshInProgress());
          await _fetchCommissionList(emit);
        case TappedTransactionCard(:final context, :final index):
          final CashTransaction cashTransaction =
              _response.accruedCommissionTransactions[index];
          await context.rootNavigator.pushNamed(
            TransactionSuccessScreen.id,
            arguments: TransactionSuccessScreenArguments(
              transactionStatusDetail: cashTransaction.transactionDetail,
              succeededAt: cashTransaction.succeededAt,
              recordId: cashTransaction.recordId.uuid,
              amount: cashTransaction.amount,
              onDone: context.navigator.pop,
            ),
          );
          emit(const AccruedCommissionState.refreshInProgress());
          _requestRefresh();
      }
    });
  }

  void _requestRefresh() {
    refreshController.requestRefresh(needMove: false);
  }

  Future<void> _fetchCommissionList(
    Emitter<AccruedCommissionState> emit,
  ) async {
    l.v("Fetching Accrued Commission list");
    _isRPCCallInProgress = true;
    await rpcHandler(
      () async {
        final rpcResult =
            await _commissionRepository.getAccruedCommissionTransactions();
        l.d("Accrued Commissions result: $rpcResult");
        rpcResult.when(
          response: (response) {
            _response = response;
            final Map<DateTime, int> accruedCommissionTransactionsDateIndexMap =
                _getDateSeparatedCashTransactionsDateIndexMap(
                  response.accruedCommissionTransactions,
                );
            _dataLastUpdated = DateTime.now();
            l.v("Updated _dataLastUpdated $_dataLastUpdated");
            emit(
              AccruedCommissionState.fetchedCommissionList(
                response.accruedCommissionTransactions,
                response.totalAccruedCommission,
                accruedCommissionTransactionsDateIndexMap,
              ),
            );
          },
          error: (error) {
            // This should never happen since this RPC call does not
            // throw any errors.
            // Therefore, seeing this exception is a developer error.
            throw DeveloperError("Something went wrong: $error");
          },
        );
      },
      onServerError: () {
        // If the Bloc is closed, don't call the RPC.
        if (isClosed) return;
        refreshController.refreshFailed();
        // If server error exists, retry the RPC call.
        if (_dataLastUpdated == null) {
          // if the list is never built.
          // Fetch the accrued commissions.
          add(const AccruedCommissionEvent.fetchCommissionList());
        } else {
          // If accrued commission list is built,
          // call PullToRefresh's `requestRefresh`
          // that will eventually call `pullToRefresh` event.
          refreshController.requestRefresh();
        }
      },
      onTransientError: (_) {
        refreshController.refreshFailed();
        emit(const AccruedCommissionState.transientError());
      },
    );
    _isRPCCallInProgress = false;
  }

  // To determine where all the dates should be displayed in the UI.
  // A date will only be added to the map if it is not already present
  // and at the first position it comes.
  Map<DateTime, int> _getDateSeparatedCashTransactionsDateIndexMap(
    List<CashTransaction> accruedCommissionTransactions,
  ) {
    final Map<DateTime, int> accruedCommissionTransactionsDateIndexMap = {};
    for (int index = 0; index < accruedCommissionTransactions.length; index++) {
      final CashTransaction transaction = accruedCommissionTransactions[index];
      //If date is not in the map then add it
      if (!accruedCommissionTransactionsDateIndexMap.containsKey(
        transaction.succeededAt.onlyDate,
      )) {
        accruedCommissionTransactionsDateIndexMap[transaction
                .succeededAt
                .onlyDate] =
            index;
      }
    }
    return accruedCommissionTransactionsDateIndexMap;
  }
}

@freezed
sealed class AccruedCommissionState with _$AccruedCommissionState {
  const factory AccruedCommissionState.initial() = Initial;

  const factory AccruedCommissionState.loading() = Loading;

  const factory AccruedCommissionState.prominentError() = ProminentError;

  const factory AccruedCommissionState.transientError() = TransientError;

  const factory AccruedCommissionState.emptyAccruedCommissionsList() =
      EmptyAccruedCommissionsList;

  const factory AccruedCommissionState.fetchedCommissionList(
    List<CashTransaction> accruedCommissionTransactions,
    TotalAccruedCommission totalAccruedCommission,
    Map<DateTime, int> accruedCommissionTransactionsDateIndexMap,
  ) = FetchedCommissionList;

  const factory AccruedCommissionState.refreshInProgress() = RefreshInProgress;
}

@freezed
sealed class AccruedCommissionEvent with _$AccruedCommissionEvent {
  const factory AccruedCommissionEvent.fetchCommissionList() =
      FetchCommissionList;

  const factory AccruedCommissionEvent.pullToRefresh() = PullToRefresh;

  const factory AccruedCommissionEvent.tappedTransactionCard(
    BuildContext context,
    int index,
  ) = TappedTransactionCard;
}
