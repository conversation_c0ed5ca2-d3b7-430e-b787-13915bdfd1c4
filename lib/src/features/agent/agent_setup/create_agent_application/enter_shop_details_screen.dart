import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/create_agent_application/bloc/submit_application_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/location_picker_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/view_map_tile.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/reg_exp_helper.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/validators.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/upload_photo_previewer.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../utils/constants.dart';

class EnterShopDetailsScreen extends StatefulWidget {
  static const id = "/enter-shop-details-screen";

  const EnterShopDetailsScreen({Key? key}) : super(key: key);

  @override
  EnterShopDetailsScreenState createState() {
    return EnterShopDetailsScreenState();
  }
}

class EnterShopDetailsScreenState extends State<EnterShopDetailsScreen> {
  late final SubmitApplicationBloc _submitApplicationBloc =
      BlocProvider.of<SubmitApplicationBloc>(context);
  final GlobalKey<FormState> _shopDetailsFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateShopDetails = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SubmitApplicationBloc, SubmitApplicationState>(
      buildWhen: (
        SubmitApplicationState oldState,
        SubmitApplicationState newState,
      ) {
        return newState !=
            const SubmitApplicationState.locationUpdateCompleted();
      },
      builder: (context, state) {
        return CommonScreenLayout(
          padding: EdgeInsets.zero,
          appBarTitle: context.localizations.enterShopDetails,
          disableBackGesture: false,
          ctaWidget: _getSubmitShopDetailsButton(state),
          child: _getBody(),
        );
      },
    );
  }

  Widget _getBody() {
    return Column(
      children: [
        verticalGapEight,
        _getShopNameField(),
        _getLocationAddressTile(),
        UploadPhotoPreviewer(
          title: context.localizations.shopPhotoTitle,
          placeholderText: context.localizations.shopPhoto,
          onPhotoDeleted: () {
            _submitApplicationBloc.add(
              const SubmitApplicationEvent.onShopImageChanged(null),
            );
          },
          onPhotoSelected: (Photo photo) {
            _submitApplicationBloc.add(
              SubmitApplicationEvent.onShopImageChanged(photo),
            );
          },
        ),
        verticalGapTwentyFour,
      ],
    );
  }

  Widget _getShopNameField() {
    return Form(
      key: _shopDetailsFormKey,
      autovalidateMode: _shouldValidateShopDetails,
      child: Column(
        children: [
          TitleWidget(title: context.localizations.basicDetails),
          Padding(
            padding: allPaddingSixteen,
            child: PrimaryTextField(
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  RegExpHelper.alphaNumericPatternWithSpace,
                ),
              ],
              maxLength: shopNameMaxLength,
              validator:
                  (shopName) => Validators.emptyValidator(
                    context,
                    context.localizations.shopName,
                    shopName,
                  ),
              labelText: context.localizations.shopName,
              onChanged: (name) {
                _submitApplicationBloc.add(
                  SubmitApplicationEvent.onShopNameChanged(name),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String? _getLocationLabel() {
    final shopLocation = _submitApplicationBloc.shopLocation;
    if (shopLocation == null) {
      return null;
    } else if (shopLocation.address == null) {
      return "${shopLocation.latitude}, ${shopLocation.longitude}";
    } else {
      return shopLocation.address!.addressLabel;
    }
  }

  String? _getLocationInfo() {
    final shopLocation = _submitApplicationBloc.shopLocation;
    if (shopLocation == null) {
      return null;
    } else if (shopLocation.address == null) {
      return context.localizations.locationSelected;
    } else {
      return shopLocation.address!.addressText;
    }
  }

  Widget _getLocationAddressTile() {
    return ViewMapTile(
      labelText: _getLocationLabel(),
      infoText: _getLocationInfo(),
      labelStyle: context.appTextStyles.labelText2Bold,
      onTap: () async {
        final shopLocationDetails =
            await context.navigator.pushNamed(
                  LocationPickerScreen.id,
                  arguments: _submitApplicationBloc.shopLocation,
                )
                as LocationDetails?;
        if (shopLocationDetails != null) {
          _submitApplicationBloc.add(
            SubmitApplicationEvent.onShopLocationChanged(shopLocationDetails),
          );
        }
      },
    );
  }

  Widget _getSubmitShopDetailsButton(SubmitApplicationState state) {
    return PrimaryButton(
      labelText: context.localizations.submit,
      onPressed: () {
        final isFormValidated =
            _shopDetailsFormKey.currentState?.validate() ?? false;
        if (!isFormValidated) {
          setState(() {
            _shouldValidateShopDetails = AutovalidateMode.onUserInteraction;
          });
          return;
        }
        if (_submitApplicationBloc.shopLocation == null) {
          AgencyAppDialog.showErrorDialog(
            context: context,
            contentText: context.localizations.fieldIsRequired(
              context.localizations.shopLocation,
            ),
          );
          return;
        }
        _submitApplicationBloc.add(
          SubmitApplicationEvent.onSubmitApplication(context),
        );
      },
    );
  }
}
