import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/data_source/document_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/application_status/agent_application_status_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/repository/agent_application_repository.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../../core/logger.dart';

part 'submit_application_bloc.freezed.dart';

class SubmitApplicationBloc
    extends Bloc<SubmitApplicationEvent, SubmitApplicationState>
    with RPCHandler {
  Photo? _shopImage;
  LocationDetails? _shopLocation;
  String? _shopName;
  final DocumentService _documentService = locator<DocumentService>();
  final AgentApplicationSetupRepository _repository =
      AgentApplicationSetupRepository();

  SubmitApplicationBloc() : super(const Initial()) {
    on<SubmitApplicationEvent>((event, emit) async {
      switch (event) {
        case OnShopImageChanged(:final photo):
          _shopImage = photo;
        case OnShopLocationChanged(:final locationDetails):
          _shopLocation = locationDetails;
          emit(const SubmitApplicationState.shopLocationUpdated());
          emit(const SubmitApplicationState.locationUpdateCompleted());
        case OnShopNameChanged(:final name):
          if (name.trim().length <= passwordMaxLength) {
            _shopName = name;
          }
        case OnSubmitApplication(:final context):
          emit(const SubmitApplicationState.loading());
          await _submitAgentApplication(context, emit);
      }
    });
  }

  Future<void> _submitAgentApplication(
    BuildContext context,
    Emitter<SubmitApplicationState> emit,
  ) async {
    l.d('''Submitting Agent application.
    shopName: $_shopName,
    shopLocation: $shopLocation''');
    await rpcHandler(
      () async {
        AgencyAppDialog.showSpinnerDialog(context);
        LeoUUID? imageId;
        if (_shopImage != null) {
          imageId =
              currentFlavor.isMock
                  ? await Future.delayed(
                    1.seconds,
                    () => LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
                  )
                  : await _documentService.uploadPhoto(_shopImage!);
        }
        final result = await _repository.submitApplication(
          Coordinate(
            latitude: _shopLocation!.latitude,
            longitude: _shopLocation!.longitude,
          ),
          _shopName!,
          imageId,
        );
        if (context.mounted) {
          await _handleSubmitApplicationResult(emit, result, context);
        }
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        emit(const SubmitApplicationState.transientError());
      },
    );
  }

  Future<void> _handleSubmitApplicationResult(
    Emitter<SubmitApplicationState> emit,
    LeoRPCResult<SubmitApplicationDataResponse, SubmitApplicationDataError>
    result,
    BuildContext context,
  ) async {
    context.rootNavigator.pop();
    result.when(
      response: (response) {
        AgencyAppDialog.showAppDialog(
          context: context,
          contentText: context.localizations.applicationSubmitted,
          actions:
              (dialogContext) => [
                PrimaryTextButton(
                  text: context.localizations.okay,
                  onTap: () {
                    dialogContext.navigator.pop();
                    _navigateToApplicationStatusScreen(context);
                  },
                ),
              ],
        );
      },
      error: (error) {
        emit(const SubmitApplicationState.prominentError());
        error.when(
          invalidShopImageId: (_) => ProminentErrorHandler.invalidShopImage(),
          phoneNumberRegisteredAsAgentManager: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText:
                  context
                      .localizations
                      .userAlreadyRegisteredAsAgentManagerError,
              isDismissible: false,
              onOkay: (_) => forceSignOutUser(context),
            );
          },
          applicationSubmissionLimitExceeded: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText:
                  context.localizations.applicationSubmissionLimitExceeded,
              isDismissible: false,
              onOkay: _navigateToApplicationStatusScreen,
            );
          },
          applicationAlreadyApproved: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.applicationAlreadyApproved,
              isDismissible: false,
              onOkay: _navigateToApplicationStatusScreen,
            );
          },
          applicationAlreadySubmitted: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.applicationAlreadySubmitted,
              isDismissible: false,
              onOkay: _navigateToApplicationStatusScreen,
            );
          },
        );
      },
    );
  }

  void _navigateToApplicationStatusScreen(BuildContext context) =>
      context.rootNavigator.pop(AgentApplicationStatusScreen.id);

  LocationDetails? get shopLocation => _shopLocation;
}

@freezed
sealed class SubmitApplicationState with _$SubmitApplicationState {
  const factory SubmitApplicationState.initial() = Initial;

  const factory SubmitApplicationState.loading() = Loading;

  const factory SubmitApplicationState.prominentError() = ProminentError;

  const factory SubmitApplicationState.transientError() = TransientError;

  const factory SubmitApplicationState.invalidShopName() = ButtonEnabled;

  const factory SubmitApplicationState.shopLocationUpdated() =
      ShopLocationUpdated;

  const factory SubmitApplicationState.locationUpdateCompleted() =
      LocationUpdateCompleted;
}

@freezed
sealed class SubmitApplicationEvent with _$SubmitApplicationEvent {
  const factory SubmitApplicationEvent.onShopImageChanged(Photo? photo) =
      OnShopImageChanged;

  const factory SubmitApplicationEvent.onShopLocationChanged(
    LocationDetails locationDetails,
  ) = OnShopLocationChanged;

  const factory SubmitApplicationEvent.onShopNameChanged(String name) =
      OnShopNameChanged;

  const factory SubmitApplicationEvent.onSubmitApplication(
    BuildContext context,
  ) = OnSubmitApplication;
}
