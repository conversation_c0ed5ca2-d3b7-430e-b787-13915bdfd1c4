import 'dart:io';

import 'package:agency_banking_rpcs/agency/get_application_status_rpc.dart';
import 'package:agency_banking_rpcs/agency/rejected_application_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/no_internet_snackbar_with_retry.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../../core/logger.dart';
import '../../../../session_pin/setup_session_pin/setup_session_pin_screen.dart';
import '../../repository/agent_application_repository.dart';

part 'agent_application_bloc.freezed.dart';

class AgentApplicationBloc
    extends Bloc<AgentApplicationEvent, AgentApplicationState>
    with RPCHandler {
  final _repository = AgentApplicationSetupRepository();
  final refreshController = RefreshController();
  List<RejectedApplication>? _rejectedApplications;

  AgentApplicationBloc() : super(const AgentApplicationState.loading()) {
    on<AgentApplicationEvent>((event, emit) async {
      switch (event) {
        case GetApplicationStatus(:final context):
          emit(const AgentApplicationState.loading());
          await _getApplicationStatus(
            context,
            emit,
            isAskingForFirstTime: true,
          );

        case PullToRefresh(:final context):
          l.v("Called Pull to refresh");
          emit(const AgentApplicationState.pullToRefreshLoading());
          await _getApplicationStatus(
            context,
            emit,
            isAskingForFirstTime: false,
          );
      }
    });
  }

  List<RejectedApplication>? get rejectedApplications => _rejectedApplications;

  Future<void> _getApplicationStatus(
    BuildContext context,
    Emitter<AgentApplicationState> emit, {
    required bool isAskingForFirstTime,
  }) async {
    l.d("Getting Application status.");
    l.d("IsAskingForFirstTime: $isAskingForFirstTime");
    await rpcHandler(
      () async {
        final rpcResult = await _repository.getApplicationStatus();
        l.d("Agent Application Status RPC Result: $rpcResult");
        if (context.mounted) {
          await _handleRPCResult(
            rpcResult,
            emit,
            context,
            isAskingForFirstTime,
          );
        }
      },
      onTransientError: (_) {
        refreshController.refreshFailed();
        _rejectedApplications = null;
        emit(const AgentApplicationState.transientError());
        showNoInternetSnackBarWithRetry(context, () {
          add(AgentApplicationEvent.getApplicationStatus(context));
        });
      },
      onServerError: () async {
        await AgencyAppDialog.showErrorDialog(
          context: context,
          contentText: context.localizations.somethingWentWrong,
        );
        // If the Bloc is closed, don't call the RPC.
        if (isClosed) return;
        refreshController.refreshFailed();
        // If server error exists, retry the RPC call.
        refreshController.requestRefresh();
      },
      shouldHandleTransientError: false,
    );
  }

  Future<void> _handleRPCResult(
    LeoRPCResult<GetApplicationStatusResponse, GetApplicationStatusError>
    rpcResult,
    Emitter<AgentApplicationState> emit,
    BuildContext context,
    bool isAskingForFirstTime,
  ) async {
    await rpcResult.when(
      response: (response) async {
        refreshController.refreshCompleted();
        await response.result.when(
          applicationNotSubmitted: (applicationNotSubmittedResponse) {
            _rejectedApplications =
                applicationNotSubmittedResponse.rejectedApplications;
            final isApplicationLimitExceeded =
                applicationNotSubmittedResponse
                    .isApplicationSubmissionLimitExceeded;
            emit(
              AgentApplicationState.applicationNotSubmitted(
                isApplicationLimitExceeded,
              ),
            );
          },
          applicationSubmitted: (applicationSubmittedResponse) {
            _rejectedApplications =
                applicationSubmittedResponse.rejectedApplications;
            emit(const AgentApplicationState.applicationSubmitted());
          },
          applicationApproved: (_) async {
            // If Asking for the agent application status for the first time,
            // no need to show an application approved dialog,
            // directly navigate to SetupAppPinScreen.
            if (isAskingForFirstTime) {
              await _setUpAppPin(context);
            } else {
              AgencyAppDialog.showAppDialog(
                context: context,
                contentText: context.localizations.applicationApproved,
                actions:
                    (dialogContext) => [
                      PrimaryTextButton(
                        text: context.localizations.getStarted,
                        onTap: () async => await _setUpAppPin(context),
                      ),
                    ],
              );
            }
            emit(const AgentApplicationState.approvedApplication());
          },
        );
      },
      error: (error) {
        refreshController.refreshFailed();
        emit(const AgentApplicationState.prominentError());
        emit(const AgentApplicationState.applicationSubmitted());
        error.when(
          phonenumberRegisteredAsAgentManager: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText:
                  context
                      .localizations
                      .userAlreadyRegisteredAsAgentManagerError,
              isDismissible: false,
              onOkay: (_) => forceSignOutUser(context),
            );
          },
          agentDeactivated: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.agentDeactivated,
              isDismissible: false,
              onOkay: (_) => forceSignOutUser(context),
            );
          },
          applicationSubmissionLimitExceeded: (e) {
            AgencyAppDialog.showAppDialog(
              context: context,
              contentText:
                  context.localizations.applicationLimitExceededErrorMessage,
              actions:
                  (context) => [
                    PrimaryTextButton(
                      text: context.localizations.closeApp,
                      onTap: () => exit(0),
                    ),
                    PrimaryTextButton(
                      text: context.localizations.signOut,
                      onTap: () => signOutUser(context),
                    ),
                  ],
            );
          },
        );
      },
    );
  }

  Future<void> _setUpAppPin(BuildContext context) async {
    l.d("Setting Up session PIN");
    final bool? isAppPinSetup =
        await context.rootNavigator.pushNamed(SetupSessionPinScreen.id)
            as bool?;
    if (isAppPinSetup == true && context.mounted) {
      context.rootNavigator.pushNamedAndRemoveUntil(
        HomeScreen.id,
        (_) => false,
      );
    }
  }
}

@freezed
sealed class AgentApplicationState with _$AgentApplicationState {
  const factory AgentApplicationState.loading() = Loading;

  const factory AgentApplicationState.pullToRefreshLoading() =
      PullToRefreshLoading;

  const factory AgentApplicationState.prominentError() = ProminentError;

  const factory AgentApplicationState.transientError() = TransientError;

  const factory AgentApplicationState.approvedApplication() =
      ApprovedApplication;

  const factory AgentApplicationState.applicationSubmitted() =
      ApplicationSubmitted;

  const factory AgentApplicationState.applicationNotSubmitted(
    bool isApplicationLimitExceeded,
  ) = ApplicationNotSubmitted;
}

@freezed
sealed class AgentApplicationEvent with _$AgentApplicationEvent {
  const factory AgentApplicationEvent.getApplicationStatus(
    BuildContext context,
  ) = GetApplicationStatus;

  const factory AgentApplicationEvent.pullToRefresh(BuildContext context) =
      PullToRefresh;
}
