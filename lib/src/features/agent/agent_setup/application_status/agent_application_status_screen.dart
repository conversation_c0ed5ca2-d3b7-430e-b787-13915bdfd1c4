import 'package:agency_banking_rpcs/agency/rejected_application_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/create_agent_application/enter_shop_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/widgets/agent_welcome_widget.dart';
import 'package:bcn_agency_banking_flutter/src/features/landing_screen/widgets/user_type_button.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/pull_to_refresh_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../widgets/sign_out_button.dart';
import '../../../../widgets/spinner.dart';
import '../widgets/rejected_application_card.dart';
import 'bloc/agent_application_bloc.dart';

class AgentApplicationStatusScreen extends StatefulWidget {
  static const id = "/agent-application-screen";

  const AgentApplicationStatusScreen({Key? key}) : super(key: key);

  @override
  State<AgentApplicationStatusScreen> createState() =>
      _AgentApplicationStatusScreenState();
}

class _AgentApplicationStatusScreenState
    extends State<AgentApplicationStatusScreen> {
  late final _agentApplicationStatusBloc =
      BlocProvider.of<AgentApplicationBloc>(context);

  @override
  Widget build(BuildContext context) {
    // Setting status bar to the primary color
    // as per current theme.
    final currentStyle = (context.isDarkMode
            ? SystemUiOverlayStyle.dark
            : SystemUiOverlayStyle.light)
        .copyWith(
          statusBarColor: context.appColors.primaryDefaultColor,
          statusBarBrightness:
              context.isDarkMode ? Brightness.dark : Brightness.light,
        );
    return SafeArea(
      child: Scaffold(
        body: AnnotatedRegion<SystemUiOverlayStyle>(
          value: currentStyle,
          child: BlocBuilder<AgentApplicationBloc, AgentApplicationState>(
            buildWhen: (oldState, newState) {
              if (oldState is ApplicationSubmitted &&
                  newState is PullToRefreshLoading) {
                // Don't Rebuild since on ApplicationSubmitted state
                // the Status card button is already disabled.
                return false;
              }
              return newState is! ProminentError;
            },
            builder: (context, state) {
              return Column(
                children: [
                  Padding(
                    padding: commonScreenPadding,
                    child: SignOutButton(
                      isEnabled:
                          state is ApplicationSubmitted ||
                          state is ApplicationNotSubmitted ||
                          state is ApprovedApplication,
                    ),
                  ),
                  Expanded(
                    child:
                        state is Loading
                            ? _getLoadingBody()
                            : PullToRefreshWidget(
                              onRefresh: () {
                                ScaffoldMessenger.of(
                                  context,
                                ).hideCurrentSnackBar(
                                  reason: SnackBarClosedReason.dismiss,
                                );
                                _agentApplicationStatusBloc.add(
                                  AgentApplicationEvent.pullToRefresh(context),
                                );
                              },
                              controller:
                                  _agentApplicationStatusBloc.refreshController,
                              child: SingleChildScrollView(
                                child: _getBody(context, state),
                              ),
                            ),
                  ),
                  _getApplicationStatusButton(state, context),
                  verticalGapSixteen,
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _getBody(BuildContext context, AgentApplicationState state) {
    return Column(
      children: [
        const AgentWelcomeWidget(),
        _buildRestBodyBasedOnBloc(context, state),
      ],
    );
  }

  Widget _getLoadingBody() {
    return const Column(
      children: [AgentWelcomeWidget(), Expanded(child: Spinner())],
    );
  }

  Widget _buildRestBodyBasedOnBloc(
    BuildContext context,
    AgentApplicationState state,
  ) {
    final isDataLoaded =
        state is ApplicationNotSubmitted || state is ApplicationSubmitted;
    final isRejectedApplicationsListEmpty =
        _agentApplicationStatusBloc.rejectedApplications?.isEmpty ?? true;
    return Column(
      children: [
        _getAgentSubtitleText(state, context),
        if (isDataLoaded && !isRejectedApplicationsListEmpty)
          _getRejectionList(
            context,
            _agentApplicationStatusBloc.rejectedApplications!,
          ),
      ],
    );
  }

  Column _getRejectionList(
    BuildContext context,
    List<RejectedApplication> rejectedApplications,
  ) {
    return Column(
      children: [
        verticalGapTwentyFour,
        TitleWidget(title: context.localizations.previousApplications),
        verticalGapEight,
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          separatorBuilder:
              (_, __) => const Divider(
                thickness: dividerThickness,
                height: dividerThickness,
              ),
          itemCount: rejectedApplications.length,
          itemBuilder: (context, index) {
            final rejectedApplication = rejectedApplications[index];
            return RejectedApplicationCard(
              applicationDate: rejectedApplication.appliedOn.toLocal().onlyDate,
              reason: rejectedApplication.rejectionReason,
              updateSelectedCard: _updateSelectedCard,
              index: index,
              isExpanded: _selectedCard == index,
            );
          },
        ),
        verticalGapEight,
      ],
    );
  }

  Widget _getApplicationStatusButton(
    AgentApplicationState state,
    BuildContext context,
  ) {
    final isButtonEnabled =
        state is ApplicationNotSubmitted && !state.isApplicationLimitExceeded;
    final statusButton = UserTypeButton(
      buttonText:
          state is ApplicationNotSubmitted
              ? context.localizations.applyToBeAnAgent
              : context.localizations.applicationSubmittedText,
      buttonTextColor:
          state is ApplicationSubmitted
              ? context.appColors.successShade1Color
              : null,
      isEnabled: isButtonEnabled || state is ApplicationSubmitted,
      iconPath: ABAssets.fileIcon,
      onTap:
          isButtonEnabled
              ? () async {
                setStatusBarColorToTransparent();
                await context.navigator.pushNamed(EnterShopDetailsScreen.id);
                if (mounted) {
                  _agentApplicationStatusBloc.refreshController
                      .requestRefresh();
                }
              }
              : null,
      trailingIcon:
          state is ApplicationSubmitted
              ? IconWidget(
                assetName: ABAssets.checkIcon,
                iconColor: context.appColors.successShade1Color,
              )
              : null,
    );
    return switch (state) {
      Loading() => const SizedBox(),
      TransientError() => const SizedBox(),
      PullToRefreshLoading() => const SizedBox(),
      _ => statusButton,
    };
  }

  Widget _getAgentSubtitleText(
    AgentApplicationState state,
    BuildContext context,
  ) {
    if (state is ApplicationNotSubmitted || state is ApplicationSubmitted) {
      return Padding(
        padding: commonScreenPadding,
        child: Column(
          children: [
            verticalGapFour,
            if (state is ApplicationNotSubmitted &&
                state.isApplicationLimitExceeded)
              Text(
                context.localizations.tooManyApplications,
                style: context.appTextStyles.smallText1.copyWith(
                  color: context.appColors.errorTextFieldColor,
                ),
                textAlign: TextAlign.center,
              )
            else
              Text(
                state is ApplicationNotSubmitted
                    ? context.localizations.welcomeTextForAgentsSubtitle
                    : context.localizations.agentApplicationApprovalPending,
                style: context.appTextStyles.smallText1.copyWith(
                  color: context.appColors.neutralShadeDefaultColor,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  /// Updates the `_selectedCard` value to the `cardIndex` value.
  /// If the `cardIndex` value is already the same as `_selectedCard` value,
  /// sets it to -1 instead.
  void _updateSelectedCard(int cardIndex) {
    setState(() {
      _selectedCard != cardIndex
          ? _selectedCard = cardIndex
          : _selectedCard = -1;
    });
  }

  @override
  void dispose() {
    setStatusBarColorToTransparent();
    super.dispose();
  }

  // Initializing value to -1 confirms that none of the rejected cards is
  // expanded initially.
  int _selectedCard = -1;
}
