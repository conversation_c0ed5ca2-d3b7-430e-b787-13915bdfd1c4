import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../core/service_locator.dart';
import '../mock_rpc_impls/mock_get_application_status.dart';
import '../mock_rpc_impls/mock_submit_application_data_rpc_impl.dart';

class AgentApplicationSetupRepository {
  AgentApplicationSetupRepository();

  Future<LeoRPCResult<GetApplicationStatusResponse, GetApplicationStatusError>>
  getApplicationStatus() async {
    final request = GetApplicationStatusRequest();
    final GetApplicationStatusRPC impl =
        currentFlavor.isMock
            ? MockApplicationStatusImpl()
            : GetApplicationStatusRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<SubmitApplicationDataResponse, SubmitApplicationDataError>
  >
  submitApplication(
    Coordinate shopCoordinate,
    String shopName,
    LeoUUID? shopImageID,
  ) async {
    final request = SubmitApplicationDataRequest(
      shopCoordinate: shopCoordinate,
      shopName: shopName,
      shopImageId: shopImageID,
    );
    final SubmitApplicationDataRPC impl =
        currentFlavor.isMock
            ? MockSubmitApplicationDataImpl()
            : SubmitApplicationDataRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
