import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/bcn_logo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AgentWelcomeWidget extends StatelessWidget {
  const AgentWelcomeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: horizontalPaddingSixteen,
      child: Column(
        children: [
          verticalGapTwentyFour,
          const BCNLogo(),
          verticalGapTwentyFour,
          SvgPicture.asset(
            context.isDarkMode
                ? ABAssets.applicationScreenImageDark
                : ABAssets.applicationScreenImageLight,
          ),
          verticalGapSixteen,
          Text(
            context.localizations.welcomeTextForAgentsTitle,
            style: context.appTextStyles.titleBold.copyWith(
              color: context.appColors.titleColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
