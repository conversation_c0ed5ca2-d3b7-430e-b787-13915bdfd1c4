import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:flutter/material.dart';

import '../../../../utils/formatters/date_formatter.dart';

class RejectedApplicationCard extends StatelessWidget {
  final DateTime applicationDate;
  final String reason;

  // This approach is inspired by `onExpansionChanged` property of the
  // ExpansionTile widget. Thanks to Flutter-Dev team for this.
  /// This callback function is set as `onTap` callback of the card.
  /// Should take one int-type parameter with a `void` return type.
  /// [index] property value of this widget is passed as the parameter.
  final ValueSetter<int> updateSelectedCard;

  /// This is the index value of the card, out of a list of cards.
  final int index;
  final bool isExpanded;

  RejectedApplicationCard({
    super.key,
    required this.applicationDate,
    required this.reason,
    required this.updateSelectedCard,
    required this.index,
    required this.isExpanded,
  });

  static final Duration _animationDuration = 300.milliseconds;
  final _dateFormat = DateFormatter.defaultDateFormat();

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => updateSelectedCard(index),
      child: Ink(
        color: context.appColors.backgroundColor,
        child: AnimatedSize(
          duration: _animationDuration,
          alignment: Alignment.topCenter,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: dimenFortyEight,
                padding: horizontalPaddingSixteen,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        "${context.localizations.appliedDate}"
                                " ${_dateFormat.format(applicationDate)}"
                            .overflow,
                        style: context.appTextStyles.smallText1.copyWith(
                          color: context.appColors.neutralShade1Color,
                        ),
                        maxLines: appliedDateMaxLines,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    horizontalGapEight,
                    _buildRejectedTextContainer(context),
                    horizontalGapEight,
                    AnimatedRotation(
                      turns: isExpanded ? -0.5 : 0,
                      duration: _animationDuration,
                      child: IconWidget(
                        assetName: ABAssets.arrowDownIcon,
                        iconColor: context.appColors.neutralShadeDefaultColor,
                      ),
                    ),
                  ],
                ),
              ),
              if (isExpanded) ...[
                Padding(
                  padding: horizontalPaddingSixteen,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      verticalGapFour,
                      Text(
                        context.localizations.reason,
                        style: context.appTextStyles.smallText2.copyWith(
                          color: context.appColors.neutralShade7Color,
                        ),
                      ),
                      verticalGapFour,
                      Text(
                        reason,
                        style: context.appTextStyles.labelText2Bold.copyWith(
                          color: context.appColors.neutralShade1Color,
                        ),
                      ),
                      verticalGapTwenty,
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Container _buildRejectedTextContainer(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: dimenFour,
        horizontal: dimenEight,
      ),
      decoration: BoxDecoration(
        color: context.appColors.errorStateBackgroundColor,
        borderRadius: BorderRadius.circular(dimenFour),
      ),
      child: Text(
        context.localizations.rejected,
        style: context.appTextStyles.smallText2.copyWith(
          color: context.appColors.errorStateTextColor,
        ),
      ),
    );
  }
}

const appliedDateMaxLines = 1;
