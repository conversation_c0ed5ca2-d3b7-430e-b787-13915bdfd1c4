import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockApplicationStatusImpl extends GetApplicationStatusRPC {
  @override
  Future<LeoRPCResult<GetApplicationStatusResponse, GetApplicationStatusError>>
  execute(GetApplicationStatusRequest request) {
    final response = GetApplicationStatusResponse(
      result: GetApplicationStatusResponseResultEnum.APPLICATION_NOT_SUBMITTED(
        rejectedApplications: [],
        isApplicationSubmissionLimitExceeded: false,
      ),
    );
    final error =
        GetApplicationStatusError.GetApplicationStatusErrorPhonenumberRegisteredAsAgentManagerError(
          errorCode: "",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
