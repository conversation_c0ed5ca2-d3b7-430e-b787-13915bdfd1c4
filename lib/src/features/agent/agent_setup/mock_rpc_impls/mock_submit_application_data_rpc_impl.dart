import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockSubmitApplicationDataImpl extends SubmitApplicationDataRPC {
  @override
  Future<
    LeoRPCResult<SubmitApplicationDataResponse, SubmitApplicationDataError>
  >
  execute(SubmitApplicationDataRequest request) {
    final response = SubmitApplicationDataResponse();
    final error =
        SubmitApplicationDataError.SubmitApplicationDataErrorApplicationAlreadyApprovedError(
          errorCode: "",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
