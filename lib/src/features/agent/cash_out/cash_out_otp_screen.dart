import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/blocs/cash_out_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/blocs/cash_out_otp_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/phone_number_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../widgets/otp_widget.dart';
import '../../../widgets/primary_button.dart';
import '../../../widgets/user_details_card.dart';

class CashOutOTPScreen extends StatefulWidget {
  static const id = "/cash-out-otp-screen";

  const CashOutOTPScreen({Key? key}) : super(key: key);

  @override
  State<CashOutOTPScreen> createState() => _CashOutOTPScreenState();
}

class _CashOutOTPScreenState extends State<CashOutOTPScreen> {
  late final _cashOutBloc = BlocProvider.of<CashOutBloc>(context);
  late final _cashOutOtpBloc = BlocProvider.of<CashOutOTPBloc>(context);
  late final _userInfo = _cashOutBloc.createCashOutRequestResponse.bcnSender;
  late final _response = _cashOutBloc.createCashOutRequestResponse;
  final GlobalKey<FormState> _otpFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateCashOutDetails = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.withdrawFromOwnWalletTitle,
      isCancellable: true,
      ctaWidget: _buildCTA(),
      padding: EdgeInsets.zero,
      child: _getFormBody(context),
    );
  }

  Widget _buildCTA() {
    return PrimaryButton(
      labelText: context.localizations.attemptTransfer,
      onPressed: () {
        if (!(_otpFormKey.currentState?.validate() ?? false)) {
          setState(() {
            _shouldValidateCashOutDetails = AutovalidateMode.onUserInteraction;
          });
          return;
        }
        _cashOutOtpBloc.add(
          CashOutOTPEvent.attemptTransfer(
            context,
            _response.recordId,
            _cashOutBloc.withdrawingAmount!,
          ),
        );
      },
    );
  }

  Widget _getFormBody(BuildContext context) {
    final formattedPhoneNumber = _getPhoneNumberString(
      _cashOutBloc.phoneNumber,
    );
    return Column(
      children: [
        verticalGapEight,
        TitleWidget(title: context.localizations.userDetails),
        Padding(
          padding: verticalPaddingSixteen,
          child: UserDetailsCard(
            image: _userInfo.profileImage,
            name: _userInfo.displayName,
            subTitle: formattedPhoneNumber!,
          ),
        ),
        TransactionDetailsWidget(
          transactionAmountLabel: context.localizations.withdrawAmount,
          transactionAmount: _cashOutBloc.withdrawingAmount!,
          transactionFee: _response.claimedTransactionFee,
          receivingAmountLabel: context.localizations.customerDebit,
          receivingAmount: _cashOutBloc.debitedAmount!,
        ),
        TitleWidget(title: context.localizations.otpVerification),
        Form(
          key: _otpFormKey,
          autovalidateMode: _shouldValidateCashOutDetails,
          child: Padding(
            padding: allPaddingSixteen,
            child: OTPWidget(
              otpValidityDetails: _cashOutBloc.otpValidityDetails,
              onChanged: (otp) {
                _cashOutOtpBloc.add(CashOutOTPEvent.addOtp(otp));
              },
              onResend: () async {
                _cashOutOtpBloc.add(
                  CashOutOTPEvent.resendOTP(
                    context,
                    _cashOutBloc.createCashOutRequestResponse.recordId,
                  ),
                );

                /// Wait For Bloc to send some State.
                final latestState = await _cashOutOtpBloc.stream.firstOrNull;
                return switch (latestState) {
                  OTPResentSuccessfully(:final resendDetails) => resendDetails,
                  _ => null,
                };
              },
            ),
          ),
        ),
        verticalGapEight,
      ],
    );
  }

  String? _getPhoneNumberString(String? phoneNumber) {
    if (phoneNumber != null) {
      return PhoneNumberFormatter.format(phoneNumber);
    } else {
      return phoneNumber;
    }
  }
}
