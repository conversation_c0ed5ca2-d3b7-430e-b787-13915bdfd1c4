import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/mock_impls/mock_confirm_cash_out_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/mock_impls/mock_create_cash_out_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/mock_impls/mock_request_cash_out_otp.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/mock_impls/mock_resend_cash_out_otp.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import '../../../../core/auth/rpc_auth_provider.dart';
import '../../../../core/service_locator.dart';

class CashOutRepository {
  Future<LeoRPCResult<CreateCashOutRequestResponse, CreateCashOutRequestError>>
  createCashOutRequest(LeoPhoneNumber phoneNumber, Amount amount) async {
    final request = CreateCashOutRequestRequest(
      phoneNumber: phoneNumber,
      amount: amount,
    );
    final CreateCashOutRequestRPC impl =
        currentFlavor.isMock
            ? MockCreateCashOutRequest()
            : CreateCashOutRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<LeoRPCResult<RequestCashOutOTPResponse, RequestCashOutOTPError>>
  requestOTP(LeoUUID recordId) async {
    final request = RequestCashOutOTPRequest(recordId: recordId);
    final RequestCashOutOTPRPC impl =
        currentFlavor.isMock
            ? MockRequestCashOutOTP()
            : RequestCashOutOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<LeoRPCResult<ResendCashOutOTPResponse, ResendCashOutOTPError>>
  resendOTP(LeoUUID recordId) async {
    final request = ResendCashOutOTPRequest(recordId: recordId);
    final ResendCashOutOTPRPC impl =
        currentFlavor.isMock
            ? MockResendCashOutOTP()
            : ResendCashOutOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<ConfirmCashOutRequestResponse, ConfirmCashOutRequestError>
  >
  confirmCashOutRequest({required Otp otp, required LeoUUID recordId}) async {
    final request = ConfirmCashOutRequestRequest(recordId: recordId, otp: otp);
    final ConfirmCashOutRequestRPC impl =
        currentFlavor.isMock
            ? MockConfirmCashOutRequest()
            : ConfirmCashOutRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
