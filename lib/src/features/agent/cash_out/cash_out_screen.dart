import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/cash_out_otp_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/route_generator.dart';
import '../../../widgets/nested_navigator_observer.dart';
import 'blocs/cash_out_otp_bloc.dart';
import 'cash_out_landing_screen.dart';

/// A Nested Navigator for CashOut Flow screen.
/// All screens will come under this Navigator and
/// routes for the same will be added in [Navigator.onGenerateRoute]
/// See this for more info: https://docs.flutter.dev/cookbook/effects/nested-nav
class CashOutScreen extends StatelessWidget {
  static const id = "/cash-out";

  const CashOutScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final nestedNavigationObserver = NestedNavigatorObserver();
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }
        final NavigatorState navigator = Navigator.of(context);
        if (isCurrentRouteInitialRoute(
          nestedNavigationObserver,
          CashOutLandingScreen.id,
        )) {
          navigator.pop();
          return;
        }
        final bool? shouldPop = await AgencyAppDialog.showCancelConfirmation(
          context,
          context.localizations.transaction,
        );
        if (shouldPop ?? false) {
          navigator.pop();
          return;
        }
      },
      child: Navigator(
        onGenerateRoute: (settings) {
          switch (settings.name) {
            case CashOutLandingScreen.id:
              return _getCashOutLandingScreenRoute();
            case CashOutOTPScreen.id:
              return Routes.getMaterialRoute(
                CashOutOTPScreen.id,
                BlocProvider(
                  create: (context) => CashOutOTPBloc(),
                  child: const CashOutOTPScreen(),
                ),
              );
            default:
              return _getCashOutLandingScreenRoute();
          }
        },
        initialRoute: CashOutLandingScreen.id,
        observers: [nestedNavigationObserver],
      ),
    );
  }

  MaterialPageRoute<dynamic> _getCashOutLandingScreenRoute() {
    return Routes.getMaterialRoute(
      CashOutLandingScreen.id,
      const CashOutLandingScreen(),
    );
  }
}
