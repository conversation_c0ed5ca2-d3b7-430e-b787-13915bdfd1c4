import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/blocs/cash_out_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_text_input_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/amount_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_number_text_field/phone_number_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/logger.dart';

class CashOutLandingScreen extends StatefulWidget {
  static const id = "/cash-out-landing-screen";

  const CashOutLandingScreen({Key? key}) : super(key: key);

  @override
  State<CashOutLandingScreen> createState() => _CashOutLandingScreenState();
}

class _CashOutLandingScreenState extends State<CashOutLandingScreen> {
  late final _cashOutBloc = BlocProvider.of<CashOutBloc>(context);
  final GlobalKey<FormState> _cashOutDetailsFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateCashOutDetails = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.withdrawFromOwnWalletTitle,
      ctaWidget: _buildCTA(context),
      padding: commonScreenPadding.copyWith(top: dimenForty),
      child: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Form(
      key: _cashOutDetailsFormKey,
      autovalidateMode: _shouldValidateCashOutDetails,
      child: Column(
        children: [
          PhoneNumberTextField(
            onPhoneNumberValidated: (phoneNumber) {
              _cashOutBloc.add(CashOutEvent.addPhoneNumber(phoneNumber));
            },
          ),
          verticalGapSixteen,
          AmountTextField(
            amountTextInputFormatter: AmountTextInputFormatter(
              currency: _cashOutBloc.defaultCurrency,
            ),
            onChanged: (amount) {
              _cashOutBloc.add(CashOutEvent.addAmount(amount));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCTA(BuildContext context) {
    return PrimaryButton(
      labelText: context.localizations.continueText,
      onPressed: () {
        if (!(_cashOutDetailsFormKey.currentState?.validate() ?? false)) {
          l.v("Cash In Landing Screen Form not validated");
          setState(() {
            _shouldValidateCashOutDetails = AutovalidateMode.onUserInteraction;
          });
          return;
        }
        _cashOutBloc.add(CashOutEvent.createCashOutRequest(context));
      },
    );
  }
}
