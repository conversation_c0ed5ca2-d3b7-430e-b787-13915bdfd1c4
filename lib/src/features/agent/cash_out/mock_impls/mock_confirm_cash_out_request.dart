import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import '../../../../helpers/mock_helpers.dart';

class MockConfirmCashOutRequest extends ConfirmCashOutRequestRPC {
  @override
  Future<
    LeoRPCResult<ConfirmCashOutRequestResponse, ConfirmCashOutRequestError>
  >
  execute(ConfirmCashOutRequestRequest request) {
    final response = ConfirmCashOutRequestResponse(
      succeededAt: DateTime.now(),
      transactionDetail: TransactionStatusDetail(
        description: LocalizedText(en: 'Fee charged for customer'),
        itemDetail: [
          TransactionStatusItemDetail(
            label: LocalizedText(en: "Debited From"),
            valueType:
                TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                  title: "Zikomo Malawa",
                  description: '+91 **********',
                  image: LocalizedImage(
                    en: ThemedImage(dark: mockImage, light: mockImage),
                  ),
                ),
          ),
        ],
      ),
    );

    final error =
        ConfirmCashOutRequestError.ConfirmCashOutRequestErrorIncorrectOtpError(
          numberOfValidationAttemptsLeft: 0,
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: !checkWhetherOTPValid(request.otp),
      response: response,
      error: error,
    );
    return Future.delayed(2.seconds, () => result);
  }
}
