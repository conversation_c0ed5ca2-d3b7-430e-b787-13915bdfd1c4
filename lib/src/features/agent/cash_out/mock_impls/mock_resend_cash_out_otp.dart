import 'package:agency_banking_rpcs/agency/resend_cash_out_otp_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import '../../../../helpers/mock_helpers.dart';

class MockResendCashOutOTP extends ResendCashOutOTPRPC {
  @override
  Future<LeoRPCResult<ResendCashOutOTPResponse, ResendCashOutOTPError>> execute(
    ResendCashOutOTPRequest request,
  ) {
    final response = ResendCashOutOTPResponse(
      otpDetails: OTPConstants.resendDetails,
    );

    final error =
        ResendCashOutOTPError.ResendCashOutOTPErrorTooManyResendRequestsError(
          errorCode: "123",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
