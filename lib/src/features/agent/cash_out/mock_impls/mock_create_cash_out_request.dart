import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

class MockCreateCashOutRequest extends CreateCashOutRequestRPC {
  @override
  Future<LeoRPCResult<CreateCashOutRequestResponse, CreateCashOutRequestError>>
  execute(CreateCashOutRequestRequest request) async {
    final response = CreateCashOutRequestResponse(
      recordId: LeoUUID(null),
      bcnSender: BCNUserDisplayInfo(
        displayName: 'Zikomo Malawa',
        profileImage: mockImage,
      ),
      claimedTransactionFee: Amount(
        amount: 1236,
        currency: Currency(currencyCode: "MWK"),
      ),
      feeExpiringAt: DateTime.now().add(10.minutes),
    );

    final error =
        CreateCashOutRequestError.CreateCashOutRequestErrorAgentDisabledForSpecificIntervalError(
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return await Future.delayed(1.seconds, () => result);
  }
}
