import 'dart:async';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/cash_out_otp_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/phone_number_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_phone_number.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../core/logger.dart';
import '../../../../helpers/rpc_handler.dart';
import '../../../session_pin/enter_session_pin/enter_session_pin_screen.dart';
import '../repositories/cash_out_repository.dart';

part 'cash_out_bloc.freezed.dart';

class CashOutBloc extends Bloc<CashOutEvent, CashOutState> with RPCHandler {
  String? _phoneNumber;
  Amount? _withdrawingAmount;
  Amount? _debitedAmount;
  final Coordinate shopCoordinate;
  final Currency defaultCurrency;
  final CashOutRepository _cashOutRepository = CashOutRepository();
  final LocationService _locationService = locator<LocationService>();
  late CreateCashOutRequestResponse? _createCashOutRequestResponse;
  late final OTPValidityDetails? _otpValidityDetails;

  CashOutBloc({required this.shopCoordinate, required this.defaultCurrency})
    : super(const CashOutState.initial()) {
    on<CashOutEvent>((event, emit) async {
      switch (event) {
        case AddPhoneNumber(:final phoneNumber):
          if (state is Loading) return;
          _phoneNumber = phoneNumber;
        case AddAmount(:final amount):
          if (state is Loading) return;
          _withdrawingAmount = amount;
        case CreateCashOutRequest(:final context):
          await _createCashOutRequest(emit, context);
      }
    });
  }

  String? get phoneNumber => _phoneNumber;

  Amount? get withdrawingAmount => _withdrawingAmount;

  Amount? get debitedAmount => _debitedAmount;

  CreateCashOutRequestResponse get createCashOutRequestResponse =>
      _createCashOutRequestResponse!;

  OTPValidityDetails get otpValidityDetails => _otpValidityDetails!;

  Future<void> _createCashOutRequest(
    Emitter<CashOutState> emit,
    BuildContext context,
  ) async {
    if (!context.mounted) return;
    if (!_isAgentLocationValid(context, emit)) return;
    final isAgentAuthenticated =
        await context.rootNavigator.pushNamed(EnterSessionPinScreen.id)
            as bool?;
    if (!(isAgentAuthenticated ?? false)) {
      return;
    }
    if (context.mounted) {
      l.d('''Creating Cash Out Request: 
        phoneNumber: $phoneNumber
        amount: $withdrawingAmount
      ''');
      emit(const CashOutState.loading());
      l.v("Showing Loader Dialog");
      _showLoaderDialog(context);
      await rpcHandler(
        () async {
          final rpcResult = await _cashOutRepository.createCashOutRequest(
            LeoPhoneNumber(_phoneNumber!),
            _withdrawingAmount!,
          );
          l.d("Create Cash Out Request Result: $rpcResult");
          if (context.mounted) {
            await _handleCreateCashOutRequestRPCResult(
              context,
              rpcResult,
              emit,
            );
          }
        },
        onTransientError: (_) {
          l.v("Emitting Transient Error");
          context.rootNavigator.pop();
          emit(const CashOutState.transientError());
        },
      );
    }
  }

  void _showLoaderDialog(BuildContext context) {
    final formattedPhoneNumber = PhoneNumberFormatter.format(_phoneNumber!);
    AgencyAppDialog.showSpinnerDialog(
      context,
      contentText: context.localizations.lookingUpDetailsPhoneNumber(
        formattedPhoneNumber,
      ),
    );
  }

  Future<void> _handleCreateCashOutRequestRPCResult(
    BuildContext context,
    LeoRPCResult<CreateCashOutRequestResponse, CreateCashOutRequestError>
    rpcResult,
    Emitter<CashOutState> emit,
  ) async {
    l.v("Handling Create Cash Out Result");
    // Pop the loader dialog.
    context.rootNavigator.pop();
    await rpcResult.when(
      response: (response) async {
        l.d("Create Cash Out Request Response: $response");
        if (response.receivingAmount != null) {
          l.e("Create Cash Out Request has Exchange Rate component");
          ProminentErrorHandler.exchangeRateNotSupported();
        }
        emit(const CashOutState.createdCashOutRequest());
        _createCashOutRequestResponse = response;
        _debitedAmount =
            response.receivingAmount ??
            AmountFormatter.addAmounts(
              _withdrawingAmount!,
              response.claimedTransactionFee,
            );
        l.d("Debited Amount in Cash Out: $_debitedAmount");
        final bool? shouldProceed = await TransactionDialog.show(
          context: context,
          transactionAmountLabel: context.localizations.withdrawAmount,
          transactionAmount: _withdrawingAmount!,
          transactionFee: response.claimedTransactionFee,
          receivingAmountLabel: context.localizations.customerDebit,
          receivingAmount: _debitedAmount!,
          additionalDetailLabel: context.localizations.customer,
          additionalDetailValue: response.bcnSender.displayName,
        );
        l.d("Should Proceed Cash Out Request $shouldProceed");
        if (shouldProceed == true && context.mounted) {
          AgencyAppDialog.showSpinnerDialog(context);
          await _requestOTP(context, emit, response.recordId);
        }
      },
      error: (error) {
        l.e("Error while creating Cash Out Request: $error");
        emit(const CashOutState.prominentError());
        error.when(
          phoneNumberUnknown: (_) {
            ProminentErrorHandler.phoneNumberUnknown(_phoneNumber!);
          },
          currencyMismatchForAgentAccountAndAmount:
              (e) => throw DeveloperError(e.toString()),
          periodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.periodicRequestTransactionLimitExceeded();
          },
          monetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.monetaryTransactionLimitExceededErrorMessage();
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          amountTooLess: (error) {
            ProminentErrorHandler.amountTooLess(error.minimumAllowedAmount);
          },
          amountTooLarge: (error) {
            ProminentErrorHandler.amountTooHigh(error.maximumAllowedAmount);
          },
          insufficientBalance: (e) {
            ProminentErrorHandler.inSufficientBalance(
              contentText: context.localizations.customerInsufficientBalance(
                e.transactionFee.localisedFormattedAmount,
              ),
            );
          },
          recipientCannotBeSelf: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.cashOutRecipientCannotBeSelf,
            );
          },
          recipientCannotBeAgent: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.cashOutRecipientCannotBeAgent,
            );
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          senderAccountDeactivated: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.senderAccountDeactivated,
            );
          },
          agentAccountWouldCrossLimit: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.agentAccountWouldCrossLimit,
            );
          },
        );
      },
    );
  }

  Future<void> _requestOTP(
    BuildContext context,
    Emitter<CashOutState> emit,
    LeoUUID recordId,
  ) async {
    l.d("Requesting OTP for Cash Out Request. Record ID: $recordId");
    if (!_isAgentLocationValid(context, emit)) return;
    await rpcHandler(
      () async {
        final result = await _cashOutRepository.requestOTP(recordId);
        l.d("Request Cash Out Request result: $result");
        if (context.mounted) {
          _handlerRequestOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        l.d("Emitting Transient Error");
        context.rootNavigator.pop();
        emit(const CashOutState.transientError());
      },
    );
  }

  void _handlerRequestOTPResult(
    BuildContext context,
    Emitter<CashOutState> emit,
    LeoRPCResult<RequestCashOutOTPResponse, RequestCashOutOTPError> result,
  ) {
    l.v("Handling Request Cash Out OTP Result");
    // Pop the loader dialog.
    context.rootNavigator.pop();
    result.when(
      response: (response) {
        l.d("Response for Request Cash Out OTP: $response");
        _otpValidityDetails = response.otpDetails;
        context.navigator.pushNamed(CashOutOTPScreen.id);
      },
      error: (error) {
        l.e("Error while Requesting Cash Out OTP: $error");
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          transactionFeeConfirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.cashOut,
            );
          },
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          tooManyOtpRequests: (_) {
            ProminentErrorHandler.tooManyOTPRequests();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          inactiveSender: (_) {
            ProminentErrorHandler.inactiveSender();
          },
        );
      },
    );
  }

  /// Checks whether agent location is near to their shop location.
  /// if not returns false and shows appropriate dialog.
  bool _isAgentLocationValid(BuildContext context, Emitter<CashOutState> emit) {
    l.v("Checking Location");
    final LocationDetails locationDetails = shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    l.d("IsLocationValid $isAgentLocationValid");
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class CashOutState with _$CashOutState {
  const factory CashOutState.initial() = Initial;

  const factory CashOutState.loading() = Loading;

  const factory CashOutState.prominentError() = ProminentError;

  const factory CashOutState.transientError() = TransientError;

  const factory CashOutState.createdCashOutRequest() = CreatedCashOutRequest;
}

@freezed
sealed class CashOutEvent with _$CashOutEvent {
  const factory CashOutEvent.addPhoneNumber(String? phoneNumber) =
      AddPhoneNumber;

  const factory CashOutEvent.addAmount(Amount? amount) = AddAmount;

  const factory CashOutEvent.createCashOutRequest(BuildContext context) =
      CreateCashOutRequest;
}
