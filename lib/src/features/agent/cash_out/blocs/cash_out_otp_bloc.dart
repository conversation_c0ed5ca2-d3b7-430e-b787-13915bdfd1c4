import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/blocs/cash_out_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_success_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../core/logger.dart';
import '../../../../helpers/rpc_handler.dart';
import '../../../../models/transaction_success_screen_arguments.dart';
import '../repositories/cash_out_repository.dart';

part 'cash_out_otp_bloc.freezed.dart';

class CashOutOTPBloc extends Bloc<CashOutOTPEvent, CashOutOTPState>
    with RPCHandler {
  String? _otp;
  final CashOutRepository _cashOutRepository = CashOutRepository();
  final LocationService _locationService = locator<LocationService>();

  CashOutOTPBloc() : super(const Initial()) {
    on<CashOutOTPEvent>((event, emit) async {
      switch (event) {
        case AddOTP(:final otp):
          if (state is OTPLoading) return;
          _otp = otp;
        case AttemptTransfer(:final context, :final recordId, :final amount):
          await _attemptCashOutRequest(context, emit, recordId, amount);
        case ResendOTP(:final context, :final recordId):
          if (state is OTPLoading) return;
          await _resendOTP(context, emit, recordId);
      }
    });
  }

  Future<void> _attemptCashOutRequest(
    BuildContext context,
    Emitter<CashOutOTPState> emit,
    LeoUUID recordId,
    Amount amount,
  ) async {
    l.d('''Confirming Cash Out Request: 
    amount: $amount
    recordId: $recordId
    otp: $_otp''');
    if (context.mounted) {
      if (!_isAgentLocationValid(context, emit)) return;
      AgencyAppDialog.showSpinnerDialog(context);
      emit(const CashOutOTPState.loading());
      await rpcHandler(
        () async {
          final result = await _cashOutRepository.confirmCashOutRequest(
            otp: Otp(otp: _otp!),
            recordId: recordId,
          );
          l.d("Confirm Cash Out Request Result: $result");
          if (context.mounted) {
            _handleConfirmCashOutRequestResult(
              context,
              emit,
              result,
              recordId,
              amount,
            );
          }
        },
        onTransientError: (_) {
          context.rootNavigator.pop();
          emit(const CashOutOTPState.transientError());
        },
      );
    }
  }

  Future<void> _resendOTP(
    BuildContext context,
    Emitter<CashOutOTPState> emit,
    LeoUUID recordId,
  ) async {
    if (!_isAgentLocationValid(context, emit)) return;
    await rpcHandler(
      () async {
        final result = await _cashOutRepository.resendOTP(recordId);
        if (context.mounted) {
          await _handleResendOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        emit(const CashOutOTPState.transientError());
      },
    );
  }

  Future<void> _handleResendOTPResult(
    BuildContext context,
    Emitter<CashOutOTPState> emit,
    LeoRPCResult<ResendCashOutOTPResponse, ResendCashOutOTPError> result,
  ) async {
    result.when(
      response: (response) {
        emit(CashOutOTPState.otpResentSuccessfully(response.otpDetails));
      },
      error: (error) {
        emit(const CashOutOTPState.prominentError());
        error.when(
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          waitForResend: (_) => ProminentErrorHandler.waitForOTPResend(),
          tooManyResendRequests: (_) {
            ProminentErrorHandler.tooManyResendRequests();
          },
          otpNeverSent: (e) => throw DeveloperError(e.toString()),
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          incorrectRecordId: (e) => throw DeveloperError(e.toString()),
          inactiveSender: (_) {
            ProminentErrorHandler.inactiveSender();
          },
          cashOutConfirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.cashOut,
            );
          },
        );
      },
    );
  }

  void _handleConfirmCashOutRequestResult(
    BuildContext context,
    Emitter<CashOutOTPState> emit,
    LeoRPCResult<ConfirmCashOutRequestResponse, ConfirmCashOutRequestError>
    result,
    LeoUUID recordId,
    Amount amount,
  ) {
    l.v("Handling Confirm Cash Our Request Result");
    result.when(
      response: (response) {
        l.d("Response for Confirm Cash out Request: $response");
        emit(const CashOutOTPState.cashOutSuccessful());
        context.rootNavigator.pop();
        context.rootNavigator.pushNamed(
          TransactionSuccessScreen.id,
          arguments: TransactionSuccessScreenArguments(
            transactionStatusDetail: response.transactionDetail,
            succeededAt: response.succeededAt,
            recordId: recordId.uuid,
            amount: amount,
          ),
        );
      },
      error: (error) {
        l.d("Error for Confirm Cash Out request: $error");
        context.rootNavigator.pop();
        emit(const CashOutOTPState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          otpExpired: (_) {
            ProminentErrorHandler.otpExpired();
          },
          incorrectOtp: (error) {
            ProminentErrorHandler.incorrectOTP(
              numberOfValidationsAttemptsLeft:
                  error.numberOfValidationAttemptsLeft,
              featureName: context.localizations.cashOut,
            );
          },
          tooManyOtpAttempts: (_) {
            ProminentErrorHandler.tooManyOTPRequests();
          },
          periodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.periodicRequestTransactionLimitExceeded();
          },
          monetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.monetaryTransactionLimitExceededErrorMessage();
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          insufficientBalance: (e) {
            ProminentErrorHandler.inSufficientBalance(
              onOkay: navigateToHomeScreen,
              contentText: context.localizations.customerInsufficientBalance(
                e.transactionFee.localisedFormattedAmount,
              ),
              buttonText: context.localizations.okay,
            );
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          inactiveSender: (_) {
            ProminentErrorHandler.inactiveSender();
          },
          cashOutConfirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.cashOut,
            );
          },
        );
      },
    );
  }

  /// Checks whether agent location is near to their shop location.
  /// if not returns false and shows appropriate dialog.
  bool _isAgentLocationValid(
    BuildContext context,
    Emitter<CashOutOTPState> emit,
  ) {
    final cashOutBloc = BlocProvider.of<CashOutBloc>(context);
    final LocationDetails locationDetails =
        cashOutBloc.shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const CashOutOTPState.prominentError());
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class CashOutOTPState with _$CashOutOTPState {
  const factory CashOutOTPState.initial() = Initial;

  const factory CashOutOTPState.loading() = OTPLoading;

  const factory CashOutOTPState.prominentError() = ProminentError;

  const factory CashOutOTPState.transientError() = TransientError;

  const factory CashOutOTPState.otpResentSuccessfully(
    OTPResendDetails resendDetails,
  ) = OTPResentSuccessfully;

  const factory CashOutOTPState.cashOutSuccessful() = CashOutSuccessful;
}

@freezed
sealed class CashOutOTPEvent with _$CashOutOTPEvent {
  const factory CashOutOTPEvent.addOtp(String otp) = AddOTP;

  const factory CashOutOTPEvent.attemptTransfer(
    BuildContext context,
    LeoUUID recordId,
    Amount amount,
  ) = AttemptTransfer;

  const factory CashOutOTPEvent.resendOTP(
    BuildContext context,
    LeoUUID recordId,
  ) = ResendOTP;
}
