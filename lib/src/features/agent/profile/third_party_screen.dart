import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../../core/third_party_software_licenses.dart';
import '../../../exceptions/url_destination_missing.dart';
import '../../../helpers/helpers.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/primary_text_button.dart';

/// Packages that we own and are not taken
/// from pub.dev or any other package distributor.
/// Please add all the packages that we own here,
/// so that it does not show up in the
/// [ThirdPartySoftwareScreen].
const _ownPackages = [
  "dedwig",
  "leo_flutter_ui",
  "agency_banking_rpcs",
  "leo_dart_runtime",
];

class ThirdPartySoftwareScreen extends StatelessWidget {
  static const id = "/third-party-software";

  const ThirdPartySoftwareScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final filteredPackages =
        dependencies
            .where((package) => !_ownPackages.contains(package.name))
            .toList();
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.thirdPartySoftware),
      body: ListView.separated(
        padding: verticalPaddingEight,
        separatorBuilder:
            (_, __) => const Divider(height: dimenOne, thickness: dimenOne),
        itemCount: filteredPackages.length,
        itemBuilder:
            (context, index) =>
                _LicenseCard(licensePackage: filteredPackages[index]),
      ),
    );
  }
}

class _LicenseCard extends StatelessWidget {
  final Package licensePackage;

  const _LicenseCard({Key? key, required this.licensePackage})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: commonScreenPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            licensePackage.name,
            style: context.appTextStyles.labelText2Bold.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
          ),
          if (licensePackage.repository != null) ...{
            verticalGapEight,
            Text.rich(
              TextSpan(
                text: licensePackage.repository!,
                style: context.appTextStyles.labelText3.copyWith(
                  color: context.appColors.primaryLightColor,
                ),
                recognizer:
                    TapGestureRecognizer()
                      ..onTap =
                          () => _launchURLAndHandleMissingBrowserException(
                            context,
                            licensePackage.repository!,
                          ),
              ),
            ),
          },
          verticalGapEight,
          Text(
            licensePackage.license!,
            style: context.appTextStyles.labelText3.copyWith(
              color: context.appColors.neutralShade7Color,
            ),
          ),
          if (licensePackage.homepage != null) ...{
            verticalGapEight,
            Text.rich(
              TextSpan(
                text: licensePackage.homepage!,
                style: context.appTextStyles.labelText3.copyWith(
                  color: context.appColors.primaryLightColor,
                ),
                recognizer:
                    TapGestureRecognizer()
                      ..onTap =
                          () => _launchURLAndHandleMissingBrowserException(
                            context,
                            licensePackage.homepage!,
                          ),
              ),
            ),
          },
        ],
      ),
    );
  }

  Future<void> _launchURLAndHandleMissingBrowserException(
    BuildContext context,
    String url,
  ) async {
    try {
      await launchExternalUrl(url);
    } on UrlDestinationMissingException catch (_) {
      if (context.mounted) {
        _communicateMissingBrowser(context);
      }
    }
  }

  void _communicateMissingBrowser(BuildContext context) {
    AgencyAppDialog.showAppDialog(
      context: context,
      contentText:
          context.localizations.unableToOpenUrlMissingBrowserErrorMessage,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.dismiss.toUpperCase(),
              onTap: dialogContext.navigator.pop,
            ),
          ],
    );
  }
}
