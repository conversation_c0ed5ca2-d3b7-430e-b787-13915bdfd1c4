import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../helpers/mock_helpers.dart';

class MockProfilePageImpl extends GetAgentProfileDataRPC {
  @override
  Future<LeoRPCResult<GetAgentProfileDataResponse, Never>> execute(
    GetAgentProfileDataRequest request,
  ) {
    final supportedLocale = [
      LocaleInformation(
        locale: Locale(code: "EN_US"),
        displayName: LocalizedText(en: "English", ny: "English"),
        flag: ThemedImage(dark: mockImage, light: mockImage),
      ),
      LocaleInformation(
        locale: Locale(code: "NY"),
        displayName: LocalizedText(en: "Nyanja  ", ny: "Nyanja"),
        flag: ThemedImage(dark: mockImage, light: mockImage),
      ),
    ];
    final accounts = [
      BCNAccountDetail(
        accountId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
        displayName: "Home",
      ),
    ];
    final response = GetAgentProfileDataResponse(
      userPersonalDetails: UserPersonalDetails(
        firstName: "John",
        lastName: "Cena",
        gender: GenderEnum.MALE,
        dateOfBirth: LeoDate("2000-09-10"),
        nationalId: "********",
      ),
      accounts: accounts,
      agentAccount: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
      profileImage: mockImage,
      shopName: "Shop",
      shopStatus: ShopStatusEnum.SHOP_OPEN,
      shopCoordinate: Coordinate(latitude: 19.4138, longitude: 72.8220),
      supportedLocales: supportedLocale,
      phoneNumber: LeoPhoneNumber("+**********"),
    );
    final result = LeoRPCResult<GetAgentProfileDataResponse, Never>.response(
      response,
    );

    return Future.delayed(2.seconds, () => result);
  }
}

class MockChangeLocaleImpl extends ChangeLocaleRPC {
  @override
  Future<LeoRPCResult<ChangeLocaleResponse, Never>> execute(
    ChangeLocaleRequest request,
  ) {
    final result = LeoRPCResult<ChangeLocaleResponse, Never>.response(
      ChangeLocaleResponse(),
    );

    return Future.delayed(2.seconds, () => result);
  }
}
