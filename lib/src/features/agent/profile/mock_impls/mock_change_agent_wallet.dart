import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockChangeAgentWalletImpl extends ChangeAgentAccountRPC {
  @override
  Future<LeoRPCResult<ChangeAgentAccountResponse, Never>> execute(
    ChangeAgentAccountRequest request,
  ) {
    final response = ChangeAgentAccountResponse(
      agentAccountBalance: Amount(
        amount: 1000000,
        currency: Currency(currencyCode: 'MWK'),
      ),
    );
    final result = LeoRPCResult<ChangeAgentAccountResponse, Never>.response(
      response,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
