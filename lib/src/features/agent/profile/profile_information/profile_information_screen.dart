import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/map_location_tile.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/gender_helper.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/user_details_card.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../models/location_details.dart';
import '../../../../resources/ab_assets.dart';
import '../../../../widgets/section_widget.dart';
import 'shop_location_map_preview.dart';

class ProfileInformationScreen extends StatelessWidget {
  static const id = "/profile-information-screen";

  final GetAgentProfileDataResponse profileData;

  final LocationDetails locationDetails;

  const ProfileInformationScreen({
    Key? key,
    required this.profileData,
    required this.locationDetails,
  }) : super(key: key);

  String get _agentFullName =>
      "${profileData.userPersonalDetails.firstName} ${profileData.userPersonalDetails.lastName ?? ""}";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.profileInformation),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildUserDetails(context),
            _buildBasicDetails(context),
            _buildShopDetails(context),
            _buildShopLocation(context),
            verticalGapTwentyFour,
          ],
        ),
      ),
    );
  }

  UserDetailsCard _buildUserDetails(BuildContext context) {
    return UserDetailsCard(
      userImageSize: dimenSixtyFour,
      name: _agentFullName,
      image: profileData.profileImage,
      subTitle: profileData.shopName,
      nameStyle: context.appTextStyles.labelText1Bold,
      thirdLineContent: profileData.phoneNumber.formattedPhoneNumber,
    );
  }

  SectionWidget _buildBasicDetails(BuildContext context) {
    return SectionWidget(
      title: context.localizations.basicDetails,
      children: [
        if (profileData.userPersonalDetails.otherName != null)
          InfoLabel(
            title: context.localizations.otherName,
            bodyText: profileData.userPersonalDetails.otherName,
          ),
        InfoLabel(
          title: context.localizations.dateOfBirth,
          bodyText: _getFormattedDate(
            profileData.userPersonalDetails.dateOfBirth,
          ),
        ),
        InfoLabel(
          title: context.localizations.nationalID,
          bodyText: profileData.userPersonalDetails.nationalId,
        ),
        InfoLabel(
          title: context.localizations.gender,
          bodyText: GenderHelper.getGenderString(
            profileData.userPersonalDetails.gender,
            context,
          ),
        ),
      ],
    );
  }

  SectionWidget _buildShopDetails(BuildContext context) {
    return SectionWidget(
      title: context.localizations.shopDetails,
      padding: horizontalPaddingSixteen.copyWith(top: dimenEight),
      children: [
        InfoLabel(
          title: context.localizations.shopName,
          bodyText: profileData.shopName,
        ),
      ],
    );
  }

  InkWell _buildShopLocation(BuildContext context) {
    return InkWell(
      onTap: () {
        context.navigator.pushNamed(
          ShopLocationMapPreview.id,
          arguments: ShopLocationMapPreviewArguments(
            locationDetails,
            profileData.shopName,
          ),
        );
      },
      child: Padding(
        padding: commonScreenPadding,
        child: Row(
          children: [
            Expanded(
              child: MapLocationTile(
                labelText: _getLocationLabel(),
                infoText: _getLocationInfo(),
                labelStyle: context.appTextStyles.smallText1Bold,
              ),
            ),
            horizontalGapSixteen,
            IconWidget(
              assetName: ABAssets.arrowRightIcon,
              iconColor: context.appColors.neutralShadeDefaultColor,
            ),
          ],
        ),
      ),
    );
  }

  String _getLocationLabel() {
    if (locationDetails.address == null) {
      return "${locationDetails.latitude}, ${locationDetails.longitude}";
    } else {
      return locationDetails.address!.addressLabel;
    }
  }

  String? _getLocationInfo() {
    if (locationDetails.address == null) {
      return null;
    } else {
      return locationDetails.address!.addressText;
    }
  }

  String _getFormattedDate(LeoDate leoDate) {
    final String dateString = leoDate.date;
    final DateTime date = DateTime.parse(dateString);
    final DateFormat formatter = DateFormatter.dobFormat();
    return formatter.format(date);
  }
}

class ProfileInformationScreenArguments {
  final GetAgentProfileDataResponse profileData;
  final LocationDetails locationDetails;

  ProfileInformationScreenArguments({
    required this.profileData,
    required this.locationDetails,
  });
}
