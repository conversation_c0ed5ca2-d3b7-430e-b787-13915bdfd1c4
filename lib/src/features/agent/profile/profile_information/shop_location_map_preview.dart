import 'package:bcn_agency_banking_flutter/src/features/maps/map_preview_widget.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:sprintf/sprintf.dart';

import '../../../../resources/ab_assets.dart';
import '../../../../utils/constants.dart';
import '../../../../widgets/icon_widget.dart';

class ShopLocationMapPreview extends StatelessWidget {
  static const id = "/shop-location-map-preview";
  final LocationDetails shopLocationDetails;
  final String shopName;

  const ShopLocationMapPreview({
    Key? key,
    required this.shopLocationDetails,
    required this.shopName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: IconWidget(
            assetName: ABAssets.closeIcon,
            iconColor: context.appColors.genericWhiteColor,
          ),
          onPressed: context.navigator.pop,
        ),
        title: Text(context.localizations.shopLocation),
        actions: [
          IconButton(
            icon: IconWidget(
              assetName: ABAssets.shareAltIcon,
              iconColor: context.appColors.genericWhiteColor,
            ),
            onPressed: () => _shareLocation(context),
          ),
        ],
      ),
      body: MapPreviewWidget(
        latitude: shopLocationDetails.latitude,
        longitude: shopLocationDetails.longitude,
      ),
    );
  }

  void _shareLocation(BuildContext context) async {
    await Share.share(
      context.localizations.shareLocationMessage(
        shopName,
        sprintf(googleLocationQueryURL, [
          shopLocationDetails.latitude,
          shopLocationDetails.longitude,
        ]),
      ),
      subject: context.localizations.shareLocationMessageTitle,
    );
  }
}

class ShopLocationMapPreviewArguments {
  final LocationDetails shopLocationDetails;
  final String shopName;

  ShopLocationMapPreviewArguments(this.shopLocationDetails, this.shopName);
}
