import 'dart:io';

import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/bcn_logo.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppInfoScreen extends StatefulWidget {
  static const id = "/app-info";

  const AppInfoScreen({super.key});

  @override
  State<AppInfoScreen> createState() => _AppInfoScreenState();
}

class _AppInfoScreenState extends State<AppInfoScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.appInfo),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(child: _showAppInfo(context)),
            _buildShareUsageResult(context),
          ],
        ),
      ),
    );
  }

  SafeArea _buildShareUsageResult(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: horizontalPaddingSixteen.copyWith(bottom: dimenTwentyFour),
        child: Row(
          children: [
            horizontalGapSixteen,
            Text(
              context.localizations.shareUsageReports,
              style: context.appTextStyles.labelText2.copyWith(
                color: context.appColors.neutralShadeDefaultColor,
              ),
            ),
            const Spacer(),
            Switch(
              value:
                  FirebaseCrashlytics.instance.isCrashlyticsCollectionEnabled,
              onChanged: (value) async {
                final shouldApplySetting = await AgencyAppDialog.showAppDialog(
                  context: context,
                  contentText:
                      context.localizations.shareUsageReportsRestartApp,
                  actions:
                      (dialogContext) => [
                        PrimaryTextButton(
                          text: context.localizations.cancel,
                          onTap: () => dialogContext.navigator.pop(false),
                        ),
                        PrimaryTextButton(
                          text: context.localizations.closeApp,
                          onTap: () => dialogContext.navigator.pop(true),
                        ),
                      ],
                );
                if (shouldApplySetting) {
                  await FirebaseCrashlytics.instance
                      .setCrashlyticsCollectionEnabled(value);
                  setState(() {});
                  await Future.delayed(500.milliseconds);
                  exit(0);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Column _showAppInfo(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const BCNLogo(),
        verticalGapTwentyFour,
        Text(
          context.localizations.appTitle,
          style: context.appTextStyles.titleBold.copyWith(
            color: context.appColors.neutralShade1Color,
          ),
        ),
        FutureBuilder<PackageInfo>(
          future: PackageInfo.fromPlatform(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) return const SizedBox();
            return Text(
              "${snapshot.data!.version}(${snapshot.data!.buildNumber})",
              style: context.appTextStyles.labelText2.copyWith(
                color: context.appColors.neutralShade2Color,
              ),
            );
          },
        ),
      ],
    );
  }
}
