import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../core/auth/rpc_auth_provider.dart';
import '../../../../core/service_locator.dart';
import '../../../../flavors/flavors.dart';
import '../mock_impls/mock_change_agent_wallet.dart';
import '../mock_impls/mock_profile_page.dart';

class AgentProfileRepository {
  Future<LeoRPCResult<GetAgentProfileDataResponse, Never>>
  getAgentProfileData() async {
    final request = GetAgentProfileDataRequest();
    final agentProfileDataImpl =
        currentFlavor.isMock
            ? MockProfilePageImpl()
            : GetAgentProfileDataRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );

    return await agentProfileDataImpl.execute(request);
  }

  Future<LeoRPCResult<ChangeAgentAccountResponse, ChangeAgentAccountError>>
  changeAgentWallet(LeoUUID newAgentAccountId) async {
    final request = ChangeAgentAccountRequest(
      newAgentAccountId: newAgentAccountId,
    );
    final changeAgentWalletImpl =
        currentFlavor.isMock
            ? MockChangeAgentWalletImpl()
            : ChangeAgentAccountRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await changeAgentWalletImpl.execute(request);
  }

  Future<LeoRPCResult<ChangeLocaleResponse, Never>> changeLocale(
    Locale newLocale,
  ) async {
    final request = ChangeLocaleRequest(newLocale: newLocale);
    final changeLocaleImpl =
        currentFlavor.isMock
            ? MockChangeLocaleImpl()
            : ChangeLocaleRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );

    return await changeLocaleImpl.execute(request);
  }
}
