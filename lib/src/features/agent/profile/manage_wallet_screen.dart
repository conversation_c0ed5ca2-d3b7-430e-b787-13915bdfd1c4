import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/profile/widget/wallet_item.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../features/agent/profile/blocs/manage_wallet_bloc.dart';
import '../../../resources/dimensions.dart';
import '../../../widgets/primary_app_bar.dart';
import '../../../widgets/primary_text_button.dart';

class ManageWalletScreen extends StatefulWidget {
  static const id = "/manage-wallet-page";

  final GetAgentProfileDataResponse profileData;

  const ManageWalletScreen({Key? key, required this.profileData})
    : super(key: key);

  @override
  State<ManageWalletScreen> createState() => _ManageWalletScreenState();
}

class _ManageWalletScreenState extends State<ManageWalletScreen> {
  late final _manageWalletPageBloc = BlocProvider.of<ManageWalletBloc>(context);
  late final List<BCNAccountDetail> _wallets = widget.profileData.accounts;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.changeDefaultWallet),
      body: SafeArea(
        child: Padding(
          padding: verticalPaddingEight,
          child: BlocBuilder<ManageWalletBloc, ManageWalletState>(
            buildWhen: (_, ManageWalletState currentState) {
              return currentState is Initial ||
                  currentState is OnAgentWalletChanged;
            },
            builder: (context, state) {
              return SingleChildScrollView(
                child: Column(children: [_reactToState(), _buildSeparator()]),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _reactToState() {
    return _getWalletList(_manageWalletPageBloc.defaultWalletID);
  }

  Widget _getWalletList(LeoUUID agentWalletId) {
    final List<BCNAccountDetail> sortedWallets = _putDefaultWalletAtFirst(
      _wallets,
      agentWalletId,
    );
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: sortedWallets.length,
      separatorBuilder: (context, _) => _buildSeparator(),
      itemBuilder: (context, position) {
        return WalletItem<LeoUUID>(
          walletDetails: sortedWallets[position],
          defaultWalletUUID: agentWalletId,
          onChanged: (LeoUUID? newSelectedWallet) {
            if (newSelectedWallet != null &&
                agentWalletId != newSelectedWallet) {
              AgencyAppDialog.showAppDialog(
                context: context,
                actions:
                    (dialogContext) => [
                      PrimaryTextButton(
                        text: dialogContext.localizations.cancel.toUpperCase(),
                        onTap: dialogContext.navigator.pop,
                      ),
                      PrimaryTextButton(
                        text:
                            dialogContext.localizations.setAsDefault
                                .toUpperCase(),
                        onTap: () {
                          _manageWalletPageBloc.add(
                            ManageWalletEvent.changeAgentAccount(
                              newSelectedWallet,
                              context,
                            ),
                          );
                          dialogContext.navigator.pop();
                        },
                      ),
                    ],
                contentWidget:
                    (dialogContext) => Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          dialogContext
                              .localizations
                              .changeWalletConfirmationMessage,
                          style: dialogContext.appTextStyles.smallText1
                              .copyWith(
                                color:
                                    dialogContext.appColors.neutralShade1Color,
                              ),
                        ),
                        verticalGapTwelve,
                        Text(
                          sortedWallets[position].displayName,
                          style: dialogContext.appTextStyles.normalBold
                              .copyWith(
                                color:
                                    dialogContext.appColors.neutralShade1Color,
                              ),
                        ),
                      ],
                    ),
              );
            }
          },
        );
      },
    );
  }

  Divider _buildSeparator() =>
      const Divider(thickness: dimenOne, height: dimenOne);

  List<BCNAccountDetail> _putDefaultWalletAtFirst(
    List<BCNAccountDetail> wallets,
    LeoUUID agentWalletId,
  ) {
    for (int i = 0; i < wallets.length; ++i) {
      if (agentWalletId == wallets[i].accountId) {
        // If the wallet id matching the default wallet id
        // (agentWalletId in this case) then it is removed from it's current
        // position and inserted the starting of the list.
        final defaultWallet = wallets.removeAt(i);
        wallets.insert(0, defaultWallet);
        return wallets;
      }
    }
    throw DeveloperError("No Default Wallet found");
  }
}
