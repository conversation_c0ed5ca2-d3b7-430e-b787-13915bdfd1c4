import 'dart:typed_data';

import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:dedwig/dedwig.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../../utils/constants.dart';
import '../../../widgets/primary_app_bar.dart';
import '../../../widgets/server_error_widget.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  static const id = "/privacy-policy-screen";

  const PrivacyPolicyScreen({super.key});

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  Future<Uint8List>? _loadedPdf;

  @override
  Widget build(BuildContext context) {
    _loadedPdf ??= _loadPdf();
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.privacyPolicy),
      body: _getBody(context),
    );
  }

  Widget _getBody(context) {
    return FutureBuilder(
      future: _loadedPdf,
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return const Spinner();
        } else if (snapshot.hasData) {
          return _policyPdfView(context, snapshot.data!);
        } else if (snapshot.hasError) {
          return _errorWidgetIfNoInternet(snapshot.error);
        } else {
          throw DeveloperError("Unreachable condition!");
        }
      },
    );
  }

  ServerErrorWidget _errorWidgetIfNoInternet(Object? error) {
    if (error is NetworkException) {
      return ServerErrorWidget(
        onRetryButtonClicked: () {
          // To trigger a rebuild on the FutureBuilder, the `future` it's based
          // on needs to be changed somehow. On this part of the code, it's
          // certain that the data is not loaded into _loadedPdf so we can
          // assign it `null` safely to fulfill the purpose.
          setState(() => _loadedPdf = null);
        },
      );
    } else {
      throw DeveloperError(error.toString());
    }
  }

  SfPdfViewerTheme _policyPdfView(BuildContext context, Uint8List policyData) {
    return SfPdfViewerTheme(
      data: SfPdfViewerThemeData(
        backgroundColor: context.appColors.backgroundColor,
      ),
      child: SfPdfViewer.memory(
        policyData,
        scrollDirection: PdfScrollDirection.horizontal,
      ),
    );
  }

  Future<Uint8List> _loadPdf() async {
    final Uri uri = Uri.parse(privacyPolicyUrl);
    final Request request = Request(method: Method.get, path: uri);
    final AsyncAPIClient asyncAPIClient = AsyncAPIClient(
      configuration: APIClientConfiguration(baseURL: Uri()),
    );
    final Response response = await asyncAPIClient.sendRequest(request);
    return Uint8List.fromList(response.body.codeUnits);
  }
}
