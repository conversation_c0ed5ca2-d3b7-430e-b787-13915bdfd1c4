import 'package:agency_banking_rpcs/types/locale_information_type.dart';
import 'package:agency_banking_rpcs/types/locale_type.dart' as rpc_locale;
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../resources/ab_assets.dart';
import '../../../resources/dimensions.dart';
import '../../../widgets/icon_widget.dart';
import '../../../widgets/primary_app_bar.dart';
import 'blocs/change_language_bloc.dart';

class ChangeLanguageScreen extends StatefulWidget {
  static const id = "/change-language";

  const ChangeLanguageScreen({Key? key}) : super(key: key);

  @override
  State<ChangeLanguageScreen> createState() => _ChangeLanguageScreenState();
}

class _ChangeLanguageScreenState extends State<ChangeLanguageScreen> {
  late final _changeLanguageBloc = BlocProvider.of<ChangeLanguageBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.changeLanguage),
      body: SafeArea(
        child: Column(
          children: [
            verticalGapEight,
            Expanded(
              child: BlocBuilder<ChangeLanguageBloc, ChangeLanguageState>(
                builder: (context, state) {
                  if (state is Initial) return const SizedBox();
                  return ListView.builder(
                    itemCount: _changeLanguageBloc.supportedLocales.length,
                    itemBuilder: (context, index) {
                      final localeInformation =
                          _changeLanguageBloc.supportedLocales[index];
                      return _LanguageTile(
                        localeInformation: localeInformation,
                        currentLocale: _changeLanguageBloc.selectedLocale,
                        onTap: () {
                          _changeLanguageBloc.add(
                            ChangeLanguageEvent.onNewLocaleSelected(
                              context,
                              localeInformation.locale,
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
            Padding(
              padding: commonScreenPadding,
              child: _buildCTAButton(context),
            ),
          ],
        ),
      ),
    );
  }

  PrimaryButton _buildCTAButton(BuildContext context) {
    return PrimaryButton(
      labelText: context.localizations.changeLanguage,
      onPressed: () {
        _changeLanguageBloc.add(ChangeLanguageEvent.changeLocale(context));
      },
    );
  }
}

class _LanguageTile extends StatelessWidget {
  final LocaleInformation localeInformation;
  final VoidCallback onTap;
  final rpc_locale.Locale currentLocale;

  const _LanguageTile({
    Key? key,
    required this.localeInformation,
    required this.onTap,
    required this.currentLocale,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: commonScreenPadding.copyWith(
          // Radio already has a padding of eight px top and bottom.
          top: dimenEight,
          bottom: dimenEight,
        ),
        child: Row(
          children: [
            CachedNetworkImage(
              imageUrl: localeInformation.flag.getThemedImageUrl(context),
              height: dimenTwentyFour,
              width: dimenTwentyFour,
              errorWidget:
                  (context, url, error) => IconWidget(
                    assetName: ABAssets.failedToFetchIcon,
                    height: dimenTwentyFour,
                    width: dimenTwentyFour,
                  ),
            ),
            horizontalGapThirtyTwo,
            Expanded(
              child: Text(
                BCNLocale.getLocalisedString(
                  context,
                  localeInformation.displayName,
                ),
                style: context.appTextStyles.smallText1.copyWith(
                  color: context.appColors.neutralShade1Color,
                ),
              ),
            ),
            Radio<rpc_locale.Locale>(
              value: localeInformation.locale,
              groupValue: currentLocale,
              onChanged: (_) => onTap(),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ],
        ),
      ),
    );
  }
}
