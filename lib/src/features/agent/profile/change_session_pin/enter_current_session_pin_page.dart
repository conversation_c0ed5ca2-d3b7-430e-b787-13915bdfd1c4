import 'package:bcn_agency_banking_flutter/src/features/agent/profile/blocs/change_session_pin_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/agency_auth_pin_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../helpers/helpers.dart';
import '../../../../widgets/bcn_logo.dart';
import '../../../../widgets/primary_text_button.dart';

class EnterCurrentSessionPinPage extends StatelessWidget {
  const EnterCurrentSessionPinPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          verticalGapEighty,
          const BCNLogo(),
          verticalGapTwentyFour,
          _buildTitle(context),
          _buildErrorIfAny(),
          verticalGapTwentyFour,
          _buildAuthPinField(context),
          _buildForgotSessionPIN(context),
        ],
      ),
    );
  }

  Text _buildTitle(BuildContext context) {
    return Text(
      context.localizations.enterCurrentSessionPIN,
      style: context.appTextStyles.titleBold.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
      textAlign: TextAlign.center,
    );
  }

  BlocBuilder<ChangeSessionPinBloc, ChangeSessionPinState> _buildErrorIfAny() {
    return BlocBuilder<ChangeSessionPinBloc, ChangeSessionPinState>(
      buildWhen: (oldState, newState) {
        return newState is WrongSessionPin;
      },
      builder: (context, state) {
        if (state is! WrongSessionPin) return const SizedBox();
        return Padding(
          padding: const EdgeInsets.only(top: dimenFour),
          child: Text(
            state.attemptsLeft > 1
                ? context.localizations.wrongSessionPinEnteredError(
                  state.attemptsLeft,
                )
                : context.localizations.singleAttemptLeftError,
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.errorTextFieldColor,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  AgencyAuthPinField _buildAuthPinField(BuildContext context) {
    return AgencyAuthPinField(
      onPinComplete: (pin, clear) {
        final currentSessionPinBloc = BlocProvider.of<ChangeSessionPinBloc>(
          context,
        );
        currentSessionPinBloc.add(
          ChangeSessionPinEvent.checkCurrentSessionPin(context, pin),
        );
        clear();
      },
    );
  }

  Padding _buildForgotSessionPIN(BuildContext context) {
    return Padding(
      padding: commonScreenPadding.copyWith(top: dimenTwelve),
      child: PrimaryTextButton(
        text: context.localizations.forgotSessionPin,
        fixedSize: buttonHeight,
        onTap: () {
          dismissKeyboard();
          final currentSessionPinBloc = BlocProvider.of<ChangeSessionPinBloc>(
            context,
          );
          currentSessionPinBloc.add(
            ChangeSessionPinEvent.forgotPassword(context),
          );
        },
      ),
    );
  }
}
