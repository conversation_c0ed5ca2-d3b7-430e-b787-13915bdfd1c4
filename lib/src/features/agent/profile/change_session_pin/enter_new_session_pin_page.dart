import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../resources/dimensions.dart';
import '../../../../widgets/agency_auth_pin_field.dart';
import '../../../../widgets/bcn_logo.dart';
import '../blocs/change_session_pin_bloc.dart';

class EnterNewSessionPinPage extends StatelessWidget {
  const EnterNewSessionPinPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          verticalGapEighty,
          const BCNLogo(),
          verticalGapTwentyFour,
          _buildTitle(context),
          verticalGapTwentyFour,
          _buildAuthPinField(context),
        ],
      ),
    );
  }

  Text _buildTitle(BuildContext context) {
    return Text(
      context.localizations.enterNewSessionPIN,
      style: context.appTextStyles.titleBold.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
      textAlign: TextAlign.center,
    );
  }

  AgencyAuthPinField _buildAuthPinField(BuildContext context) {
    return AgencyAuthPinField(
      onPinComplete: (pin, clear) {
        final changeSessionPinBloc = BlocProvider.of<ChangeSessionPinBloc>(
          context,
        );
        changeSessionPinBloc.add(ChangeSessionPinEvent.newSessionPin(pin));
        clear();
      },
    );
  }
}
