import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../resources/dimensions.dart';
import '../../../../widgets/agency_auth_pin_field.dart';
import '../../../../widgets/bcn_logo.dart';
import '../blocs/change_session_pin_bloc.dart';

class ConfirmNewSessionPinPage extends StatelessWidget {
  const ConfirmNewSessionPinPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          verticalGapEighty,
          const BCNLogo(),
          verticalGapTwentyFour,
          _buildTitle(context),
          _buildWarningIfAny(),
          _buildErrorIfAny(),
          verticalGapTwentyFour,
          _buildAuthPinField(context),
          _buildBypassSessionPINViaBiometricsSwitch(context),
        ],
      ),
    );
  }

  Text _buildTitle(BuildContext context) {
    return Text(
      context.localizations.confirmNewSessionPIN,
      style: context.appTextStyles.titleBold.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
      textAlign: TextAlign.center,
    );
  }

  BlocBuilder<ChangeSessionPinBloc, ChangeSessionPinState>
  _buildWarningIfAny() {
    return BlocBuilder<ChangeSessionPinBloc, ChangeSessionPinState>(
      buildWhen:
          (_, newState) =>
              newState is BiometricsUnavailable ||
              newState is EnableBypassSwitch,
      builder: (context, state) {
        if (state is! BiometricsUnavailable) {
          return const SizedBox();
        }
        return Padding(
          padding: commonScreenPadding.copyWith(
            top: dimenFour,
            bottom: dimenZero,
          ),
          child: Text(
            context.localizations.biometricsNotAvailable,
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.warningColor,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  BlocBuilder<ChangeSessionPinBloc, ChangeSessionPinState> _buildErrorIfAny() {
    return BlocBuilder<ChangeSessionPinBloc, ChangeSessionPinState>(
      buildWhen: (_, newState) {
        return newState is ConfirmPinAndSessionPinMatches ||
            newState is ConfirmPinAndSessionPinNotMatch;
      },
      builder: (context, state) {
        if (state is ConfirmPinAndSessionPinNotMatch) {
          return Padding(
            padding: commonScreenPadding.copyWith(
              top: dimenFour,
              bottom: dimenZero,
            ),
            child: Text(
              context.localizations.sessionPinDoesNotMatchWithConfirmPin,
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.errorTextFieldColor,
              ),
              textAlign: TextAlign.center,
            ),
          );
        }
        return const SizedBox();
      },
    );
  }

  AgencyAuthPinField _buildAuthPinField(BuildContext context) {
    return AgencyAuthPinField(
      onPinComplete: (pin, clear) {
        final changeSessionPinBloc = BlocProvider.of<ChangeSessionPinBloc>(
          context,
        );
        changeSessionPinBloc.add(
          ChangeSessionPinEvent.confirmNewSessionPIN(context, pin),
        );
        clear();
      },
    );
  }

  BlocBuilder<ChangeSessionPinBloc, ChangeSessionPinState>
  _buildBypassSessionPINViaBiometricsSwitch(BuildContext context) {
    final changeSessionPinBloc = BlocProvider.of<ChangeSessionPinBloc>(context);
    return BlocBuilder<ChangeSessionPinBloc, ChangeSessionPinState>(
      buildWhen: (_, newState) {
        // Only build for these states.
        return newState is SetBiometricsValue ||
            newState is EnableBypassSwitch ||
            newState is DisableBypassSwitch;
      },
      builder: (context, state) {
        if (state is SetBiometricsValue &&
            !state.doesDeviceHasBiometricsHardware) {
          return const SizedBox();
        }
        return Padding(
          padding: commonScreenPadding.copyWith(top: dimenTwentyFour),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.localizations.bypassPinUsingBiometrics,
                style: context.appTextStyles.smallText1.copyWith(
                  color: context.appColors.neutralShadeDefaultColor,
                ),
              ),
              Switch(
                value:
                    changeSessionPinBloc
                        .bypassSessionPinByBiometricsSwitchValue,
                onChanged: (shouldBypass) {
                  changeSessionPinBloc.add(
                    ChangeSessionPinEvent.setBypassSessionPinUsingBiometrics(
                      shouldBypass,
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
