import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../blocs/change_session_pin_bloc.dart';
import 'confirm_new_session_page.dart';
import 'enter_current_session_pin_page.dart';
import 'enter_new_session_pin_page.dart';

class ChangeSessionPinScreen extends StatefulWidget {
  static const id = "/change-session-pin";

  const ChangeSessionPinScreen({Key? key}) : super(key: key);

  @override
  State<ChangeSessionPinScreen> createState() => _ChangeSessionPinScreenState();
}

class _ChangeSessionPinScreenState extends State<ChangeSessionPinScreen> {
  late final changeSessionPinBloc = BlocProvider.of<ChangeSessionPinBloc>(
    context,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.changeSessionPIN),
      body: PageView(
        controller: changeSessionPinBloc.pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: const [
          EnterCurrentSessionPinPage(),
          EnterNewSessionPinPage(),
          ConfirmNewSessionPinPage(),
        ],
      ),
    );
  }
}
