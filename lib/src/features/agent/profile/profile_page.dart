import 'package:bcn_agency_banking_flutter/src/features/agent/profile/app_info_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/profile/privacy_policy_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/profile/profile_information/profile_information_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/profile/third_party_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/server_error_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/user_details_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/locale_service/bcn_locale.dart';
import '../../../core/logger.dart';
import '../../../features/agent/profile/blocs/profile_page_bloc.dart';
import '../../../helpers/helpers.dart';
import '../../../resources/ab_assets.dart';
import '../../../utils/constants.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/icon_widget.dart';
import '../../../widgets/icon_with_label.dart';
import 'change_language_screen.dart';
import 'change_session_pin/change_session_pin_screen.dart';
import 'manage_wallet_screen.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with RPCHandler {
  late final _profilePageBloc = BlocProvider.of<ProfilePageBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.localizations.profile),
        centerTitle: false,
        automaticallyImplyLeading: false,
      ),
      body: BlocBuilder<ProfilePageBloc, ProfilePageState>(
        builder: (context, state) {
          return _reactToState(context, state);
        },
      ),
    );
  }

  Widget _reactToState(BuildContext context, ProfilePageState state) {
    if (state is OnData) {
      return _getProfileScreen(context);
    } else if (state is TransientError) {
      return NoInternetWidget(onRetryButtonClicked: _reloadProfile);
    } else if (state is Loading) {
      return const Spinner();
    } else if (state is ServerError) {
      return ServerErrorWidget(onRetryButtonClicked: _reloadProfile);
    } else {
      return const SizedBox();
    }
  }

  Widget _getProfileScreen(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _getProfileDetailsSection(context),
          _buildSeparator(context),
          _getProfileMenuSection(context),
        ],
      ),
    );
  }

  Container _buildSeparator(BuildContext context) {
    return Container(
      height: dimenSixteen,
      color: context.appColors.titleBackgroundColor,
    );
  }

  Widget _getProfileDetailsSection(BuildContext context) {
    return UserDetailsCard(
      name: _profilePageBloc.agentFullName,
      subTitle: _profilePageBloc.profileData.shopName,
      image: _profilePageBloc.profileData.profileImage,
      userImageSize: dimenSixtyFour,
      nameStyle: context.appTextStyles.labelText1Bold,
      thirdLineContent:
          _profilePageBloc.profileData.phoneNumber.formattedPhoneNumber,
    );
  }

  Widget _getProfileMenuSection(BuildContext context) {
    final profileMenuItems = _getProfileMenuItems(context);
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: verticalPaddingEight,
      itemBuilder: (context, index) => profileMenuItems[index],
      itemCount: profileMenuItems.length,
    );
  }

  List<Widget> _getProfileMenuItems(BuildContext context) {
    return [
      _buildProfileInformationItem(context),
      if (_profilePageBloc.profileData.accounts.length > 1)
        _buildChangeDefaultWalletItem(context),
      if (supportChangeLanguageFeature) _buildChangeLanguageItem(),
      _buildChangeSessionPinItem(context),
      _buildThirdPartySoftwareItem(context),
      _buildPrivacyPolicyItem(context),
      _buildAppInfoItem(context),
      verticalGapEight,
      _buildSeparator(context),
      verticalGapEight,
      _buildSignOutItem(context),
    ];
  }

  _ProfileItem _buildThirdPartySoftwareItem(BuildContext context) {
    return _ProfileItem(
      assetName: ABAssets.arrowIcon,
      label: context.localizations.thirdPartySoftware,
      onTap: () async {
        l.d("Opened Third Party software screen");
        await context.navigator.pushNamed(ThirdPartySoftwareScreen.id);
        if (context.mounted) {
          _profilePageBloc.add(ProfilePageEvent.getProfileData(context));
        }
      },
    );
  }

  _ProfileItem _buildSignOutItem(BuildContext context) {
    return _ProfileItem(
      assetName: ABAssets.signOutIcon,
      label: context.localizations.signOut,
      onTap: () async {
        l.d("Opened Sign out dialog");
        final shouldSignOutUser = await AgencyAppDialog.showConfirmationDialog(
          context: context,
          contentText: context.localizations.confirmSignOut,
        );
        if (shouldSignOutUser ?? false) {
          if (context.mounted) {
            AgencyAppDialog.showSpinnerDialog(context);
          }
          await rpcHandler(
            () async => await signOutUser(context),
            onTransientError: (_) {
              // Close the spinner dialog.
              context.rootNavigator.pop();
            },
          );
        }
      },
    );
  }

  _ProfileItem _buildPrivacyPolicyItem(BuildContext context) {
    return _ProfileItem(
      assetName: ABAssets.shieldCheckIcon,
      label: context.localizations.privacyPolicy,
      onTap: () async {
        l.d("Opened Privacy Policy screen");
        await context.navigator.pushNamed(PrivacyPolicyScreen.id);
        if (context.mounted) {
          _profilePageBloc.add(ProfilePageEvent.getProfileData(context));
        }
      },
    );
  }

  _ProfileItem _buildChangeSessionPinItem(BuildContext context) {
    return _ProfileItem(
      assetName: ABAssets.lockIcon,
      label: context.localizations.changeSessionPIN,
      onTap: () async {
        l.d("Opened Change session PIN screen");
        await context.navigator.pushNamed(ChangeSessionPinScreen.id);
        if (context.mounted) {
          _profilePageBloc.add(ProfilePageEvent.getProfileData(context));
        }
      },
    );
  }

  _ProfileItem _buildChangeDefaultWalletItem(BuildContext context) {
    return _ProfileItem(
      assetName: ABAssets.walletIcon,
      label: context.localizations.changeDefaultWallet,
      onTap: () async {
        l.d("Opened Manage Wallet screen");
        await context.navigator.pushNamed(
          ManageWalletScreen.id,
          arguments: _profilePageBloc.profileData,
        );
        if (context.mounted) {
          _profilePageBloc.add(ProfilePageEvent.getProfileData(context));
        }
      },
    );
  }

  _ProfileItem _buildProfileInformationItem(BuildContext context) {
    return _ProfileItem(
      assetName: ABAssets.userIcon,
      label: context.localizations.profileInformation,
      onTap: () async {
        l.d("Opened Profile information screen");
        await context.navigator.pushNamed(
          ProfileInformationScreen.id,
          arguments: ProfileInformationScreenArguments(
            profileData: _profilePageBloc.profileData,
            locationDetails: _profilePageBloc.locationDetail,
          ),
        );
        if (mounted) {
          _profilePageBloc.add(ProfilePageEvent.getProfileData(this.context));
        }
      },
    );
  }

  Widget _buildChangeLanguageItem() {
    return _ProfileItem(
      assetName: ABAssets.languageIcon,
      label: context.localizations.changeLanguage,
      onTap: () async {
        await context.rootNavigator.pushNamed(
          ChangeLanguageScreen.id,
          arguments: _profilePageBloc.profileData.supportedLocales,
        );
        if (mounted) {
          _profilePageBloc.add(ProfilePageEvent.getProfileData(context));
        }
      },
      trailingText: BCNLocale.getLocalisedString(
        context,
        _profilePageBloc.currentLocale.displayName,
      ),
    );
  }

  void _reloadProfile() {
    _profilePageBloc.add(ProfilePageEvent.getProfileData(context));
  }

  Widget _buildAppInfoItem(BuildContext context) {
    return _ProfileItem(
      assetName: ABAssets.infoCircleIcon,
      label: context.localizations.appInfo,
      onTap: () async {
        l.d("Opened App Info Screen");
        await context.navigator.pushNamed(AppInfoScreen.id);
        if (context.mounted) {
          _profilePageBloc.add(ProfilePageEvent.getProfileData(context));
        }
      },
    );
  }
}

class _ProfileItem extends StatelessWidget {
  final String assetName;
  final String label;
  final String? trailingText;
  final VoidCallback onTap;

  const _ProfileItem({
    Key? key,
    required this.assetName,
    required this.label,
    required this.onTap,
    this.trailingText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IconWithLabel(
      height: dimenFiftySix,
      icon: IconWidget(
        assetName: assetName,
        iconColor: context.appColors.primaryLightColor,
      ),
      label: label,
      trailing:
          trailingText != null
              ? Text(
                trailingText!,
                style: context.appTextStyles.labelText2.copyWith(
                  color: context.appColors.neutralShade2Color,
                ),
              )
              : null,
      padding: horizontalPaddingSixteen,
      spacing: dimenThirtyTwo,
      onTap: onTap,
    );
  }
}
