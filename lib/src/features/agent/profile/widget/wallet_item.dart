import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../utils/constants.dart';

class WalletItem<T> extends StatelessWidget {
  final LeoUUID defaultWalletUUID;
  final BCNAccountDetail walletDetails;
  final ValueChanged<LeoUUID?> onChanged;

  const WalletItem({
    Key? key,
    required this.defaultWalletUUID,
    required this.walletDetails,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap:
          walletDetails.accountId != defaultWalletUUID
              ? () => onChanged(walletDetails.accountId)
              : () {},
      child: _getRadioButton(context),
    );
  }

  Widget _getRadioButton(BuildContext context) {
    return Padding(
      padding: commonScreenPadding,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  walletDetails.displayName,
                  style: context.appTextStyles.titleBold.copyWith(
                    color: context.appColors.neutralShade1Color,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: walletDisplayNameMaxLines,
                ),
                verticalGapFour,
                Text(
                  context.localizations.walletIdDetails(
                    walletDetails.accountId.uuid.substring(
                      walletDetails.accountId.uuid.length - walletIdMaskLength,
                    ),
                  ),
                  style: context.appTextStyles.smallText1.copyWith(
                    color: context.appColors.neutralShade7Color,
                  ),
                ),
                if (defaultWalletUUID == walletDetails.accountId) ...{
                  verticalGapFour,
                  Text(
                    context.localizations.defaultWallet,
                    style: context.appTextStyles.smallText1Bold.copyWith(
                      color: context.appColors.successShade1Color,
                    ),
                  ),
                },
              ],
            ),
          ),
          Radio(
            value: walletDetails.accountId,
            groupValue: defaultWalletUUID,
            onChanged: onChanged,
            activeColor: context.appColors.primaryLightColor,
          ),
        ],
      ),
    );
  }
}
