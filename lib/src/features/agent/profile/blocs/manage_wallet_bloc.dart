import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../errors/developer_error.dart';
import '../../../../helpers/prominent_error_handlers.dart';
import '../../../../helpers/rpc_handler.dart';
import '../../../../widgets/dialogs.dart';
import '../repositories/agent_profile_repository.dart';

part 'manage_wallet_bloc.freezed.dart';

class ManageWalletBloc extends Bloc<ManageWalletEvent, ManageWalletState>
    with RPCHandler {
  final _profilePageRepository = AgentProfileRepository();
  LeoUUID defaultWalletID;

  ManageWalletBloc(this.defaultWalletID) : super(const Initial()) {
    on<ManageWalletEvent>((event, emit) async {
      switch (event) {
        case ChangeAgentAccount(:final newAgentAccountId, :final context):
          await _changeAgentWallet(context, emit, newAgentAccountId);
      }
    });
  }

  Future<void> _changeAgentWallet(
    BuildContext context,
    Emitter<ManageWalletState> emit,
    LeoUUID newAgentWalletDefaultId,
  ) async {
    AgencyAppDialog.showSpinnerDialog(context);
    emit(const ManageWalletState.loading());
    await rpcHandler(
      () async {
        final rpcResult = await _profilePageRepository.changeAgentWallet(
          newAgentWalletDefaultId,
        );
        if (context.mounted) {
          context.navigator.pop();
        }
        rpcResult.when(
          response: (response) {
            defaultWalletID = newAgentWalletDefaultId;
            emit(
              ManageWalletState.onAgentWalletChanged(newAgentWalletDefaultId),
            );
          },
          error: (error) {
            emit(const ManageWalletState.prominentError());
            error.when(
              incorrectAccountId: (e) {
                throw DeveloperError("The account ID is invalid. $e");
              },
              inactiveAccount: (_) {
                AgencyAppDialog.showErrorDialog(
                  context: context,
                  contentText:
                      context
                          .localizations
                          .changeAgentAccountInActiveAccountErrorMessage,
                );
              },
              agentDisabledForSpecificInterval: (_) {
                ProminentErrorHandler.agentDisabledForSpecificInterval(
                  onOkay: context.navigator.pop,
                );
              },
            );
          },
        );
      },
      onTransientError: (_) {
        context.navigator.pop();
        emit(const ManageWalletState.transientError());
      },
    );
  }
}

@freezed
sealed class ManageWalletState with _$ManageWalletState {
  const factory ManageWalletState.initial() = Initial;

  const factory ManageWalletState.loading() = Loading;

  const factory ManageWalletState.prominentError() = ProminentError;

  const factory ManageWalletState.transientError() = TransientError;

  const factory ManageWalletState.onAgentWalletChanged(
    LeoUUID newAgentAccountId,
  ) = OnAgentWalletChanged;
}

@freezed
sealed class ManageWalletEvent with _$ManageWalletEvent {
  const factory ManageWalletEvent.changeAgentAccount(
    LeoUUID newAgentAccountId,
    BuildContext context,
  ) = ChangeAgentAccount;
}
