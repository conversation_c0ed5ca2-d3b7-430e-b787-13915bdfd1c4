import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../core/logger.dart';
import '../../../../errors/developer_error.dart';
import '../../../../helpers/rpc_handler.dart';
import '../../../../models/location_details.dart';
import '../repositories/agent_profile_repository.dart';

part 'profile_page_bloc.freezed.dart';

class ProfilePageBloc extends Bloc<ProfilePageEvent, ProfilePageState>
    with RPCHandler {
  final _profilePageRepository = AgentProfileRepository();
  GetAgentProfileDataResponse? _getAgentProfileDataResponse;
  LocaleInformation? _getCurrentRPCLocale;
  LocationDetails? _locationDetails;

  ProfilePageBloc() : super(const Initial()) {
    on<ProfilePageEvent>((event, emit) async {
      switch (event) {
        case GetProfileData(:final context):
          emit(const ProfilePageState.loading());
          await _getProfileData(context, emit);
      }
    });
  }

  Future<void> _getProfileData(
    BuildContext context,
    Emitter<ProfilePageState> emit,
  ) async {
    l.d("Getting Profile data");
    await rpcHandler(
      () async {
        final rpcResult = await _profilePageRepository.getAgentProfileData();
        l.d("GetAgentProfileData result: $rpcResult");
        await rpcResult.when(
          response: (response) async {
            _getAgentProfileDataResponse = response;
            final Locale currentLocale =
                await BCNLocale.fetchCurrentRPCLocale();
            l.d("Current locale: $currentLocale");
            _getCurrentRPCLocale = _getAgentProfileDataResponse!
                .supportedLocales
                .firstWhere(
                  (element) => element.locale == currentLocale,
                  orElse: () {
                    l.d("Unsupported locale");
                    throw DeveloperError(
                      "The current locale is not present in the supported locale.",
                    );
                  },
                );
            _setLocationDetails();
            emit(const ProfilePageState.onData());
          },
          error: (error) {
            //Error type is Never.
            throw DeveloperError("Something went wrong: $error");
          },
        );
      },
      onTransientError: (_) {
        l.d("Emitting Transient Error");
        emit(const ProfilePageState.transientError());
      },
      onServerError: () {
        // If the Bloc is closed, don't call the RPC.
        if (isClosed) return;
        emit(const ProfilePageState.serverError());
      },
    );
  }

  Future<void> _setLocationDetails() async {
    final Coordinate shopCoordinate = profileData.shopCoordinate;
    final FormattedAddress? formattedAddress = await getReverseGeocode(
      shopCoordinate.latitude,
      shopCoordinate.longitude,
    );
    _locationDetails = LocationDetails(
      latitude: shopCoordinate.latitude,
      longitude: shopCoordinate.longitude,
      address: formattedAddress,
    );
    l.d("Shop location details: $_locationDetails");
  }

  GetAgentProfileDataResponse get profileData => _getAgentProfileDataResponse!;

  String get agentFullName =>
      "${profileData.userPersonalDetails.firstName} ${profileData.userPersonalDetails.lastName ?? ""}";

  LocaleInformation get currentLocale => _getCurrentRPCLocale!;

  LocationDetails get locationDetail => _locationDetails!;
}

@freezed
sealed class ProfilePageState with _$ProfilePageState {
  const factory ProfilePageState.initial() = Initial;

  const factory ProfilePageState.loading() = Loading;

  const factory ProfilePageState.transientError() = TransientError;

  const factory ProfilePageState.serverError() = ServerError;

  const factory ProfilePageState.onData() = OnData;
}

@freezed
sealed class ProfilePageEvent with _$ProfilePageEvent {
  const factory ProfilePageEvent.getProfileData(BuildContext context) =
      GetProfileData;
}
