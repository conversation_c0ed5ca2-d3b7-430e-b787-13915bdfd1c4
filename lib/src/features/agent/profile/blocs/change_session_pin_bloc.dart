import 'package:bcn_agency_banking_flutter/src/core/auth/app_pin_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../core/logger.dart';
import '../../../../helpers/helpers.dart';
import '../../../../widgets/primary_text_button.dart';
import '../../home_screen.dart';

part 'change_session_pin_bloc.freezed.dart';

class ChangeSessionPinBloc
    extends Bloc<ChangeSessionPinEvent, ChangeSessionPinState> {
  final PageController pageController = PageController();
  late final String _currentSessionPin;
  late int _attemptsLeft;
  bool bypassSessionPinByBiometricsSwitchValue = false;
  late String _newSessionPin;

  ChangeSessionPinBloc() : super(const Loading()) {
    on<ChangeSessionPinEvent>((event, emit) async {
      switch (event) {
        case Initialise(:final context):
          l.d("Changing Session PIN initialisation");
          AgencyAppDialog.showSpinnerDialog(context);
          _currentSessionPin = (await AppPinProvider.getAppPin())!;
          _attemptsLeft = await AppPinProvider.getRemainingAppPinAttempts();
          if (_attemptsLeft <= 0 && context.mounted) {
            await _forceSignUserOutDueToTooManyAttempts(context);
          }
          if (context.mounted) {
            // Pop the spinner dialog.
            context.navigator.pop();
          }
          emit(const ChangeSessionPinState.initial());
        case CheckCurrentSessionPin(:final context, :final pin):
          l.d("Checking current session PIN with user entered PIN");
          emit(const ChangeSessionPinState.loading());
          if (_currentSessionPin != pin) {
            _attemptsLeft--;
            l.d("Decreasing Attempts left. attemptsLeft: $_attemptsLeft");
            AppPinProvider.setRemainingAppPinAttempts(_attemptsLeft);
            if (_attemptsLeft == 0) {
              await _forceSignUserOutDueToTooManyAttempts(context);
            }
            emit(ChangeSessionPinState.wrongSessionPin(_attemptsLeft));
          } else {
            AppPinProvider.setRemainingAppPinAttempts(maximumIncorrectAttempts);
            // Go to EnterNewSessionPinPage.
            goToNextPage();
          }
        case ForgotPassword(:final context):
          l.d("Forgot Session PIN");
          final bool shouldSignOut = await _showForgotSessionPinDialog(context);
          if (shouldSignOut && context.mounted) {
            _signUserOut(context);
          }
        case NewSessionPin(:final pin):
          _newSessionPin = pin;
          goToNextPage();
          await _setSwitchValue(emit);
        case SetBypassSessionPinUsingBiometrics(:final shouldBypass):
          if (shouldBypass) {
            final bool canCheckBiometrics =
                await AppPinProvider.canDeviceCheckBiometrics();
            final bool isBiometricEnrolled =
                await AppPinProvider.isBiometricEnrolled();
            bypassSessionPinByBiometricsSwitchValue =
                canCheckBiometrics && isBiometricEnrolled;
            l.d('''Should ByPass Session PIN 
            canCheckBiometrics: $canCheckBiometrics
            isBiometricEnrolled: $isBiometricEnrolled''');
            if (bypassSessionPinByBiometricsSwitchValue == false) {
              emit(const ChangeSessionPinState.biometricsUnavailable());
              emit(const ChangeSessionPinState.disableBypassSwitch());
            } else {
              emit(const ChangeSessionPinState.enableBypassSwitch());
            }
          } else {
            bypassSessionPinByBiometricsSwitchValue = false;
            emit(const ChangeSessionPinState.disableBypassSwitch());
          }
        case ConfirmNewSessionPIN(:final context, pin: final confirmPin):
          l.d("Confirming new Session PIN");
          if (confirmPin == _newSessionPin) {
            l.d("New Session PIN confirmed");
            emit(const ChangeSessionPinState.confirmPinAndSessionPinMatches());
            AgencyAppDialog.showSpinnerDialog(context);
            await AppPinProvider.setAppPin(confirmPin);
            await AppPinProvider.setRemainingAppPinAttempts(
              maximumIncorrectAttempts,
            );
            if (context.mounted) {
              // Pop the spinner dialog.
              context.rootNavigator.pop();

              await AppPinProvider.setBiometricsAuthenticationPreference(
                isEnabled: bypassSessionPinByBiometricsSwitchValue,
                localisedReason: context.localizations.identificationIsRequired,
                onLockedOut: () async {
                  await _showBiometricsVerificationFailedError(context);
                  if (context.mounted) {
                    await _showSessionPINChangedDialog(context);
                  }
                },
                onAuthenticationCancelled: () async {
                  await _showBiometricsVerificationFailedError(context);
                  if (context.mounted) {
                    await _showSessionPINChangedDialog(context);
                  }
                },
                onSuccess: () async {
                  await _showSessionPINChangedDialog(context);
                },
              );
            }
          } else {
            emit(const ChangeSessionPinState.confirmPinAndSessionPinNotMatch());
          }
      }
    });
  }

  Future<void> _showSessionPINChangedDialog(BuildContext context) async {
    await AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.sessionPinChangedMessage,
      onOkay: (dialogContext) {
        dialogContext.navigator.popUntil(ModalRoute.withName(HomeScreen.id));
      },
    );
  }

  Future<void> _showBiometricsVerificationFailedError(
    BuildContext context,
  ) async {
    await AppPinProvider.resetBiometricsPreferences();
    if (context.mounted) {
      await AgencyAppDialog.showErrorDialog(
        context: context,
        contentText: context.localizations.biometricsVerificationFailedError,
      );
    }
  }

  Future<void> _forceSignUserOutDueToTooManyAttempts(
    BuildContext context,
  ) async {
    l.d("Force Sign out due to too many attempts");
    await forceSignOutUser(context);
    // Since after navigation,
    // the above context will become defunct,
    // we will take context from navigation service.
    _showForceSignOutDialog(NavigationService.navigatorKey.currentContext!);
  }

  Future<bool> _showForgotSessionPinDialog(BuildContext context) async {
    return await AgencyAppDialog.showAppDialog(
      context: context,
      contentText: context.localizations.forgotSessionPinDialogMessage,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: dialogContext.localizations.cancel,
              onTap: () => context.navigator.pop(false),
            ),
            PrimaryTextButton(
              text: dialogContext.localizations.signOut,
              onTap: () => context.navigator.pop(true),
            ),
          ],
    );
  }

  void _signUserOut(BuildContext context) async {
    await forceSignOutUser(context);
  }

  void goToNextPage() => pageController.jumpToNextPage;

  void _showForceSignOutDialog(BuildContext context) async {
    await AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.tooManyAppPinAttempts,
    );
  }

  Future<void> _setSwitchValue(Emitter<ChangeSessionPinState> emit) async {
    final bool canCheckBiometrics =
        await AppPinProvider.canDeviceCheckBiometrics();
    final bool isBiometricEnrolled = await AppPinProvider.isBiometricEnrolled();
    bypassSessionPinByBiometricsSwitchValue =
        canCheckBiometrics && isBiometricEnrolled;
    emit(
      ChangeSessionPinState.setBypassSessionPinByBiometrics(
        doesDeviceHasBiometricsHardware: canCheckBiometrics,
        doesUserEnrolledBiometrics: isBiometricEnrolled,
      ),
    );
  }
}

@freezed
sealed class ChangeSessionPinState with _$ChangeSessionPinState {
  const factory ChangeSessionPinState.initial() = Initial;

  const factory ChangeSessionPinState.loading() = Loading;

  const factory ChangeSessionPinState.wrongSessionPin(int attemptsLeft) =
      WrongSessionPin;

  const factory ChangeSessionPinState.setBypassSessionPinByBiometrics({
    required bool doesDeviceHasBiometricsHardware,
    required bool doesUserEnrolledBiometrics,
  }) = SetBiometricsValue;

  const factory ChangeSessionPinState.biometricsUnavailable() =
      BiometricsUnavailable;

  const factory ChangeSessionPinState.enableBypassSwitch() = EnableBypassSwitch;

  const factory ChangeSessionPinState.disableBypassSwitch() =
      DisableBypassSwitch;

  const factory ChangeSessionPinState.confirmPinAndSessionPinNotMatch() =
      ConfirmPinAndSessionPinNotMatch;

  const factory ChangeSessionPinState.confirmPinAndSessionPinMatches() =
      ConfirmPinAndSessionPinMatches;
}

@freezed
sealed class ChangeSessionPinEvent with _$ChangeSessionPinEvent {
  const factory ChangeSessionPinEvent.initialise(BuildContext context) =
      Initialise;

  const factory ChangeSessionPinEvent.checkCurrentSessionPin(
    BuildContext context,
    String pin,
  ) = CheckCurrentSessionPin;

  const factory ChangeSessionPinEvent.forgotPassword(BuildContext context) =
      ForgotPassword;

  const factory ChangeSessionPinEvent.newSessionPin(String pin) = NewSessionPin;

  const factory ChangeSessionPinEvent.setBypassSessionPinUsingBiometrics(
    bool shouldBypass,
  ) = SetBypassSessionPinUsingBiometrics;

  const factory ChangeSessionPinEvent.confirmNewSessionPIN(
    BuildContext context,
    String pin,
  ) = ConfirmNewSessionPIN;
}
