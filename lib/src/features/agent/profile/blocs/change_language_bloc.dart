import 'package:agency_banking_rpcs/types/locale_information_type.dart';
import 'package:agency_banking_rpcs/types/locale_type.dart' as rpc_locale;
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../core/locale_service/bcn_locale.dart';
import '../../../../errors/developer_error.dart';
import '../../../../helpers/rpc_handler.dart';
import '../../../../widgets/dialogs.dart';
import '../repositories/agent_profile_repository.dart';

part 'change_language_bloc.freezed.dart';

class ChangeLanguageBloc extends Bloc<ChangeLanguageEvent, ChangeLanguageState>
    with RPCHandler {
  final _profilePageRepository = AgentProfileRepository();
  rpc_locale.Locale? _selectedLocale;
  rpc_locale.Locale? _currentLocale;
  final List<LocaleInformation> supportedLocales;

  ChangeLanguageBloc(this.supportedLocales) : super(const Initial()) {
    on<ChangeLanguageEvent>((event, emit) async {
      switch (event) {
        case GetCurrentLocale(:final context):
          AgencyAppDialog.showSpinnerDialog(context);
          _selectedLocale =
              _currentLocale = await BCNLocale.fetchCurrentRPCLocale();
          if (context.mounted) {
            // Pop the spinner dialog.
            context.rootNavigator.pop();
          }
          emit(const ChangeLanguageState.onData());
        case OnNewLocaleSelected(:final newLocale):
          emit(const ChangeLanguageState.loading());
          _selectedLocale = newLocale;
          emit(const ChangeLanguageState.localeUpdated());
        case ChangeLocale(:final context):
          AgencyAppDialog.showSpinnerDialog(context);
          emit(const ChangeLanguageState.loading());
          await _changeLocale(context, emit, _selectedLocale!);
      }
    });
  }

  Future<void> _changeLocale(
    BuildContext context,
    Emitter<ChangeLanguageState> emit,
    rpc_locale.Locale newLocale,
  ) async {
    // if the selected locale is same as the current locale,
    // don't call the RPC, just navigate to HomeScreen.
    if (newLocale == _currentLocale) {
      navigateToHomeScreen(context);
      return;
    }
    await rpcHandler(
      () async {
        final rpcResult = await _profilePageRepository.changeLocale(newLocale);
        await rpcResult.when(
          response: (response) async {
            if (!BCNLocale.isCurrentAppLocaleSameAsRPCLocale(
              context,
              newLocale,
            )) {
              _selectedLocale = _currentLocale = newLocale;
              await BCNLocale.changeDefaultLocale(newLocale);
            }
            if (context.mounted) {
              navigateToHomeScreen(context);
            }
          },
          error: (error) {
            //Error type is Never.
            throw DeveloperError("Something went wrong: $error");
          },
        );
      },
      onTransientError: (_) {
        if (context.mounted) {
          context.rootNavigator.pop();
        }
        emit(const ChangeLanguageState.transientError());
      },
    );
  }

  /// Currently selected locale.
  /// Radio will be switched to this.
  rpc_locale.Locale get selectedLocale => _selectedLocale!;
}

@freezed
sealed class ChangeLanguageState with _$ChangeLanguageState {
  const factory ChangeLanguageState.initial() = Initial;

  const factory ChangeLanguageState.loading() = Loading;

  const factory ChangeLanguageState.onData() = OnData;

  const factory ChangeLanguageState.transientError() = TransientError;

  /// State called when the user selects a locale.
  /// It does not mean that the locale has been changed.
  const factory ChangeLanguageState.localeUpdated() = LocaleUpdated;
}

@freezed
sealed class ChangeLanguageEvent with _$ChangeLanguageEvent {
  const factory ChangeLanguageEvent.getCurrentLocale(BuildContext context) =
      GetCurrentLocale;

  const factory ChangeLanguageEvent.onNewLocaleSelected(
    BuildContext context,
    rpc_locale.Locale newLocale,
  ) = OnNewLocaleSelected;

  const factory ChangeLanguageEvent.changeLocale(BuildContext context) =
      ChangeLocale;
}
