import 'package:bcn_agency_banking_flutter/src/features/agent/commission/commission_page.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/logger.dart';
import '../../models/custom_bottom_navigation_bar_item.dart';
import '../../resources/ab_assets.dart';
import '../../widgets/custom_bottom_navigation_bar.dart';
import 'home/blocs/home_page_bloc.dart';
import 'home/home_page.dart';
import 'profile/blocs/profile_page_bloc.dart';
import 'profile/profile_page.dart';

/// [HomeScreen] has three pages
/// viz: [HomePage], [CommissionPage] and [ProfilePage].
/// All managed by bottom navigation bar.
class HomeScreen extends StatefulWidget {
  static const id = "/home-screen";

  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  int _currentIndex = 0;

  late final _pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          BlocProvider(
            create:
                (context) =>
                    HomePageBloc()..add(HomePageEvent.getHomeData(context)),
            child: const HomePage(),
          ),
          const CommissionPage(),
          BlocProvider(
            create:
                (context) =>
                    ProfilePageBloc()
                      ..add(ProfilePageEvent.getProfileData(context)),
            child: const ProfilePage(),
          ),
        ],
      ),
      bottomNavigationBar: CustomBottomNavigationBar(
        currentIndex: _currentIndex,
        onItemTapped: (index) {
          l.d("Tapped on BottomNavigationBar item index: $index");
          _pageController.jumpToPage(index);
          _currentIndex = index;
          setState(() {});
        },
        navBarItems: _navBarItems(context),
      ),
    );
  }

  List<CustomBottomNavigationBarItem> _navBarItems(BuildContext context) => [
    CustomBottomNavigationBarItem(
      iconLocation: ABAssets.homeAltIcon,
      label: context.localizations.home,
    ),
    CustomBottomNavigationBarItem(
      iconLocation: ABAssets.moneyBillIcon,
      label: context.localizations.commissionEarned,
    ),
    CustomBottomNavigationBarItem(
      iconLocation: ABAssets.userIcon,
      label: context.localizations.profile,
    ),
  ];
}
