import 'package:agency_banking_rpcs/agency/get_agent_home_data_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/supported_countries.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../core/logger.dart';
import '../../../../errors/developer_error.dart';
import '../../../../helpers/rpc_handler.dart';
import '../../../../models/cash_transactions_arguments.dart';
import '../repositories/agent_home_repository.dart';

part 'home_page_bloc.freezed.dart';

class HomePageBloc extends Bloc<HomePageEvent, HomePageState> with RPCHandler {
  final refreshController = RefreshController();
  final _homePageRepository = AgentHomeRepository();
  GetAgentHomeDataResponse? _getAgentHomeDataResponse;

  // Set of Arguments to be sent for all the Cash Transaction flows.
  CashTransactionArguments? _cashTransactionArguments;

  HomePageBloc() : super(const Initial()) {
    on<HomePageEvent>((event, emit) async {
      switch (event) {
        case GetHomeDataAfterShopStatusUpdate(:final context):
          l.v("Called Event after shop status update");
          emit(const HomePageState.intermediateState());
          await _getHomeData(context, emit);
        case GetHomeData(:final context):
          l.v("Get Home Data Event called on HomePageBloc");
          emit(const HomePageState.loading());
          await _getHomeData(context, emit);
        case PullToRefresh(:final context):
          l.v("Called pullToRefresh event on HomePageBloc");
          emit(const HomePageState.pullToRefreshLoading());
          await _getHomeData(context, emit);
      }
    });
  }

  Future<void> _getHomeData(
    BuildContext context,
    Emitter<HomePageState> emit,
  ) async {
    l.d("Called Get Home Data RPC");
    await rpcHandler(
      () async {
        final rpcResult = await _homePageRepository.getAgentHomeData();
        l.d("GetAgentHomeDataRPC result: $rpcResult");
        await rpcResult.when(
          response: (response) async {
            final locationService = locator<LocationService>();
            _getAgentHomeDataResponse = response;
            locationService.agentMaxAllowedDistance =
                response.transactionAllowLocationRadiusInMeter;
            await CachedSupportedCountries.setSupportedCountries(
              response.supportedCountries,
            );
            _cashTransactionArguments = CashTransactionArguments(
              shopCoordinate: response.shopCoordinate,
              defaultCurrency: response.walletBalance.currency,
            );
            emit(const HomePageState.onData());
            refreshController.refreshCompleted();
            if (context.mounted) {
              // Check whether server locale is NOT matching with current app locale.
              // If so, show a dialog to acknowledge the user.
              await _handleLocaleChange(context, response);
            }
          },
          error: (error) {
            //Error type is Never.
            throw DeveloperError("Something went wrong: $error");
          },
        );
      },
      onServerError: () {
        // If the Bloc is closed, don't go further.
        if (isClosed) return;
        refreshController.refreshFailed();
        emit(const HomePageState.serverError());
      },
      onTransientError: (_) {
        // If the Bloc is closed, don't go further.
        if (isClosed) return;
        refreshController.refreshFailed();
        emit(const HomePageState.transientError());
      },
    );
  }

  Future<void> _handleLocaleChange(
    BuildContext context,
    GetAgentHomeDataResponse response,
  ) async {
    l.d("Handling Locale Change");
    l.d("Server locale: ${response.locale}");
    // Check whether server locale is NOT matching with current app locale.
    // If so, show a dialog to acknowledge the user.
    if (!BCNLocale.isCurrentAppLocaleSameAsRPCLocale(
      context,
      response.locale,
    )) {
      l.d("Current AppLocale is not same as the server locale");
      await AgencyAppDialog.showErrorDialog(
        context: context,
        contentText: context.localizations.localeChangeAcknowledgment,
      );
      await BCNLocale.changeDefaultLocale(response.locale);
    }
  }

  GetAgentHomeDataResponse? get homeData => _getAgentHomeDataResponse;

  CashTransactionArguments get cashTransactionArguments =>
      _cashTransactionArguments!;
}

@freezed
sealed class HomePageState with _$HomePageState {
  const factory HomePageState.initial() = Initial;

  const factory HomePageState.loading() = Loading;

  // After updating the shopStatus we need to have a state change so the screen will rebuild, that's why we are adding this.
  const factory HomePageState.intermediateState() = IntermediateState;

  const factory HomePageState.pullToRefreshLoading() = PullToRefreshLoading;

  const factory HomePageState.transientError() = TransientError;

  const factory HomePageState.serverError() = ServerError;

  const factory HomePageState.onData() = OnData;
}

@freezed
sealed class HomePageEvent with _$HomePageEvent {
  const factory HomePageEvent.getHomeData(BuildContext context) = GetHomeData;

  const factory HomePageEvent.pullToRefresh(BuildContext context) =
      PullToRefresh;

  const factory HomePageEvent.getHomeDataAfterShopStatusUpdate(
    BuildContext context,
  ) = GetHomeDataAfterShopStatusUpdate;
}
