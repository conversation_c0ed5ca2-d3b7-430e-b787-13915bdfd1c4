import 'package:agency_banking_rpcs/agency/get_agent_home_data_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

import '../../../../core/auth/rpc_auth_provider.dart';
import '../../../../core/service_locator.dart';
import '../mock_impls/mock_home_page.dart';

class AgentHomeRepository {
  Future<LeoRPCResult<GetAgentHomeDataResponse, Never>>
  getAgentHomeData() async {
    final request = GetAgentHomeDataRequest();
    final agentHomeDataImpl =
        currentFlavor.isMock
            ? MockHomePageImpl()
            : GetAgentHomeDataRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );

    return await agentHomeDataImpl.execute(request);
  }
}
