import 'package:agency_banking_rpcs/agency/get_agent_home_data_rpc.dart';
import 'package:agency_banking_rpcs/types/shop_status_enum.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/server_error_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../widgets/dialogs.dart';
import '../../../widgets/list_tile_avatar_card.dart';
import '../../../widgets/no_internet_widget.dart';
import '../../../widgets/pull_to_refresh_widget.dart';
import '../../../widgets/shop_status_dropdown/shop_status_dropdown.dart';
import '../cash_in/cash_in_screen.dart';
import '../cash_out/cash_out_screen.dart';
import '../money_transfer/money_transfer_screen.dart';
import 'blocs/home_page_bloc.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late final HomePageBloc _homePageBloc = BlocProvider.of<HomePageBloc>(
    context,
  );

  bool get _isShopClosed =>
      _homePageBloc.homeData!.shopStatus == ShopStatusEnum.SHOP_CLOSED;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomePageBloc, HomePageState>(
      buildWhen: (oldState, newState) {
        // If new state is PullToRefreshLoading or InterMediateState then don't rebuild.
        return (newState is! PullToRefreshLoading &&
            newState is! IntermediateState);
      },
      builder: (context, state) {
        return Scaffold(
          appBar: _buildAppBar(context),
          body: _reactToState(state, context),
        );
      },
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      title: Text(context.localizations.home),
      actions: [
        BlocBuilder<HomePageBloc, HomePageState>(
          builder: (context, state) {
            return ShopStatusDropDown(
              // Passing UniqueKey to force dropdown to rebuild
              // for every state change in Bloc.
              key: UniqueKey(),
              initialShopStatus:
                  _homePageBloc.homeData?.shopStatus ??
                  ShopStatusEnum.SHOP_OPEN,
              shopCoordinate: _homePageBloc.homeData?.shopCoordinate,
              isEnabled: state is OnData,
            );
          },
        ),
        horizontalGapSixteen,
      ],
    );
  }

  Widget _reactToState(HomePageState state, BuildContext context) {
    switch (state) {
      case Initial():
        return const Spinner();
      case Loading():
        return const Spinner();
      case ServerError():
        return ServerErrorWidget(
          onRetryButtonClicked: () => _retryHome(context),
        );
      case TransientError():
        return NoInternetWidget(
          onRetryButtonClicked: () => _retryHome(context),
        );
      case OnData():
        return PullToRefreshWidget(
          controller: _homePageBloc.refreshController,
          onRefresh:
              () => _homePageBloc.add(HomePageEvent.pullToRefresh(context)),
          child: SingleChildScrollView(
            child: Column(
              children: [_showAmountDetails(context), _showFeatures(context)],
            ),
          ),
        );
      default:
        throw DeveloperError("invalid state $state");
    }
  }

  void _retryHome(BuildContext context) {
    _homePageBloc.add(HomePageEvent.getHomeData(context));
  }

  Widget _showFeatures(BuildContext context) {
    return Padding(
      padding: commonScreenPadding,
      child: Column(
        children: [
          ListTileAvatarCard(
            title: context.localizations.cashInRequest,
            iconPath: ABAssets.cashInHomeIcon,
            subtitle: context.localizations.depositToOwnWallet,
            isEnabled: !_isShopClosed,
            onTap: () async {
              if (_isShopClosed) {
                _showShopClosedDialog();
                return;
              }
              await context.rootNavigator.pushNamed(
                CashInScreen.id,
                arguments: _homePageBloc.cashTransactionArguments,
              );
              _refreshHome();
            },
          ),
          verticalGapSixteen,
          ListTileAvatarCard(
            title: context.localizations.cashOutRequest,
            iconPath: ABAssets.cashOutHomeIcon,
            subtitle: context.localizations.withdrawFromOwnWallet,
            isEnabled: !_isShopClosed,
            onTap: () async {
              if (_isShopClosed) {
                _showShopClosedDialog();
                return;
              }
              await context.rootNavigator.pushNamed(
                CashOutScreen.id,
                arguments: _homePageBloc.cashTransactionArguments,
              );
              _refreshHome();
            },
          ),
          verticalGapSixteen,
          ListTileAvatarCard(
            title: context.localizations.moneyTransfer,
            iconPath: ABAssets.exchangeAltCircleIcon,
            subtitle: context.localizations.sendMoney,
            isEnabled: !_isShopClosed,
            onTap: () async {
              if (_isShopClosed) {
                _showShopClosedDialog();
                return;
              }
              await context.rootNavigator.pushNamed(
                MoneyTransferScreen.id,
                arguments: _homePageBloc.cashTransactionArguments,
              );
              _refreshHome();
            },
          ),
        ],
      ),
    );
  }

  // This will show wallet balance
  // and agent's commission.
  Widget _showAmountDetails(BuildContext context) {
    final GetAgentHomeDataResponse homeData = _homePageBloc.homeData!;
    return Container(
      color: context.theme.primaryColor,
      width: double.maxFinite,
      padding: const EdgeInsets.symmetric(
        horizontal: dimenSixteen,
        vertical: dimenTwentyFour,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            context.localizations.commissionEarned,
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.genericWhiteColor,
            ),
          ),
          verticalGapFour,
          Text(
            homeData
                .totalAccruedCommission
                .totalAccruedCommission
                .localisedFormattedAmount,
            style: context.appTextStyles.headingBold.copyWith(
              color: context.appColors.genericWhiteColor,
            ),
          ),
          verticalGapSixteen,
          Text(
            context.localizations.walletBalance,
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.genericWhiteColor,
            ),
          ),
          verticalGapFour,
          Text(
            homeData.walletBalance.localisedFormattedAmount,
            style: context.appTextStyles.headingBold.copyWith(
              color: context.appColors.genericWhiteColor,
            ),
          ),
        ],
      ),
    );
  }

  void _refreshHome() {
    if (!mounted) return;
    _homePageBloc.refreshController.requestRefresh();
  }

  void _showShopClosedDialog() {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.shopClosedDialogMessage,
    );
  }
}
