import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockHomePageImpl extends GetAgentHomeDataRPC {
  @override
  Future<LeoRPCResult<GetAgentHomeDataResponse, Never>> execute(
    GetAgentHomeDataRequest request,
  ) {
    final countries = [
      Country(
        displayName: LocalizedText(en: "India"),
        code: CountryCode(code: "IN"),
        phoneCode: "+91",
      ),
    ];
    final response = GetAgentHomeDataResponse(
      transactionAllowLocationRadiusInMeter: 50,
      agentDisplayName: "Azibo Dulani",
      walletBalance: Amount(
        amount: 251665.rpcAmount,
        currency: Currency(currencyCode: "MWK"),
      ),
      totalAccruedCommission: TotalAccruedCommission(
        totalAccruedCommission: Amount(
          amount: 10000.rpcAmount,
          currency: Currency(currencyCode: "MWK"),
        ),
        nextCommissionTransferredOn: DateTime.now().add(
          const Duration(days: 25),
        ),
      ),
      shopStatus: ShopStatusEnum.SHOP_OPEN,
      shopCoordinate: Coordinate(latitude: 19.4138, longitude: 72.8220),
      userCountry: countries.first,
      supportedCountries: countries,
      locale: Locale(code: 'EN_US'),
    );
    final result = LeoRPCResult<GetAgentHomeDataResponse, Never>.response(
      response,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
