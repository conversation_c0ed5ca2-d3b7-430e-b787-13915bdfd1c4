import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/accept_request_landing_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/active_requests_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_create_request_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/customer_refunds_listing_screen.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/feature_card.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:flutter/material.dart';

import '../../../models/cash_transactions_arguments.dart';

class MoneyTransferScreen extends StatelessWidget {
  static const id = "/money-transfer-screen";
  final CashTransactionArguments cashTransactionArguments;

  const MoneyTransferScreen({Key? key, required this.cashTransactionArguments})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.sendMoney),
      body: _buildBody(context),
    );
  }

  SingleChildScrollView _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          verticalGapEight,
          FeatureCard(
            title: context.localizations.createRequest,
            onTap: () async {
              context.navigator.pushNamed(
                MTCreateRequestScreen.id,
                arguments: cashTransactionArguments,
              );
            },
          ),
          FeatureCard(
            title: context.localizations.acceptRequest,
            onTap: () {
              context.navigator.pushNamed(
                AcceptRequestLandingScreen.id,
                arguments: cashTransactionArguments,
              );
            },
          ),
          FeatureCard(
            title: context.localizations.activeRequests,
            onTap: () {
              context.navigator.pushNamed(
                ActiveRequestsScreen.id,
                arguments: cashTransactionArguments,
              );
            },
          ),
          FeatureCard(
            title: context.localizations.customerRefunds,
            onTap: () {
              context.navigator.pushNamed(
                CustomerRefundsListingScreen.id,
                arguments: cashTransactionArguments,
              );
            },
          ),
        ],
      ),
    );
  }
}
