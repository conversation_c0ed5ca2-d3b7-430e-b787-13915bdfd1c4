import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/mt_recipient_details_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/mt_sender_verification_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../utils/validators.dart';

class MTRecipientDetailsScreen extends StatefulWidget {
  static const id = "/mt-recipient-details-screen";

  const MTRecipientDetailsScreen({Key? key}) : super(key: key);

  @override
  State<MTRecipientDetailsScreen> createState() =>
      _MTRecipientDetailsScreenState();
}

class _MTRecipientDetailsScreenState extends State<MTRecipientDetailsScreen> {
  late final _mTSenderVerificationBloc =
      BlocProvider.of<MTSenderVerificationBloc>(context);
  late final _mTRecipientDetailsBloc = BlocProvider.of<MTRecipientDetailsBloc>(
    context,
  );
  final _recipientDetailsFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldAutoValidateRecipientDetails =
      AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.recipientDetails,
      isCancellable: true,
      ctaWidget: _buildCTA(context),
      child: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Column(
      children: [
        verticalGapTwentyFour,
        Form(
          key: _recipientDetailsFormKey,
          autovalidateMode: _shouldAutoValidateRecipientDetails,
          child: PrimaryTextField(
            maxLength: nationalIdMaxLength,
            inputFormatters: [nationalIdFormatter],
            onChanged: (String value) {
              _mTRecipientDetailsBloc.add(
                MTRecipientDetailsEvent.nationalIDNumberChanged(value),
              );
            },
            labelText: context.localizations.recipientNationalId,
            validator:
                (recipientNationalID) => Validators.emptyValidator(
                  context,
                  context.localizations.recipientNationalId,
                  recipientNationalID,
                ),
          ),
        ),
        verticalGapThirtySix,
      ],
    );
  }

  Widget _buildCTA(BuildContext context) {
    return BlocBuilder<MTRecipientDetailsBloc, MTRecipientDetailsState>(
      bloc: _mTRecipientDetailsBloc,
      builder: (context, state) {
        return PrimaryButton(
          labelText: context.localizations.continueText,
          onPressed: () {
            final bool isFormValidated =
                _recipientDetailsFormKey.currentState?.validate() ?? false;
            if (!isFormValidated) {
              setState(() {
                _shouldAutoValidateRecipientDetails =
                    AutovalidateMode.onUserInteraction;
              });
              return;
            }
            if (_mTSenderVerificationBloc.isSenderBCNUser) {
              _mTRecipientDetailsBloc.add(
                MTRecipientDetailsEvent.confirmMTSenderBCNUserRequest(context),
              );
            } else {
              _mTRecipientDetailsBloc.add(
                MTRecipientDetailsEvent.confirmMTSenderNonBCNUserRequest(
                  context,
                ),
              );
            }
          },
        );
      },
    );
  }
}
