import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_recipient_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../core/logger.dart';

part 'mt_sender_details_bloc.freezed.dart';

class MTSenderDetailsBloc
    extends Bloc<MTSenderDetailsEvent, MTSenderDetailsState>
    with RPC<PERSON>and<PERSON> {
  String? _senderName;
  String? _senderAddress;
  String? _senderNationalIdNumber;
  Photo? _senderPhoto;

  MTSenderDetailsBloc() : super(const Initial()) {
    on<MTSenderDetailsEvent>((event, emit) async {
      switch (event) {
        case NationalIDNumberChanged(:final nationalIdNumber):
          _senderNationalIdNumber = nationalIdNumber;
        case NameChanged(:final name):
          _senderName = name;
        case AddressChanged(:final address):
          _senderAddress = address;
        case PhotoChanged(:final photo):
          _senderPhoto = photo;
        case PhotoDeleted():
          _senderPhoto = null;
        case AddSenderDetails(:final context):
          if (_senderPhoto == null) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.fieldIsRequired(
                context.localizations.sendersPhoto,
              ),
            );
            return;
          }
          l.d('''Added Sender Details: 
            senderAddress: $senderAddress,
            senderPhoto: $senderPhoto,
            senderName: $senderName,
            senderNationalID: $senderNationalIdNumber,
           ''');
          context.navigator.pushNamed(MTRecipientDetailsScreen.id);
      }
    });
  }

  ABUserName get senderName => ABUserName(text: _senderName!);

  String get senderAddress => _senderAddress!;

  NationalId get senderNationalIdNumber =>
      NationalId(id: _senderNationalIdNumber!);

  Photo get senderPhoto => _senderPhoto!;
}

@freezed
sealed class MTSenderDetailsState with _$MTSenderDetailsState {
  const factory MTSenderDetailsState.initial() = Initial;

  const factory MTSenderDetailsState.addedSenderDetails() = AddedSenderDetails;
}

@freezed
sealed class MTSenderDetailsEvent with _$MTSenderDetailsEvent {
  const factory MTSenderDetailsEvent.nameChanged(String name) = NameChanged;

  const factory MTSenderDetailsEvent.addressChanged(String address) =
      AddressChanged;

  const factory MTSenderDetailsEvent.nationalIDNumberChanged(
    String nationalIdNumber,
  ) = NationalIDNumberChanged;

  const factory MTSenderDetailsEvent.photoChanged(Photo photo) = PhotoChanged;

  const factory MTSenderDetailsEvent.photoDeleted() = PhotoDeleted;

  const factory MTSenderDetailsEvent.addSenderDetails(BuildContext context) =
      AddSenderDetails;
}
