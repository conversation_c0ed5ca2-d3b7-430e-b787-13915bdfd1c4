import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/data_source/document_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/create_request_landing_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/mt_sender_details_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/repositories/mt_create_request_repository.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:bcn_agency_banking_flutter/src/models/transaction_success_screen_arguments.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_dialog.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_success_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../../core/logger.dart';

part 'mt_recipient_details_bloc.freezed.dart';

class MTRecipientDetailsBloc
    extends Bloc<MTRecipientDetailsEvent, MTRecipientDetailsState>
    with RPCHandler {
  String? _recipientNationalIdNumber;
  final MTCreateRequestRepository _mTCreateRequestRepository =
      MTCreateRequestRepository();
  final LocationService _locationService = locator<LocationService>();

  MTRecipientDetailsBloc() : super(const Initial()) {
    on<MTRecipientDetailsEvent>((event, emit) async {
      switch (event) {
        case NationalIDNumberChanged(:final nationalIdNumber):
          _recipientNationalIdNumber = nationalIdNumber;
        case ConfirmMTSenderBCNUserRequest(:final context):
          await _confirmMTSenderBCNUserRequest(context, emit);
        case ConfirmMTSenderNonBCNUserRequest(:final context):
          await _confirmMTSenderNonBCNUserRequest(context, emit);
      }
    });
  }

  Future<LeoUUID?> _getSenderPhotoUploadUUID(
    Emitter<MTRecipientDetailsState> emit,
    Photo senderPhoto,
  ) async {
    late final LeoUUID? photoId;
    await rpcHandler(
      () async {
        photoId =
            currentFlavor.isMock
                ? await Future.delayed(
                  1.seconds,
                  () => LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
                )
                : await DocumentService().uploadPhoto(senderPhoto);
      },
      onTransientError: (_) {
        emit(const MTRecipientDetailsState.transientError());
      },
    );
    return photoId;
  }

  Future<bool?> _getProceedActionFromDialog(
    BuildContext context,
    LeoUUID recordId,
    Amount sendAmount,
    Amount transactionFee,
    Amount receivedAmount,
  ) async {
    return await TransactionDialog.show(
      context: context,
      transactionAmountLabel: context.localizations.sendAmount,
      transactionAmount: sendAmount,
      transactionFee: transactionFee,
      additionalDetailLabel: context.localizations.recipientNationalId,
      additionalDetailValue: _recipientNationalIdNumber,
      receivingAmountLabel: context.localizations.recipientReceives,
      receivingAmount: receivedAmount,
    );
  }

  Future<void> _confirmMTSenderBCNUserRequest(
    BuildContext context,
    Emitter<MTRecipientDetailsState> emit,
  ) async {
    l.v("Confirm MT Sender BCN User Request");
    final createRequestLandingBloc = BlocProvider.of<CreateRequestLandingBloc>(
      context,
    );
    final LeoUUID recordId =
        createRequestLandingBloc.createMoneyTransferResponse.recordId;
    final Amount sendAmount = createRequestLandingBloc.transactionAmount;
    final Amount transactionFee = createRequestLandingBloc.transactionFee;
    final Amount receivedAmount = createRequestLandingBloc.receivingAmount;
    final bool? shouldProceed = await _getProceedActionFromDialog(
      context,
      recordId,
      sendAmount,
      transactionFee,
      receivedAmount,
    );
    if (shouldProceed == true && context.mounted) {
      if (context.mounted) {
        if (!_isAgentLocationValid(context, emit)) return;
        emit(const MTRecipientDetailsState.loading());
        AgencyAppDialog.showSpinnerDialog(context);
        await rpcHandler(
          () async {
            final result = await _mTCreateRequestRepository
                .confirmMTSenderBCNUserRequest(
                  recordId,
                  NationalId(id: _recipientNationalIdNumber!),
                );
            if (context.mounted) {
              // Pop the loader dialog.
              context.rootNavigator.pop();
              _handleConfirmMTSenderBCNUserRequest(
                context,
                emit,
                result,
                recordId,
                sendAmount,
                transactionFee,
                receivedAmount,
              );
            }
          },
          onTransientError: (_) {
            // Pop the loader dialog.
            context.rootNavigator.pop();
            emit(const MTRecipientDetailsState.transientError());
          },
        );
      }
    }
  }

  void _handleConfirmMTSenderBCNUserRequest(
    BuildContext context,
    Emitter<MTRecipientDetailsState> emit,
    LeoRPCResult<
      ConfirmMoneyTransferSenderBCNUserRequestResponse,
      ConfirmMoneyTransferSenderBCNUserRequestError
    >
    result,
    LeoUUID recordId,
    Amount sendAmount,
    Amount transactionFee,
    Amount receivedAmount,
  ) {
    result.when(
      response: (response) {
        emit(const MTRecipientDetailsState.confirmedMTSenderBCNUserRequest());
        context.rootNavigator.pushNamed(
          TransactionSuccessScreen.id,
          arguments: TransactionSuccessScreenArguments(
            transactionStatusDetail: response.transactionDetail,
            succeededAt: response.succeededAt,
            recordId: recordId.uuid,
            amount: sendAmount,
          ),
        );
      },
      error: (error) {
        emit(const MTRecipientDetailsState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.createTitle,
            );
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          recipientPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.periodicRequestTransactionLimitExceeded();
          },
          recipientMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.monetaryTransactionLimitExceededErrorMessage();
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          insufficientBalance: (e) {
            ProminentErrorHandler.inSufficientBalance(
              onOkay: navigateToHomeScreen,
              buttonText: context.localizations.okay,
              transactionFee: e.transactionFee,
            );
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          senderNotBcnUser: (e) => throw DeveloperError(e.toString()),
          couldNotSendRequestId: (e) => throw DeveloperError(e.toString()),
          senderAndRecipientAreSame: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context
                      .localizations
                      .senderAndRecipientNationalIdCannotBeSame,
              onOkay: navigateToHomeScreen,
            );
          },
        );
      },
    );
  }

  Future<void> _confirmMTSenderNonBCNUserRequest(
    BuildContext context,
    Emitter<MTRecipientDetailsState> emit,
  ) async {
    final createRequestLandingBloc = BlocProvider.of<CreateRequestLandingBloc>(
      context,
    );
    late final mTSenderDetailsBloc = BlocProvider.of<MTSenderDetailsBloc>(
      context,
    );
    final LeoUUID recordId =
        createRequestLandingBloc.createMoneyTransferResponse.recordId;
    final Amount sendAmount = createRequestLandingBloc.transactionAmount;
    final Amount transactionFee = createRequestLandingBloc.transactionFee;
    final Amount receivedAmount = createRequestLandingBloc.receivingAmount;
    final ABUserName senderName = mTSenderDetailsBloc.senderName;
    final String senderAddress = mTSenderDetailsBloc.senderAddress;
    final NationalId senderNationalId =
        mTSenderDetailsBloc.senderNationalIdNumber;
    final Photo senderPhoto = mTSenderDetailsBloc.senderPhoto;
    emit(const MTRecipientDetailsState.loading());
    AgencyAppDialog.showSpinnerDialog(context);
    final LeoUUID? photoId = await _getSenderPhotoUploadUUID(emit, senderPhoto);
    l.d('''Confirm MT Sender NON BCN User
    recordId: $recordId,
    recipientNationalIdNumber:   $_recipientNationalIdNumber!,
    senderNationalId: $senderNationalId,
    senderPhotoId: $photoId!,
    senderName: $senderName,
    senderAddress: $senderAddress,''');
    if (context.mounted) {
      // Pop the loader dialog.
      context.rootNavigator.pop();
      bool? shouldProceed = await _getProceedActionFromDialog(
        context,
        recordId,
        sendAmount,
        transactionFee,
        receivedAmount,
      );
      l.d("Should Proceed transaction: $shouldProceed");
      if (shouldProceed == true && context.mounted) {
        if (context.mounted) {
          if (!_isAgentLocationValid(context, emit)) return;
          emit(const MTRecipientDetailsState.loading());
          AgencyAppDialog.showSpinnerDialog(context);
          await rpcHandler(
            () async {
              final result = await _mTCreateRequestRepository
                  .confirmMTSenderNonBCNUserRequest(
                    recordId,
                    NationalId(id: _recipientNationalIdNumber!),
                    senderNationalId,
                    photoId!,
                    senderName,
                    senderAddress,
                  );
              l.d("Confirm Money transfer sender NON BCNUser result: $result");
              if (context.mounted) {
                // Pop the loader dialog.
                context.rootNavigator.pop();
                _handleConfirmMTSenderNonBCNUserRequest(
                  context,
                  emit,
                  result,
                  recordId,
                  sendAmount,
                  transactionFee,
                  receivedAmount,
                );
              }
            },
            onTransientError: (_) {
              // Pop the loader dialog.
              context.rootNavigator.pop();
              emit(const MTRecipientDetailsState.transientError());
            },
          );
        }
      }
    }
  }

  void _handleConfirmMTSenderNonBCNUserRequest(
    BuildContext context,
    Emitter<MTRecipientDetailsState> emit,
    LeoRPCResult<
      ConfirmMoneyTransferSenderNonBCNUserRequestResponse,
      ConfirmMoneyTransferSenderNonBCNUserRequestError
    >
    result,
    LeoUUID recordId,
    Amount sendAmount,
    Amount transactionFee,
    Amount receivedAmount,
  ) {
    l.v("Handling ConfirmMTSenderNonBCNUserRequest result");
    result.when(
      response: (response) {
        l.d("ConfirmMTSenderNonBCNUserRequest response: $response");
        emit(const MTRecipientDetailsState.confirmedMTSenderBCNUserRequest());
        context.rootNavigator.pushNamed(
          TransactionSuccessScreen.id,
          arguments: TransactionSuccessScreenArguments(
            transactionStatusDetail: response.transactionDetail,
            succeededAt: response.succeededAt,
            recordId: recordId.uuid,
            amount: sendAmount,
          ),
        );
      },
      error: (error) {
        l.d("ConfirmMTSenderNonBCNUserRequest error: $error");
        emit(const MTRecipientDetailsState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.createTitle,
            );
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          recipientPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.periodicRequestTransactionLimitExceeded();
          },
          recipientMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.monetaryTransactionLimitExceededErrorMessage();
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          insufficientBalance: (e) {
            ProminentErrorHandler.inSufficientBalance(
              onOkay: navigateToHomeScreen,
              buttonText: context.localizations.okay,
              transactionFee: e.transactionFee,
            );
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          couldNotSendRequestId: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText:
                  context.localizations.couldNotSendRequestIdErrorMessage,
            );
          },
          invalidSenderImageId: (e) => throw DeveloperError(e.toString()),
          senderIsBcnUser: (e) => throw DeveloperError(e.toString()),
          senderAndRecipientAreSame: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context
                      .localizations
                      .senderAndRecipientNationalIdCannotBeSame,
              onOkay: navigateToHomeScreen,
            );
          },
        );
      },
    );
  }

  /// Checks whether agent location is near to their shop location.
  /// if not returns false and shows appropriate dialog.
  bool _isAgentLocationValid(
    BuildContext context,
    Emitter<MTRecipientDetailsState> emit,
  ) {
    final createRequestLandingBloc = BlocProvider.of<CreateRequestLandingBloc>(
      context,
    );
    final LocationDetails locationDetails =
        createRequestLandingBloc.shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const MTRecipientDetailsState.prominentError());
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class MTRecipientDetailsState with _$MTRecipientDetailsState {
  const factory MTRecipientDetailsState.initial() = Initial;

  const factory MTRecipientDetailsState.loading() = OTPLoading;

  const factory MTRecipientDetailsState.prominentError() = ProminentError;

  const factory MTRecipientDetailsState.transientError() = TransientError;

  const factory MTRecipientDetailsState.confirmedMTSenderBCNUserRequest() =
      ConfirmedMTSenderBCNUserRequest;

  const factory MTRecipientDetailsState.confirmedMTSenderNonBCNUserRequest() =
      ConfirmedMTSenderNonBCNUserRequest;
}

@freezed
sealed class MTRecipientDetailsEvent with _$MTRecipientDetailsEvent {
  const factory MTRecipientDetailsEvent.nationalIDNumberChanged(
    String nationalIdNumber,
  ) = NationalIDNumberChanged;

  const factory MTRecipientDetailsEvent.confirmMTSenderBCNUserRequest(
    BuildContext context,
  ) = ConfirmMTSenderBCNUserRequest;

  const factory MTRecipientDetailsEvent.confirmMTSenderNonBCNUserRequest(
    BuildContext context,
  ) = ConfirmMTSenderNonBCNUserRequest;
}
