import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/create_request_landing_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_recipient_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_sender_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/repositories/mt_create_request_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../../core/logger.dart';

part 'mt_sender_verification_bloc.freezed.dart';

class MTSenderVerificationBloc
    extends Bloc<MTSenderVerificationEvent, MTSenderVerificationState>
    with RPCHandler {
  String? _otp;
  late final bool? _isSenderBCNUser;
  final MTCreateRequestRepository _mTCreateRequestRepository =
      MTCreateRequestRepository();
  final LocationService _locationService = locator<LocationService>();

  MTSenderVerificationBloc() : super(const Initial()) {
    on<MTSenderVerificationEvent>((event, emit) async {
      switch (event) {
        case AddOTP(:final otp):
          if (state is OTPLoading) return;
          _otp = otp;
        case ResendOTP(:final context, :final recordId):
          if (state is OTPLoading) return;
          await _resendOTP(context, emit, recordId);
        case ConfirmMTSenderOTP(:final context, :final recordId, :final amount):
          await _confirmMTSenderOTP(context, emit, recordId, amount);
      }
    });
  }

  bool get isSenderBCNUser => _isSenderBCNUser!;

  Future<void> _confirmMTSenderOTP(
    BuildContext context,
    Emitter<MTSenderVerificationState> emit,
    LeoUUID recordId,
    Amount amount,
  ) async {
    l.d('''Confirm Money transfer Sender OTP: 
    recordId: $recordId,
    amount: $amount''');
    if (!_isAgentLocationValid(context, emit)) return;
    emit(const MTSenderVerificationState.loading());
    if (!context.mounted) return;
    AgencyAppDialog.showSpinnerDialog(context);
    await rpcHandler(
      () async {
        final result = await _mTCreateRequestRepository.confirmSenderOTP(
          recordId,
          Otp(otp: _otp!),
        );
        l.d("Confirm Sender OTP Result: $result");
        if (context.mounted) {
          // Pop the loader dialog.
          context.rootNavigator.pop();
          _handleConfirmMTSenderOTPResult(
            context,
            emit,
            result,
            recordId,
            amount,
          );
        }
      },
      onTransientError: (_) {
        // Pop the loader dialog.
        context.rootNavigator.pop();
        emit(const MTSenderVerificationState.transientError());
      },
    );
  }

  void _handleConfirmMTSenderOTPResult(
    BuildContext context,
    Emitter<MTSenderVerificationState> emit,
    LeoRPCResult<
      ConfirmMoneyTransferSenderOTPResponse,
      ConfirmMoneyTransferSenderOTPError
    >
    result,
    LeoUUID recordId,
    Amount amount,
  ) {
    result.when(
      response: (response) {
        l.d("Confirm Sender OTP response: $response");
        _isSenderBCNUser = response.isSenderBCNUser;
        emit(const MTSenderVerificationState.otpVerified());
        if (response.isSenderBCNUser) {
          context.navigator.pushNamed(MTRecipientDetailsScreen.id);
        } else {
          context.navigator.pushNamed(MTSenderDetailsScreen.id);
        }
      },
      error: (error) {
        l.d("Confirm Sender OTP error: $error");
        emit(const MTSenderVerificationState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          otpExpired: (_) {
            ProminentErrorHandler.otpExpired();
          },
          incorrectOtp: (_) {
            ProminentErrorHandler.incorrectOTP(
              numberOfValidationsAttemptsLeft: 2,
              featureName: context.localizations.createTitle,
            );
          },
          tooManyOtpAttempts: (_) {
            ProminentErrorHandler.tooManyOTPRequests();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.createTitle,
            );
          },
        );
      },
    );
  }

  Future<void> _resendOTP(
    BuildContext context,
    Emitter<MTSenderVerificationState> emit,
    LeoUUID recordId,
  ) async {
    l.v("Resending OTP");
    if (!_isAgentLocationValid(context, emit)) return;
    await rpcHandler(
      () async {
        final result = await _mTCreateRequestRepository.resendOTP(recordId);
        l.d("Resend Money Transfer Sender OTP Result: $result");
        if (context.mounted) {
          _handleResendOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        l.v("Emitting transient error");
        emit(const MTSenderVerificationState.transientError());
      },
    );
  }

  void _handleResendOTPResult(
    BuildContext context,
    Emitter<MTSenderVerificationState> emit,
    LeoRPCResult<
      ResendMoneyTransferSenderOTPResponse,
      ResendMoneyTransferSenderOTPError
    >
    result,
  ) {
    l.v("Handling ResendOTP Result");
    result.when(
      response: (response) {
        l.d("Resend OTP response: $response");
        emit(
          MTSenderVerificationState.otpResentSuccessfully(response.otpDetails),
        );
      },
      error: (error) {
        l.d("Resend OTP error: $error");
        emit(const MTSenderVerificationState.prominentError());
        error.when(
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          waitForResend: (_) => ProminentErrorHandler.waitForOTPResend(),
          tooManyResendRequests: (_) {
            ProminentErrorHandler.tooManyResendRequests();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.createTitle,
            );
          },
        );
      },
    );
  }

  /// Checks whether agent location is near to their shop location.
  /// if not returns false and shows appropriate dialog.
  bool _isAgentLocationValid(
    BuildContext context,
    Emitter<MTSenderVerificationState> emit,
  ) {
    final createRequestLandingBloc = BlocProvider.of<CreateRequestLandingBloc>(
      context,
    );
    final LocationDetails locationDetails =
        createRequestLandingBloc.shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const MTSenderVerificationState.prominentError());
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class MTSenderVerificationState with _$MTSenderVerificationState {
  const factory MTSenderVerificationState.initial() = Initial;

  const factory MTSenderVerificationState.loading() = OTPLoading;

  const factory MTSenderVerificationState.prominentError() = ProminentError;

  const factory MTSenderVerificationState.transientError() = TransientError;

  const factory MTSenderVerificationState.otpResentSuccessfully(
    OTPResendDetails resendDetails,
  ) = OTPResentSuccessfully;

  const factory MTSenderVerificationState.invalidOTP() = InvalidOTP;

  const factory MTSenderVerificationState.otpVerified() = OTPVerified;
}

@freezed
sealed class MTSenderVerificationEvent with _$MTSenderVerificationEvent {
  const factory MTSenderVerificationEvent.addOtp(String otp) = AddOTP;

  const factory MTSenderVerificationEvent.resendOTP(
    BuildContext context,
    LeoUUID recordId,
  ) = ResendOTP;

  const factory MTSenderVerificationEvent.confirmMTSenderOTP(
    BuildContext context,
    LeoUUID recordId,
    Amount amount,
  ) = ConfirmMTSenderOTP;
}
