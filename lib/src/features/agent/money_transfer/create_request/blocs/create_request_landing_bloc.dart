import 'dart:async';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_sender_verification_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/repositories/mt_create_request_repository.dart';
import 'package:bcn_agency_banking_flutter/src/features/session_pin/enter_session_pin/enter_session_pin_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../../core/logger.dart';

part 'create_request_landing_bloc.freezed.dart';

class CreateRequestLandingBloc
    extends Bloc<CreateRequestLandingEvent, CreateRequestLandingState>
    with RPCHandler {
  String? _senderPhoneNumber;
  String? _receiverPhoneNumber;
  Amount? _transactionAmount;
  Amount? _transactionFee;
  Amount? _receivingAmount;
  final Coordinate shopCoordinate;
  final Currency defaultCurrency;
  late CreateMoneyTransferRequestResponse? _createMoneyTransferRequestResponse;
  final MTCreateRequestRepository _mtCreateRequestRepository =
      MTCreateRequestRepository();
  final LocationService _locationService = locator<LocationService>();
  late final OTPValidityDetails? _otpValidityDetails;

  CreateRequestLandingBloc({
    required this.shopCoordinate,
    required this.defaultCurrency,
  }) : super(const CreateRequestLandingState.initial()) {
    on<CreateRequestLandingEvent>((event, emit) async {
      switch (event) {
        case AddSenderPhoneNumber(:final phoneNumber):
          _senderPhoneNumber = phoneNumber;
        case AddReceiverPhoneNumber(:final phoneNumber):
          _receiverPhoneNumber = phoneNumber;
        case AddAmount(:final amount):
          _transactionAmount = amount;
        case CreateMTRequest(:final context):
          await _createMoneyTransferRequest(emit, context);
      }
    });
  }

  Amount get transactionAmount => _transactionAmount!;

  Amount get transactionFee => _transactionFee!;

  Amount get receivingAmount => _receivingAmount!;

  CreateMoneyTransferRequestResponse get createMoneyTransferResponse =>
      _createMoneyTransferRequestResponse!;

  OTPValidityDetails get otpValidityDetails => _otpValidityDetails!;

  Future<void> _createMoneyTransferRequest(
    Emitter<CreateRequestLandingState> emit,
    BuildContext context,
  ) async {
    l.d('''Creating MoneyTransferRequest: 
    senderPhoneNumber: $_senderPhoneNumber,
    receiverPhoneNumber: $_receiverPhoneNumber,
    transactionAmount: $_transactionAmount
    ''');
    emit(const CreateRequestLandingState.loading());
    if (!_isAgentLocationValid(context, emit)) return;
    final isAgentAuthenticated =
        await context.rootNavigator.pushNamed(EnterSessionPinScreen.id)
            as bool?;
    l.v("isAgentAunthenticated: $isAgentAuthenticated");
    if (!(isAgentAuthenticated ?? false)) return;
    if (context.mounted) {
      _showLoaderDialog(context);
      await rpcHandler(
        () async {
          final rpcResult = await _mtCreateRequestRepository
              .createMoneyTransferRequest(
                LeoPhoneNumber(_senderPhoneNumber!),
                LeoPhoneNumber(_receiverPhoneNumber!),
                _transactionAmount!,
              );
          l.d("CreateMoneyTransfer result: $rpcResult");
          if (context.mounted) {
            _handleCreateMoneyTransferRequestRPCResult(
              context,
              rpcResult,
              emit,
            );
          }
        },
        onTransientError: (_) {
          context.rootNavigator.pop();
          emit(const CreateRequestLandingState.transientError());
        },
      );
    }
  }

  Future<void> _handleCreateMoneyTransferRequestRPCResult(
    BuildContext context,
    LeoRPCResult<
      CreateMoneyTransferRequestResponse,
      CreateMoneyTransferRequestError
    >
    rpcResult,
    Emitter<CreateRequestLandingState> emit,
  ) async {
    // Pop the loader dialog.
    context.rootNavigator.pop();
    await rpcResult.when(
      response: (response) async {
        l.d("Create Money transfer RPC response: $response");
        if (response.receivingAmount != null) {
          l.e(
            "Response contains Exchange money transfer components: ${response.receivingAmount}",
          );
          ProminentErrorHandler.exchangeRateNotSupported();
        }
        emit(const CreateRequestLandingState.createdMTRequest());
        _createMoneyTransferRequestResponse = response;
        _transactionFee = response.claimedTransactionFee;
        _receivingAmount =
            response.receivingAmount ??
            AmountFormatter.getDifferenceBetweenAmounts(
              _transactionAmount!,
              response.claimedTransactionFee,
            );
        final bool? shouldProceed = await TransactionDialog.show(
          context: context,
          transactionAmountLabel: context.localizations.sendAmount,
          transactionAmount: _transactionAmount!,
          transactionFee: response.claimedTransactionFee,
          receivingAmountLabel: context.localizations.recipientReceives,
          receivingAmount: _receivingAmount!,
        );
        l.d("Should Proceed for money transfer: $shouldProceed");
        if (shouldProceed == true && context.mounted) {
          AgencyAppDialog.showSpinnerDialog(context);
          await _requestOTP(context, emit, response.recordId);
        }
      },
      error: (error) {
        emit(const CreateRequestLandingState.prominentError());
        error.when(
          currencyMismatchForAgentAccountAndAmount:
              (e) => throw DeveloperError(e.toString()),
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          amountTooLess: (error) {
            ProminentErrorHandler.amountTooLess(error.minimumAllowedAmount);
          },
          amountTooLarge: (error) {
            ProminentErrorHandler.amountTooHigh(error.maximumAllowedAmount);
          },
          insufficientBalance: (e) {
            ProminentErrorHandler.inSufficientBalance(
              transactionFee: e.transactionFee,
            );
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          recipientPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.periodicRequestTransactionLimitExceeded();
          },
          recipientMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.monetaryTransactionLimitExceededErrorMessage();
          },
          senderAndRecipientAreSame: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.senderAndRecipientSame,
            );
          },
          senderOrRecipientCannotBeAgent: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.senderOrRecipientCannotBeAgent,
            );
          },
        );
      },
    );
  }

  Future<void> _requestOTP(
    BuildContext context,
    Emitter<CreateRequestLandingState> emit,
    LeoUUID recordId,
  ) async {
    l.d("Requesting Sender's OTP for money transfer. Record ID: $recordId");
    if (!_isAgentLocationValid(context, emit)) return;
    await rpcHandler(
      () async {
        final result = await _mtCreateRequestRepository.requestOTP(recordId);
        l.d("Create Money transfer sender OTP result: $result");
        if (context.mounted) {
          _handlerRequestOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        emit(const CreateRequestLandingState.transientError());
      },
    );
  }

  void _handlerRequestOTPResult(
    BuildContext context,
    Emitter<CreateRequestLandingState> emit,
    LeoRPCResult<
      RequestMoneyTransferSenderOTPResponse,
      RequestMoneyTransferSenderOTPError
    >
    result,
  ) {
    // Pop the loader dialog.
    context.rootNavigator.pop();
    result.when(
      response: (response) {
        l.d("Response for RequestMoneyTransferSenderOTP: $response");
        _otpValidityDetails = response.otpDetails;
        context.navigator.pushNamed(MTSenderVerificationScreen.id);
      },
      error: (error) {
        l.d("Error for RequestMoneyTransferSenderOTP: $error");
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          tooManyOtpRequests: (_) {
            ProminentErrorHandler.tooManyOTPRequests();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.createTitle,
            );
          },
        );
      },
    );
  }

  void _showLoaderDialog(BuildContext context) {
    AgencyAppDialog.showSpinnerDialog(context);
  }

  /// Checks whether agent location is near to their shop location.
  /// if not returns false and shows appropriate dialog.
  bool _isAgentLocationValid(
    BuildContext context,
    Emitter<CreateRequestLandingState> emit,
  ) {
    final LocationDetails locationDetails = shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class CreateRequestLandingState with _$CreateRequestLandingState {
  const factory CreateRequestLandingState.initial() = Initial;

  const factory CreateRequestLandingState.loading() = Loading;

  const factory CreateRequestLandingState.prominentError() = ProminentError;

  const factory CreateRequestLandingState.transientError() = TransientError;

  const factory CreateRequestLandingState.createdMTRequest() = CreatedMTRequest;
}

@freezed
sealed class CreateRequestLandingEvent with _$CreateRequestLandingEvent {
  const factory CreateRequestLandingEvent.addSenderPhoneNumber(
    String? phoneNumber,
  ) = AddSenderPhoneNumber;

  const factory CreateRequestLandingEvent.addReceiverPhoneNumber(
    String? phoneNumber,
  ) = AddReceiverPhoneNumber;

  const factory CreateRequestLandingEvent.addAmount(Amount? amount) = AddAmount;

  const factory CreateRequestLandingEvent.createMTRequest(
    BuildContext context,
  ) = CreateMTRequest;
}
