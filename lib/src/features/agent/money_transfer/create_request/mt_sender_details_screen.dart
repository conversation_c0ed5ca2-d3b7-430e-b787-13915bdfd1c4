import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/validators.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/upload_photo_previewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'blocs/mt_sender_details_bloc.dart';

class MTSenderDetailsScreen extends StatefulWidget {
  static const id = "/mt-sender-details-screen";

  const MTSenderDetailsScreen({Key? key}) : super(key: key);

  @override
  State<MTSenderDetailsScreen> createState() => _MTSenderDetailsScreenState();
}

class _MTSenderDetailsScreenState extends State<MTSenderDetailsScreen> {
  late final _mTSenderDetailsBloc = BlocProvider.of<MTSenderDetailsBloc>(
    context,
  );
  final _senderDetailsFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldAutoValidateSenderDetails = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.senderDetails,
      isCancellable: true,
      ctaWidget: _buildCTA(),
      padding: const EdgeInsets.only(top: dimenEight),
      child: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Column(
      children: [
        _buildBasicDetailsSection(),
        _getPhotoPreviewer(),
        verticalGapThirtySix,
      ],
    );
  }

  Widget _buildBasicDetailsSection() {
    return Column(
      children: [
        TitleWidget(title: context.localizations.basicDetails),
        Padding(
          padding: commonScreenPadding,
          child: Form(
            key: _senderDetailsFormKey,
            autovalidateMode: _shouldAutoValidateSenderDetails,
            child: Column(
              children: [
                _getNameTextField(),
                verticalGapSixteen,
                _getAddressTextField(),
                verticalGapSixteen,
                _getNationalIDTextField(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  PrimaryTextField _getNameTextField() {
    return PrimaryTextField(
      onChanged: (String value) {
        _mTSenderDetailsBloc.add(MTSenderDetailsEvent.nameChanged(value));
      },
      labelText: context.localizations.sendersName,
      maxLength: maxNameLength,
      validator:
          (name) => Validators.emptyValidator(
            context,
            context.localizations.sendersName,
            name,
          ),
    );
  }

  Widget _getAddressTextField() {
    return PrimaryTextField(
      labelText: context.localizations.sendersAddress,
      maxLength: maxAddressLength,
      minLines: minLines,
      maxLines: maxLines,
      onChanged: (value) {
        _mTSenderDetailsBloc.add(MTSenderDetailsEvent.addressChanged(value));
      },
      validator:
          (address) => Validators.emptyValidator(
            context,
            context.localizations.sendersAddress,
            address,
          ),
    );
  }

  PrimaryTextField _getNationalIDTextField() {
    return PrimaryTextField(
      inputFormatters: [nationalIdFormatter],
      maxLength: nationalIdMaxLength,
      onChanged: (String value) {
        _mTSenderDetailsBloc.add(
          MTSenderDetailsEvent.nationalIDNumberChanged(value),
        );
      },
      labelText: context.localizations.sendersNationalIDNumber,
      validator:
          (nationalId) => Validators.emptyValidator(
            context,
            context.localizations.sendersAddress,
            nationalId,
          ),
    );
  }

  UploadPhotoPreviewer _getPhotoPreviewer() {
    return UploadPhotoPreviewer(
      title: context.localizations.sendersPhotoTitle,
      onPhotoSelected: (Photo photo) {
        _mTSenderDetailsBloc.add(MTSenderDetailsEvent.photoChanged(photo));
      },
      onPhotoDeleted: () {
        _mTSenderDetailsBloc.add(const MTSenderDetailsEvent.photoDeleted());
      },
      placeholderText: context.localizations.sendersPhoto,
    );
  }

  Widget _buildCTA() {
    return BlocBuilder<MTSenderDetailsBloc, MTSenderDetailsState>(
      bloc: _mTSenderDetailsBloc,
      builder: (context, state) {
        return Padding(
          padding: commonScreenPadding,
          child: PrimaryButton(
            labelText: context.localizations.continueText,
            onPressed: () {
              final bool isFormValidated =
                  _senderDetailsFormKey.currentState?.validate() ?? false;
              if (!isFormValidated) {
                setState(() {
                  _shouldAutoValidateSenderDetails =
                      AutovalidateMode.onUserInteraction;
                });
                return;
              }
              _mTSenderDetailsBloc.add(
                MTSenderDetailsEvent.addSenderDetails(context),
              );
            },
          ),
        );
      },
    );
  }
}
