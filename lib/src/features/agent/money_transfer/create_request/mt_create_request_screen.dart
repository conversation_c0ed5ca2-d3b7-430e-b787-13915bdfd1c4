import 'package:bcn_agency_banking_flutter/src/core/route_generator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_create_request_landing_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_recipient_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_sender_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_sender_verification_screen.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

import '../../../../helpers/helpers.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/nested_navigator_observer.dart';

/// A Nested Navigator for Money Transfer Create Request screen.
/// All screens will come under this Navigator and
/// routes for the same will be added in [Navigator.onGenerateRoute]
/// See this for more info: https://docs.flutter.dev/cookbook/effects/nested-nav
class MTCreateRequestScreen extends StatelessWidget {
  static const id = "/mt-create-request-screen";

  const MTCreateRequestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final nestedNavigationObserver = NestedNavigatorObserver();
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }
        final NavigatorState navigator = Navigator.of(context);
        if (isCurrentRouteInitialRoute(
          nestedNavigationObserver,
          MTCreateRequestLandingScreen.id,
        )) {
          navigator.pop();
          return;
        }
        final bool? shouldPop = await AgencyAppDialog.showCancelConfirmation(
          context,
          context.localizations.transaction,
        );
        if (shouldPop ?? false) {
          navigator.pop();
          return;
        }
      },
      child: Navigator(
        onGenerateRoute: (settings) {
          switch (settings.name) {
            case MTCreateRequestLandingScreen.id:
              return _getCreateRequestLandingScreenRoute();
            case MTSenderVerificationScreen.id:
              return Routes.getMaterialRoute(
                MTSenderVerificationScreen.id,
                const MTSenderVerificationScreen(),
              );
            case MTRecipientDetailsScreen.id:
              return Routes.getMaterialRoute(
                MTRecipientDetailsScreen.id,
                const MTRecipientDetailsScreen(),
              );
            case MTSenderDetailsScreen.id:
              return Routes.getMaterialRoute(
                MTSenderDetailsScreen.id,
                const MTSenderDetailsScreen(),
              );
            default:
              return _getCreateRequestLandingScreenRoute();
          }
        },
        initialRoute: MTCreateRequestLandingScreen.id,
        observers: [nestedNavigationObserver],
      ),
    );
  }

  MaterialPageRoute<dynamic> _getCreateRequestLandingScreenRoute() {
    return Routes.getMaterialRoute(
      MTCreateRequestLandingScreen.id,
      const MTCreateRequestLandingScreen(),
    );
  }
}
