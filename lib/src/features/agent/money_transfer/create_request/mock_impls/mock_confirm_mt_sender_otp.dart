import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockConfirmMoneyTransferSenderOTP
    extends ConfirmMoneyTransferSenderOTPRPC {
  @override
  Future<
    LeoRPCResult<
      ConfirmMoneyTransferSenderOTPResponse,
      ConfirmMoneyTransferSenderOTPError
    >
  >
  execute(ConfirmMoneyTransferSenderOTPRequest request) {
    final response = ConfirmMoneyTransferSenderOTPResponse(
      isSenderBCNUser: false,
    );
    final error =
        ConfirmMoneyTransferSenderOTPError.ConfirmMoneyTransferSenderOTPErrorIncorrectOtpError(
          errorCode: "123",
          numberOfValidationAttemptsLeft: 2,
        );
    final result = getLeoRPCResult(
      shouldThrowError: !checkWhetherOTPValid(request.otp),
      response: response,
      error: error,
    );
    return Future.delayed(1.seconds, () => result);
  }
}
