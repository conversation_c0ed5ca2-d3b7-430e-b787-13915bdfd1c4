import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

class MockCreateMTRequest extends CreateMoneyTransferRequestRPC {
  @override
  Future<
    LeoRPCResult<
      CreateMoneyTransferRequestResponse,
      CreateMoneyTransferRequestError
    >
  >
  execute(CreateMoneyTransferRequestRequest request) async {
    final response = CreateMoneyTransferRequestResponse(
      recordId: LeoUUID(null),
      claimedTransactionFee: Amount(
        amount: 1236,
        currency: Currency(currencyCode: "MWK"),
      ),
      confirmationExpiringAt: DateTime.now().add(10.minutes),
    );

    final error =
        CreateMoneyTransferRequestError.CreateMoneyTransferRequestErrorAgentDisabledForSpecificIntervalError(
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return await Future.delayed(1.seconds, () => result);
  }
}
