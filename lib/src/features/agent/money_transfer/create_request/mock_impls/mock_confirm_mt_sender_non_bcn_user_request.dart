import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockConfirmMTSenderNonBCNUserRequest
    extends ConfirmMoneyTransferSenderNonBCNUserRequestRPC {
  @override
  Future<
    LeoRPCResult<
      ConfirmMoneyTransferSenderNonBCNUserRequestResponse,
      ConfirmMoneyTransferSenderNonBCNUserRequestError
    >
  >
  execute(ConfirmMoneyTransferSenderNonBCNUserRequestRequest request) {
    final response = ConfirmMoneyTransferSenderNonBCNUserRequestResponse(
      succeededAt: DateTime.now(),
      transactionDetail: TransactionStatusDetail(
        itemDetail: [
          TransactionStatusItemDetail(
            label: LocalizedText(en: "Transferring to"),
            valueType:
                TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                  title: "Zikomo Malawa",
                  image: LocalizedImage(
                    en: ThemedImage(dark: mockImage, light: mockImage),
                  ),
                ),
          ),
          TransactionStatusItemDetail(
            label: LocalizedText(en: "Request ID"),
            valueType: TransactionStatusItemDetailValueTypeEnum.COPYABLE_TEXT(
              text: 'ik75tf87',
            ),
          ),
        ],
      ),
    );
    final error =
        ConfirmMoneyTransferSenderNonBCNUserRequestError.ConfirmMoneyTransferSenderNonBCNUserRequestErrorConfirmationTimeoutError(
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return Future.delayed(1.seconds, () => result);
  }
}
