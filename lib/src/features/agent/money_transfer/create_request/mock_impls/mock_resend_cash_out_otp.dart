import 'package:agency_banking_rpcs/agency/resend_money_transfer_sender_otp_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockResendMoneyTransferSenderOTP extends ResendMoneyTransferSenderOTPRPC {
  @override
  Future<
    LeoRPCResult<
      ResendMoneyTransferSenderOTPResponse,
      ResendMoneyTransferSenderOTPError
    >
  >
  execute(ResendMoneyTransferSenderOTPRequest request) {
    final response = ResendMoneyTransferSenderOTPResponse(
      otpDetails: OTPConstants.resendDetails,
    );

    final error =
        ResendMoneyTransferSenderOTPError.ResendMoneyTransferSenderOTPErrorWaitForResendError(
          errorCode: "123",
          nextResendAt: DateTime.now(),
        );

    final result = getLeoRPCResult(
      shouldThrowError: true,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
