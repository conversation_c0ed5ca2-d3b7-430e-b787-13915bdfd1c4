import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockRequestMTSenderOTP extends RequestMoneyTransferSenderOTPRPC {
  @override
  Future<
    LeoRPCResult<
      RequestMoneyTransferSenderOTPResponse,
      RequestMoneyTransferSenderOTPError
    >
  >
  execute(RequestMoneyTransferSenderOTPRequest request) {
    final response = RequestMoneyTransferSenderOTPResponse(
      otpDetails: OTPConstants.otpValidityDetails,
    );
    final error =
        RequestMoneyTransferSenderOTPError.RequestMoneyTransferSenderOTPErrorTooManyOtpRequestsError(
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return Future.delayed(1.seconds, () => result);
  }
}
