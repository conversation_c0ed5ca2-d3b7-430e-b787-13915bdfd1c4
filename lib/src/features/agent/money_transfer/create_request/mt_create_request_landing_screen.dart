import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/create_request_landing_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_text_input_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/amount_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_number_text_field/phone_number_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MTCreateRequestLandingScreen extends StatefulWidget {
  static const id = "/mt-create-request-landing-screen";

  const MTCreateRequestLandingScreen({Key? key}) : super(key: key);

  @override
  State<MTCreateRequestLandingScreen> createState() =>
      _MTCreateRequestLandingScreenState();
}

class _MTCreateRequestLandingScreenState
    extends State<MTCreateRequestLandingScreen> {
  late final _createRequestLandingBloc =
      BlocProvider.of<CreateRequestLandingBloc>(context);
  final _createMoneyTransferFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldAutoValidateCreateMoneyTransfer =
      AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.createRequest,
      ctaWidget: _buildCTA(context),
      child: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Form(
      key: _createMoneyTransferFormKey,
      autovalidateMode: _shouldAutoValidateCreateMoneyTransfer,
      child: Column(
        children: [
          verticalGapTwentyFour,
          PhoneNumberTextField(
            labelText: context.localizations.sendersMobileNumber,
            onPhoneNumberValidated: (phoneNumber) {
              _createRequestLandingBloc.add(
                CreateRequestLandingEvent.addSenderPhoneNumber(phoneNumber),
              );
            },
          ),
          verticalGapSixteen,
          PhoneNumberTextField(
            labelText: context.localizations.recipientsMobileNumber,
            onPhoneNumberValidated: (phoneNumber) {
              _createRequestLandingBloc.add(
                CreateRequestLandingEvent.addReceiverPhoneNumber(phoneNumber),
              );
            },
          ),
          verticalGapSixteen,
          AmountTextField(
            amountTextInputFormatter: AmountTextInputFormatter(
              currency: _createRequestLandingBloc.defaultCurrency,
            ),
            onChanged: (amount) {
              _createRequestLandingBloc.add(
                CreateRequestLandingEvent.addAmount(amount),
              );
            },
          ),
          verticalGapThirtySix,
        ],
      ),
    );
  }

  Widget _buildCTA(BuildContext context) {
    return BlocBuilder<CreateRequestLandingBloc, CreateRequestLandingState>(
      bloc: _createRequestLandingBloc,
      builder: (context, state) {
        return PrimaryButton(
          labelText: context.localizations.continueText,
          onPressed: () {
            final bool isFormValidated =
                _createMoneyTransferFormKey.currentState?.validate() ?? false;
            if (!isFormValidated) {
              setState(() {
                _shouldAutoValidateCreateMoneyTransfer =
                    AutovalidateMode.onUserInteraction;
              });
              return;
            }
            _createRequestLandingBloc.add(
              CreateRequestLandingEvent.createMTRequest(context),
            );
          },
        );
      },
    );
  }
}
