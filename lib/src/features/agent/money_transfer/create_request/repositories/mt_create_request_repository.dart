import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mock_impls/mock_confirm_mt_sender_bcn_user_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mock_impls/mock_confirm_mt_sender_non_bcn_user_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mock_impls/mock_confirm_mt_sender_otp.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mock_impls/mock_create_mt_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mock_impls/mock_request_mt_sender_otp.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mock_impls/mock_resend_cash_out_otp.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MTCreateRequestRepository {
  Future<
    LeoRPCResult<
      CreateMoneyTransferRequestResponse,
      CreateMoneyTransferRequestError
    >
  >
  createMoneyTransferRequest(
    LeoPhoneNumber senderPhoneNumber,
    LeoPhoneNumber recipientPhoneNumber,
    Amount amount,
  ) async {
    final request = CreateMoneyTransferRequestRequest(
      senderPhoneNumber: senderPhoneNumber,
      recipientPhoneNumber: recipientPhoneNumber,
      amount: amount,
    );
    final CreateMoneyTransferRequestRPC impl =
        currentFlavor.isMock
            ? MockCreateMTRequest()
            : CreateMoneyTransferRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      RequestMoneyTransferSenderOTPResponse,
      RequestMoneyTransferSenderOTPError
    >
  >
  requestOTP(LeoUUID recordId) async {
    final request = RequestMoneyTransferSenderOTPRequest(recordId: recordId);
    final RequestMoneyTransferSenderOTPRPC impl =
        currentFlavor.isMock
            ? MockRequestMTSenderOTP()
            : RequestMoneyTransferSenderOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      ResendMoneyTransferSenderOTPResponse,
      ResendMoneyTransferSenderOTPError
    >
  >
  resendOTP(LeoUUID recordId) async {
    final request = ResendMoneyTransferSenderOTPRequest(recordId: recordId);
    final ResendMoneyTransferSenderOTPRPC impl =
        currentFlavor.isMock
            ? MockResendMoneyTransferSenderOTP()
            : ResendMoneyTransferSenderOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      ConfirmMoneyTransferSenderOTPResponse,
      ConfirmMoneyTransferSenderOTPError
    >
  >
  confirmSenderOTP(LeoUUID recordId, Otp otp) async {
    final request = ConfirmMoneyTransferSenderOTPRequest(
      recordId: recordId,
      otp: otp,
    );
    final ConfirmMoneyTransferSenderOTPRPC impl =
        currentFlavor.isMock
            ? MockConfirmMoneyTransferSenderOTP()
            : ConfirmMoneyTransferSenderOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      ConfirmMoneyTransferSenderBCNUserRequestResponse,
      ConfirmMoneyTransferSenderBCNUserRequestError
    >
  >
  confirmMTSenderBCNUserRequest(
    LeoUUID recordId,
    NationalId recipientNationalId,
  ) async {
    final request = ConfirmMoneyTransferSenderBCNUserRequestRequest(
      recordId: recordId,
      recipientNationalId: recipientNationalId,
    );
    final ConfirmMoneyTransferSenderBCNUserRequestRPC impl =
        currentFlavor.isMock
            ? MockConfirmMTSenderBCNUserRequest()
            : ConfirmMoneyTransferSenderBCNUserRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      ConfirmMoneyTransferSenderNonBCNUserRequestResponse,
      ConfirmMoneyTransferSenderNonBCNUserRequestError
    >
  >
  confirmMTSenderNonBCNUserRequest(
    LeoUUID recordId,
    NationalId recipientNationalId,
    NationalId senderNationalId,
    LeoUUID senderImageId,
    ABUserName senderName,
    String senderAddress,
  ) async {
    final request = ConfirmMoneyTransferSenderNonBCNUserRequestRequest(
      recordId: recordId,
      recipientNationalId: recipientNationalId,
      senderName: senderName,
      senderAddress: senderAddress,
      senderImageId: senderImageId,
      senderNationalId: senderNationalId,
    );
    final ConfirmMoneyTransferSenderNonBCNUserRequestRPC impl =
        currentFlavor.isMock
            ? MockConfirmMTSenderNonBCNUserRequest()
            : ConfirmMoneyTransferSenderNonBCNUserRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
