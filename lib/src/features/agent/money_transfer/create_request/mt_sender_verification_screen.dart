import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/create_request_landing_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/mt_sender_verification_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/otp_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MTSenderVerificationScreen extends StatefulWidget {
  static const id = "/mt-sender-verification-screen";

  const MTSenderVerificationScreen({Key? key}) : super(key: key);

  @override
  State<MTSenderVerificationScreen> createState() =>
      _MTSenderVerificationScreenState();
}

class _MTSenderVerificationScreenState
    extends State<MTSenderVerificationScreen> {
  late final _createRequestLandingBloc =
      BlocProvider.of<CreateRequestLandingBloc>(context);
  late final _mTSenderVerificationBloc =
      BlocProvider.of<MTSenderVerificationBloc>(context);
  final _senderOTPFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldAutoValidateSenderOTP = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      appBarTitle: context.localizations.senderVerification,
      isCancellable: true,
      ctaWidget: _buildCTA(context),
      child: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Form(
      key: _senderOTPFormKey,
      autovalidateMode: _shouldAutoValidateSenderOTP,
      child: Column(
        children: [
          verticalGapTwentyFour,
          OTPWidget(
            labelText: context.localizations.enterSendersOTP,
            otpValidityDetails: _createRequestLandingBloc.otpValidityDetails,
            onChanged: (otp) {
              _mTSenderVerificationBloc.add(
                MTSenderVerificationEvent.addOtp(otp),
              );
            },
            onResend: () async {
              _mTSenderVerificationBloc.add(
                MTSenderVerificationEvent.resendOTP(
                  context,
                  _createRequestLandingBloc
                      .createMoneyTransferResponse
                      .recordId,
                ),
              );

              // Wait For Bloc to send some State.
              final latestState =
                  await _mTSenderVerificationBloc.stream.firstOrNull;
              return switch (latestState) {
                OTPResentSuccessfully(:final resendDetails) => resendDetails,
                _ => null,
              };
            },
          ),
          verticalGapThirtySix,
        ],
      ),
    );
  }

  Widget _buildCTA(BuildContext context) {
    return BlocBuilder<MTSenderVerificationBloc, MTSenderVerificationState>(
      bloc: _mTSenderVerificationBloc,
      builder: (context, state) {
        return PrimaryButton(
          labelText: context.localizations.continueText,
          onPressed: () {
            final bool isFormValidated =
                _senderOTPFormKey.currentState?.validate() ?? false;
            if (!isFormValidated) {
              setState(() {
                _shouldAutoValidateSenderOTP =
                    AutovalidateMode.onUserInteraction;
              });
              return;
            }
            _mTSenderVerificationBloc.add(
              MTSenderVerificationEvent.confirmMTSenderOTP(
                context,
                _createRequestLandingBloc.createMoneyTransferResponse.recordId,
                _createRequestLandingBloc.transactionAmount,
              ),
            );
          },
        );
      },
    );
  }
}
