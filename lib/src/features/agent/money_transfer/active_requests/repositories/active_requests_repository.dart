import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/mock_impls/mock_get_money_transfer_active_requests.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/mock_impls/mock_resend_money_transfer_recipient_request_id.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class ActiveRequestsRepository {
  Future<
    LeoRPCResult<
      GetMoneyTransferActiveRequestsResponse,
      GetMoneyTransferActiveRequestsError
    >
  >
  fetchMoneyTransferActiveRequests() async {
    final request = GetMoneyTransferActiveRequestsRequest();

    final GetMoneyTransferActiveRequestsRPC impl =
        currentFlavor.isMock
            ? MockGetMoneyTransferActiveRequests()
            : GetMoneyTransferActiveRequestsRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      ResendMoneyTransferRecipientRequestIdResponse,
      ResendMoneyTransferRecipientRequestIdError
    >
  >
  resendRequestId(NationalId senderNationalId, LeoUUID recordId) async {
    final request = ResendMoneyTransferRecipientRequestIdRequest(
      senderNationalId: senderNationalId,
      recordId: recordId,
    );

    final ResendMoneyTransferRecipientRequestIdRPC impl =
        currentFlavor.isMock
            ? MockResendMoneyTransferRecipientRequestId()
            : ResendMoneyTransferRecipientRequestIdRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
