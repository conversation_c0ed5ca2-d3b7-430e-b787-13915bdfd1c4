import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetMoneyTransferActiveRequests
    extends GetMoneyTransferActiveRequestsRPC {
  @override
  Future<
    LeoRPCResult<
      GetMoneyTransferActiveRequestsResponse,
      GetMoneyTransferActiveRequestsError
    >
  >
  execute(GetMoneyTransferActiveRequestsRequest request) async {
    final response = GetMoneyTransferActiveRequestsResponse(
      moneyTransferActiveRequests: <MoneyTransferActiveRequestInfo>[
        MoneyTransferActiveRequestInfo(
          recordId: LeoUUID(null),
          senderName: ABUserName(text: '<PERSON>'),
          senderPhoneNumber: LeoPhoneNumber('+91 **********'),
          recipientNationalId: NationalId(id: 'NFKDLE02'),
          recipientPhoneNumber: LeoPhoneNumber('+91 **********'),
          amount: Amount(
            amount: *********,
            currency: Currency(currencyCode: "MWK"),
          ),
          requestCreatedAt: DateTime.now(),
          transactionFee: Amount(
            amount: 100000,
            currency: Currency(currencyCode: "MWK"),
          ),
          receivingAmount: Amount(
            amount: 2161400,
            currency: Currency(currencyCode: "ZMW"),
          ),
        ),
        MoneyTransferActiveRequestInfo(
          recordId: LeoUUID(null),
          senderName: ABUserName(text: 'Mark Lee'),
          senderPhoneNumber: LeoPhoneNumber('+91 **********'),
          recipientNationalId: NationalId(id: 'NFKDLE02'),
          recipientPhoneNumber: LeoPhoneNumber('+91 **********'),
          amount: Amount(
            amount: *********,
            currency: Currency(currencyCode: "MWK"),
          ),
          requestCreatedAt: DateTime.now(),
          transactionFee: Amount(
            amount: 100000,
            currency: Currency(currencyCode: "MWK"),
          ),
          nextResendAt: DateTime.now().add(20.seconds),
        ),
        MoneyTransferActiveRequestInfo(
          recordId: LeoUUID(null),
          senderName: ABUserName(text: 'David Kahele'),
          senderPhoneNumber: LeoPhoneNumber('+91 **********'),
          recipientNationalId: NationalId(id: 'NFKDLE02'),
          recipientPhoneNumber: LeoPhoneNumber('+91 **********'),
          amount: Amount(
            amount: *********,
            currency: Currency(currencyCode: "MWK"),
          ),
          requestCreatedAt: DateTime.now(),
          transactionFee: Amount(
            amount: 100000,
            currency: Currency(currencyCode: "MWK"),
          ),
        ),
      ],
    );

    final error =
        GetMoneyTransferActiveRequestsError.GetMoneyTransferActiveRequestsErrorAgentDisabledForSpecificIntervalError(
          errorCode: "123",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return await Future.delayed(1.seconds, () => result);
  }
}
