import 'package:agency_banking_rpcs/agency/resend_money_transfer_recipient_request_id_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockResendMoneyTransferRecipientRequestId
    extends ResendMoneyTransferRecipientRequestIdRPC {
  @override
  Future<
    LeoRPCResult<
      ResendMoneyTransferRecipientRequestIdResponse,
      ResendMoneyTransferRecipientRequestIdError
    >
  >
  execute(ResendMoneyTransferRecipientRequestIdRequest request) async {
    final response = ResendMoneyTransferRecipientRequestIdResponse(
      nextResendAt: DateTime.now().add(20.seconds),
    );

    final error =
        ResendMoneyTransferRecipientRequestIdError.ResendMoneyTransferRecipientRequestIdErrorAgentDisabledForSpecificIntervalError(
          errorCode: "123",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return await Future.delayed(1.seconds, () => result);
  }
}
