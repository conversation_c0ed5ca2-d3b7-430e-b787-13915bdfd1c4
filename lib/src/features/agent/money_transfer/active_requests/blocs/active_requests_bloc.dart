import 'package:agency_banking_rpcs/agency/money_transfer_active_request_info_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/repositories/active_requests_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

part 'active_requests_bloc.freezed.dart';

class ActiveRequestsBloc extends Bloc<ActiveRequestsEvent, ActiveRequestsState>
    with RPC<PERSON>andler {
  final ActiveRequestsRepository _activeRequestsRepository =
      ActiveRequestsRepository();
  final RefreshController refreshController = RefreshController();
  late List<MoneyTransferActiveRequestInfo> _activeRequestList;

  ActiveRequestsBloc() : super(const ActiveRequestsState.initial()) {
    on<ActiveRequestsEvent>((event, emit) async {
      switch (event) {
        case FetchRequests():
          emit(const ActiveRequestsState.loading());
          await _fetchSenderRequests(emit);
        case PullToRefresh():
          emit(const ActiveRequestsState.refreshInProgress());
          await _fetchSenderRequests(emit);
      }
    });
  }

  List<MoneyTransferActiveRequestInfo> get requestList => _activeRequestList;

  Future<void> _fetchSenderRequests(Emitter<ActiveRequestsState> emit) async {
    await rpcHandler(
      () async {
        final rpcResult =
            await _activeRequestsRepository.fetchMoneyTransferActiveRequests();
        rpcResult.when(
          response: (response) {
            if (response.moneyTransferActiveRequests.any(
              (element) => element.receivingAmount != null,
            )) {
              ProminentErrorHandler.exchangeRateNotSupported();
            }
            _activeRequestList = response.moneyTransferActiveRequests;
            emit(const ActiveRequestsState.dataFetched());
            refreshController.refreshCompleted();
          },
          error: (error) {
            refreshController.refreshFailed();
            emit(const ActiveRequestsState.prominentError());
            error.when(
              agentDisabledForSpecificInterval: (_) {
                ProminentErrorHandler.agentDisabledForSpecificInterval();
              },
            );
          },
        );
      },
      onTransientError: (_) {
        refreshController.refreshFailed();
        emit(const ActiveRequestsState.transientError());
      },
    );
  }
}

@freezed
sealed class ActiveRequestsState with _$ActiveRequestsState {
  const factory ActiveRequestsState.initial() = Initial;

  const factory ActiveRequestsState.loading() = Loading;

  const factory ActiveRequestsState.dataFetched() = DataFetched;

  const factory ActiveRequestsState.refreshInProgress() = RefreshInProgress;

  const factory ActiveRequestsState.prominentError() = ProminentError;

  const factory ActiveRequestsState.transientError() = TransientError;
}

@freezed
sealed class ActiveRequestsEvent with _$ActiveRequestsEvent {
  const factory ActiveRequestsEvent.fetchRequests() = FetchRequests;

  const factory ActiveRequestsEvent.pullToRefresh() = PullToRefresh;
}
