import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/repositories/active_requests_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

part 'active_requests_sender_request_bloc.freezed.dart';

class ActiveRequestsSenderRequestBloc
    extends
        Bloc<ActiveRequestsSenderRequestEvent, ActiveRequestsSenderRequestState>
    with RPCHandler {
  String? _nationalId;
  LeoUUID? _recordId;
  Coordinate shopCoordinates;
  final ActiveRequestsRepository _activeRequestsRepository =
      ActiveRequestsRepository();
  final _locationService = locator<LocationService>();
  final MoneyTransferActiveRequestInfo activeRequestInfo;
  late bool _restrictResend =
      activeRequestInfo.nextResendAt?.isAfter(DateTime.now()) ?? false;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  AutovalidateMode autoValidateMode = AutovalidateMode.disabled;

  ActiveRequestsSenderRequestBloc({
    required this.shopCoordinates,
    required this.activeRequestInfo,
  }) : super(const ActiveRequestsSenderRequestState.initial()) {
    on<ActiveRequestsSenderRequestEvent>((event, emit) async {
      switch (event) {
        case AddNationalId(:final nationalId):
          if (state is Loading) return;
          _nationalId = nationalId;
        case AttemptResendRequestId(:final context, :final recordId):
          if (!(formKey.currentState?.validate() ?? false)) {
            autoValidateMode = AutovalidateMode.onUserInteraction;
            return;
          }
          _recordId = recordId;
          await _resendRequestId(context, emit);
        case DurationOver():
          _restrictResend = false;
          emit(const ActiveRequestsSenderRequestState.allowResend());
      }
    });
  }

  bool get restrictResend => _restrictResend;

  Future<void> _resendRequestId(
    BuildContext context,
    Emitter<ActiveRequestsSenderRequestState> emit,
  ) async {
    final LocationDetails locationDetails = shopCoordinates.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const ActiveRequestsSenderRequestState.prominentError());
      return;
    }
    await rpcHandler(
      () async {
        _showSpinner(context);
        emit(const ActiveRequestsSenderRequestState.loading());
        final rpcResult = await _activeRequestsRepository.resendRequestId(
          NationalId(id: _nationalId!),
          _recordId!,
        );
        if (context.mounted) {
          //Popping the spinner dialog
          context.navigator.pop();
          _handleResendRequestIdRpcResult(rpcResult, emit, context);
        }
      },
      onTransientError: (_) {
        context.navigator.pop();
        emit(const ActiveRequestsSenderRequestState.transientError());
      },
    );
  }

  void _showSpinner(BuildContext context) {
    AgencyAppDialog.showSpinnerDialog(context);
  }

  void _handleResendRequestIdRpcResult(
    LeoRPCResult<
      ResendMoneyTransferRecipientRequestIdResponse,
      ResendMoneyTransferRecipientRequestIdError
    >
    rpcResult,
    Emitter<ActiveRequestsSenderRequestState> emit,
    BuildContext context,
  ) {
    rpcResult.when(
      response: (response) {
        _showSuccessDialog(context);
        _restrictResend = true;
        emit(
          ActiveRequestsSenderRequestState.requestIdSentSuccessfully(
            response.nextResendAt,
          ),
        );
      },
      error: (error) {
        emit(const ActiveRequestsSenderRequestState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          invalidSenderNationalId: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.invalidNationalID,
              buttonText: context.localizations.tryAgain,
            );
          },
          couldNotSendRequestId: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText:
                  context.localizations.couldNotSendRequestIdErrorMessage,
              buttonText: context.localizations.tryAgain,
            );
          },
          resendRequestsBlockedTemporarily: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context.localizations.resendRequestsBlockedTemporarily,
              onOkay: navigateToHomeScreen,
            );
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          refundFlowWasInitiated: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context.localizations.refundFlowWasInitiatedErrorMessage,
              onOkay: navigateToHomeScreen,
            );
          },
        );
      },
    );
  }

  void _showSuccessDialog(BuildContext context) {
    // Design for this dialog is same as the error dialogs.
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.requestIdSentToRecipient,
    );
  }
}

@freezed
sealed class ActiveRequestsSenderRequestState
    with _$ActiveRequestsSenderRequestState {
  const factory ActiveRequestsSenderRequestState.initial() = Initial;

  const factory ActiveRequestsSenderRequestState.loading() = Loading;

  const factory ActiveRequestsSenderRequestState.prominentError() =
      ProminentError;

  const factory ActiveRequestsSenderRequestState.transientError() =
      TransientError;

  const factory ActiveRequestsSenderRequestState.requestIdSentSuccessfully(
    DateTime nextResendAt,
  ) = RequestIdSentSuccessfully;

  const factory ActiveRequestsSenderRequestState.allowResend() = AllowResend;
}

@freezed
sealed class ActiveRequestsSenderRequestEvent
    with _$ActiveRequestsSenderRequestEvent {
  const factory ActiveRequestsSenderRequestEvent.addNationalId(
    String nationalId,
  ) = AddNationalId;

  const factory ActiveRequestsSenderRequestEvent.attemptResendRequestId(
    BuildContext context,
    LeoUUID recordId,
  ) = AttemptResendRequestId;

  const factory ActiveRequestsSenderRequestEvent.durationOver() = DurationOver;
}
