import 'package:agency_banking_rpcs/agency/money_transfer_active_request_info_type.dart';
import 'package:agency_banking_rpcs/types/coordinate_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/blocs/active_requests_sender_request_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/widgets/duration_text.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/utils/validators.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_outlined_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/section_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActiveRequestsSenderRequestScreen extends StatefulWidget {
  static const id = "active-requests-sender-request-screen";

  const ActiveRequestsSenderRequestScreen({Key? key}) : super(key: key);

  @override
  State<ActiveRequestsSenderRequestScreen> createState() =>
      _ActiveRequestsSenderRequestScreenState();
}

class _ActiveRequestsSenderRequestScreenState
    extends State<ActiveRequestsSenderRequestScreen> {
  late final _activeRequestsSenderRequestBloc =
      BlocProvider.of<ActiveRequestsSenderRequestBloc>(context);
  late final _activeRequestInfo =
      _activeRequestsSenderRequestBloc.activeRequestInfo;
  late DateTime? _nextResendAt =
      _activeRequestsSenderRequestBloc.activeRequestInfo.nextResendAt;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      disableBackGesture: false,
      padding: const EdgeInsets.only(top: dimenEight),
      appBarTitle: context.localizations.senderRequest,
      ctaWidget: _buildCTA(context),
      child: _getBody(context),
    );
  }

  Widget _getBody(BuildContext context) {
    return Column(
      children: <Widget>[
        _buildRequestInfo(context),
        _buildTransactionDetailsWidget(context),
        TitleWidget(title: context.localizations.resendRequestId),
        verticalGapSixteen,
        _buildInfoText(context),
        verticalGapSixteen,
        _buildTextField(context),
      ],
    );
  }

  TransactionDetailsWidget _buildTransactionDetailsWidget(
    BuildContext context,
  ) {
    return TransactionDetailsWidget(
      transactionAmount: _activeRequestInfo.amount,
      transactionFee: _activeRequestInfo.transactionFee,
      receivingAmount:
          _activeRequestInfo.receivingAmount ??
          AmountFormatter.getDifferenceBetweenAmounts(
            _activeRequestInfo.amount,
            _activeRequestInfo.transactionFee,
          ),
      transactionAmountLabel: context.localizations.amountTransferred,
      receivingAmountLabel: context.localizations.recipientReceivableAmount,
    );
  }

  Widget _buildTextField(BuildContext context) {
    return Form(
      key: _activeRequestsSenderRequestBloc.formKey,
      autovalidateMode: _activeRequestsSenderRequestBloc.autoValidateMode,
      child: Padding(
        padding: horizontalPaddingSixteen,
        child: PrimaryTextField(
          inputFormatters: [nationalIdFormatter],
          keyboardType: TextInputType.name,
          maxLength: nationalIdMaxLength,
          labelText: context.localizations.sendersNationalIDNumber,
          onChanged: (senderNationalId) {
            _activeRequestsSenderRequestBloc.add(
              ActiveRequestsSenderRequestEvent.addNationalId(senderNationalId),
            );
          },
          validator:
              (senderNationalId) => Validators.emptyValidator(
                context,
                context.localizations.sendersNationalIDNumber,
                senderNationalId,
              ),
        ),
      ),
    );
  }

  Widget _buildInfoText(BuildContext context) {
    return Padding(
      padding: horizontalPaddingSixteen,
      child: Text(
        context.localizations.resendRequestIdInfoText,
        style: context.appTextStyles.smallText1.copyWith(
          color: context.appColors.neutralShadeDefaultColor,
        ),
      ),
    );
  }

  Widget _buildRequestInfo(BuildContext context) {
    return SectionWidget(
      title: context.localizations.basicDetails,
      children: [
        InfoLabel(
          title: context.localizations.sendersName,
          bodyText: _activeRequestInfo.senderName.text,
        ),
        InfoLabel(
          title: context.localizations.transferTo,
          bodyText: _activeRequestInfo.recipientNationalId.id,
        ),
      ],
    );
  }

  Widget _buildCTA(BuildContext context) {
    return BlocBuilder<
      ActiveRequestsSenderRequestBloc,
      ActiveRequestsSenderRequestState
    >(
      bloc: _activeRequestsSenderRequestBloc,
      builder: (context, state) {
        if (state is RequestIdSentSuccessfully) {
          _nextResendAt = state.nextResendAt;
        }
        return Column(
          children: [
            PrimaryOutlinedButton(
              isEnabled: !_activeRequestsSenderRequestBloc.restrictResend,
              labelText: context.localizations.resendRequestIdToRecipient,
              onPressed: () {
                _activeRequestsSenderRequestBloc.add(
                  ActiveRequestsSenderRequestEvent.attemptResendRequestId(
                    context,
                    _activeRequestInfo.recordId,
                  ),
                );
                dismissKeyboard();
              },
            ),
            if (_activeRequestsSenderRequestBloc.restrictResend) ...{
              verticalGapTwentyFour,
              DurationText(
                nextResendAt: _nextResendAt,
                onDurationOver:
                    () => _activeRequestsSenderRequestBloc.add(
                      const ActiveRequestsSenderRequestEvent.durationOver(),
                    ),
              ),
              verticalGapEight,
            } else
              verticalGapSixteen,
          ],
        );
      },
    );
  }
}

class ActiveRequestsSenderRequestScreenArguments {
  final MoneyTransferActiveRequestInfo activeRequestInfo;
  final Coordinate shopCoordinates;

  ActiveRequestsSenderRequestScreenArguments({
    required this.activeRequestInfo,
    required this.shopCoordinates,
  });
}
