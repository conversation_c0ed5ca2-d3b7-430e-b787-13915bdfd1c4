import 'dart:async';

import 'package:bcn_agency_banking_flutter/src/helpers/formatted_duration_left_helper.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class DurationText extends StatefulWidget {
  const DurationText({
    Key? key,
    required this.nextResendAt,
    required this.onDurationOver,
  }) : super(key: key);

  final DateTime? nextResendAt;
  final VoidCallback onDurationOver;

  @override
  State<DurationText> createState() => _DurationTextState();
}

class _DurationTextState extends State<DurationText> {
  late final DateTime _nextResendAt = widget.nextResendAt!.toLocal();

  String get _formattedDurationLeftForNextResend =>
      FormattedDurationLeftHelper.getFormattedDurationLeftString(_nextResendAt);

  late final Timer timer;

  bool get _isDurationOver => _nextResendAt.isBefore(DateTime.now());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      timer = Timer.periodic(1.seconds, (_) {
        final now = DateTime.now();
        if (_nextResendAt.isAfter(now) ||
            _nextResendAt.difference(now).inSeconds < 2) {
          setState(() {});
        }
        if (_isDurationOver) {
          widget.onDurationOver();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      context.localizations.resendRequestIdInDuration(
        _formattedDurationLeftForNextResend,
      ),
      style: context.appTextStyles.smallText1.copyWith(
        color: context.appColors.neutralShade7Color,
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    timer.cancel();
  }
}
