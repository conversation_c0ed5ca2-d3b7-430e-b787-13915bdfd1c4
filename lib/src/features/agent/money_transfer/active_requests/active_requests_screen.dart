import 'package:agency_banking_rpcs/types/coordinate_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/active_requests_sender_request_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/blocs/active_requests_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/generic_error_or_empty_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/pull_to_refresh_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../widgets/request_card.dart';

class ActiveRequestsScreen extends StatefulWidget {
  static const id = "/active-requests-screen";

  const ActiveRequestsScreen({Key? key, required this.shopCoordinates})
    : super(key: key);

  final Coordinate shopCoordinates;

  @override
  State<ActiveRequestsScreen> createState() => _ActiveRequestsScreenState();
}

class _ActiveRequestsScreenState extends State<ActiveRequestsScreen> {
  late final ActiveRequestsBloc _activeRequestsBloc =
      BlocProvider.of<ActiveRequestsBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.activeRequests),
      body: _getBody(context),
    );
  }

  Widget _getBody(BuildContext context) {
    return BlocBuilder(
      bloc: _activeRequestsBloc,
      buildWhen: (_, currentState) {
        return currentState is! RefreshInProgress;
      },
      builder: (context, state) {
        if (state is DataFetched) {
          return _buildStateWidget(context);
        } else if (state is TransientError) {
          return NoInternetWidget(
            onRetryButtonClicked: () {
              _activeRequestsBloc.add(
                const ActiveRequestsEvent.fetchRequests(),
              );
            },
          );
        } else {
          return const Spinner();
        }
      },
    );
  }

  Widget _buildStateWidget(BuildContext context) {
    return PullToRefreshWidget(
      onRefresh:
          () => _activeRequestsBloc.add(
            const ActiveRequestsEvent.pullToRefresh(),
          ),
      controller: _activeRequestsBloc.refreshController,
      child:
          _activeRequestsBloc.requestList.isEmpty
              ? _buildEmptyState(context)
              : _buildActiveRequestList(),
    );
  }

  GenericErrorOrEmptyWidget _buildEmptyState(BuildContext context) {
    return GenericErrorOrEmptyWidget(
      labelText: context.localizations.noActiveRequestsYet,
      iconAssetPath: ABAssets.exchangeAltIcon,
    );
  }

  Widget _buildActiveRequestList() {
    return SingleChildScrollView(
      child: Padding(
        padding: commonScreenPadding,
        child: ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: _getActiveRequestCard,
          separatorBuilder: (context, index) => verticalGapSixteen,
          itemCount: _activeRequestsBloc.requestList.length,
        ),
      ),
    );
  }

  Widget _getActiveRequestCard(BuildContext context, int index) {
    return RequestCard(
      onTap: () async {
        await context.navigator.pushNamed(
          ActiveRequestsSenderRequestScreen.id,
          arguments: ActiveRequestsSenderRequestScreenArguments(
            activeRequestInfo: _activeRequestsBloc.requestList[index],
            shopCoordinates: widget.shopCoordinates,
          ),
        );
        _refreshScreen();
      },
      requestCardModel: RequestCardModel.activeRequest(
        _activeRequestsBloc.requestList[index],
      ),
    );
  }

  void _refreshScreen() {
    if (!mounted) return;
    _activeRequestsBloc.refreshController.requestRefresh();
  }
}
