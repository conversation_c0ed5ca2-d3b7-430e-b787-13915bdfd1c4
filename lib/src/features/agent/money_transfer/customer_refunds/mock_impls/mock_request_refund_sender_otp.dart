import 'package:agency_banking_rpcs/agency/request_refund_sender_otp_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockRequestRefundSenderOTP extends RequestRefundSenderOTPRPC {
  @override
  Future<
    LeoRPCResult<RequestRefundSenderOTPResponse, RequestRefundSenderOTPError>
  >
  execute(RequestRefundSenderOTPRequest request) async {
    final response = RequestRefundSenderOTPResponse(
      otpDetails: OTPConstants.otpValidityDetails,
    );

    final error =
        RequestRefundSenderOTPError.RequestRefundSenderOTPErrorRefundRequestBlockedError(
          errorCode: "123",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(1.seconds, () => result);
  }
}
