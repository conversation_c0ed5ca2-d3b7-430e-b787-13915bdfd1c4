import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockAgentRefundMoneyTransferRequest
    extends AgentRefundMoneyTransferRequestRPC {
  @override
  Future<
    LeoRPCResult<
      AgentRefundMoneyTransferRequestResponse,
      AgentRefundMoneyTransferRequestError
    >
  >
  execute(AgentRefundMoneyTransferRequestRequest request) async {
    final response = AgentRefundMoneyTransferRequestResponse(
      succeededAt: DateTime.now(),
      transactionDetail: TransactionStatusDetail(
        description: LocalizedText(en: "Fee charged for customer"),
        itemDetail: [
          TransactionStatusItemDetail(
            label: LocalizedText(en: "Transferred to"),
            valueType:
                TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                  title: "<PERSON>",
                  description: '+91 **********',
                  image: LocalizedImage(
                    en: ThemedImage(dark: mockImage, light: mockImage),
                  ),
                ),
          ),
        ],
      ),
    );

    final error =
        AgentRefundMoneyTransferRequestError.AgentRefundMoneyTransferRequestErrorIncorrectOtpError(
          errorCode: "123",
          numberOfValidationAttemptsLeft: 0,
        );

    final result = getLeoRPCResult(
      shouldThrowError: !checkWhetherOTPValid(request.otp),
      response: response,
      error: error,
    );

    return Future.delayed(1.seconds, () => result);
  }
}
