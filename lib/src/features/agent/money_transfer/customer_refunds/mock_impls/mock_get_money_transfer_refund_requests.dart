import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_phone_number.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

class MockGetMoneyTransferRefundRequests
    extends GetMoneyTransferRefundRequestsRPC {
  @override
  Future<
    LeoRPCResult<
      GetMoneyTransferRefundRequestsResponse,
      GetMoneyTransferRefundRequestsError
    >
  >
  execute(GetMoneyTransferRefundRequestsRequest request) async {
    final response = GetMoneyTransferRefundRequestsResponse(
      moneyTransferRefundRequests: [
        MoneyTransferRefundRequest(
          recordId: LeoUUID(null),
          senderName: ABUserName(text: 'Dall E'),
          senderPhoneNumber: LeoPhoneNumber('+91 **********'),
          recipientNationalId: NationalId(id: 'NFKDLE02'),
          recipientPhoneNumber: LeoPhoneNumber('+91 **********'),
          amount: Amount(
            amount: *********,
            currency: Currency(currencyCode: 'MWK'),
          ),
          requestCreatedAt: DateTime.now(),
          transactionFee: Amount(
            amount: 100000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          requestId: ABRequestId(id: 'AE14n7xC'),
          isTransactionBlocked: false,
          recipientReceivableAmount: Amount(
            amount: 2168400,
            currency: Currency(currencyCode: "ZMW"),
          ),
          senderRefundAmount: Amount(
            amount: 99900000,
            currency: Currency(currencyCode: 'MWK'),
          ),
        ),
        MoneyTransferRefundRequest(
          recordId: LeoUUID(null),
          senderName: ABUserName(text: 'Kenneth Isaac'),
          senderPhoneNumber: LeoPhoneNumber('+91 **********'),
          recipientNationalId: NationalId(id: 'NFKDLE02'),
          recipientPhoneNumber: LeoPhoneNumber('+91 **********'),
          amount: Amount(
            amount: *********,
            currency: Currency(currencyCode: 'MWK'),
          ),
          requestCreatedAt: DateTime.now(),
          transactionFee: Amount(
            amount: 100000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          requestId: ABRequestId(id: 'AE14n7xC'),
          isTransactionBlocked: false,
          senderRefundAmount: Amount(
            amount: 2168400,
            currency: Currency(currencyCode: 'ZMW'),
          ),
        ),
        MoneyTransferRefundRequest(
          recordId: LeoUUID(null),
          senderName: ABUserName(text: 'Kenneth Isaac'),
          senderPhoneNumber: LeoPhoneNumber('+91 **********'),
          recipientNationalId: NationalId(id: 'NFKDLE02'),
          recipientPhoneNumber: LeoPhoneNumber('+91 **********'),
          amount: Amount(
            amount: *********,
            currency: Currency(currencyCode: 'MWK'),
          ),
          requestCreatedAt: DateTime.now(),
          transactionFee: Amount(
            amount: 100000,
            currency: Currency(currencyCode: 'MWK'),
          ),
          requestId: ABRequestId(id: 'AE14n7xC'),
          isTransactionBlocked: true,
          senderRefundAmount: Amount(
            amount: 99900000,
            currency: Currency(currencyCode: 'MWK'),
          ),
        ),
      ],
    );

    final error =
        GetMoneyTransferRefundRequestsError.GetMoneyTransferRefundRequestsErrorAgentDisabledForSpecificIntervalError(
          errorCode: "123",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return await Future.delayed(1.seconds, () => result);
  }
}
