import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockResendRefundSenderOTP extends ResendRefundSenderOTPRPC {
  @override
  Future<
    LeoRPCResult<ResendRefundSenderOTPResponse, ResendRefundSenderOTPError>
  >
  execute(ResendRefundSenderOTPRequest request) async {
    final response = ResendRefundSenderOTPResponse(
      otpDetails: OTPConstants.resendDetails,
    );

    final error =
        ResendRefundSenderOTPError.ResendRefundSenderOTPErrorAgentDisabledForSpecificIntervalError(
          errorCode: "123",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(1.seconds, () => result);
  }
}
