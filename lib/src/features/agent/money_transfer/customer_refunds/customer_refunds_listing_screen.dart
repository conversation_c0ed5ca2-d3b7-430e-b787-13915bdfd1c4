import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/customer_refunds_otp_screen.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/generic_error_or_empty_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/pull_to_refresh_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/request_card.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'blocs/customer_refunds_listing_bloc.dart';

class CustomerRefundsListingScreen extends StatefulWidget {
  static const id = "/customer-refunds-listing-screen";

  const CustomerRefundsListingScreen({Key? key, required this.shopCoordinate})
    : super(key: key);

  final Coordinate shopCoordinate;

  @override
  State<CustomerRefundsListingScreen> createState() =>
      _CustomerRefundsListingScreenState();
}

class _CustomerRefundsListingScreenState
    extends State<CustomerRefundsListingScreen> {
  late final _customerRefundsListingBloc =
      BlocProvider.of<CustomerRefundsListingBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(title: context.localizations.customerRefunds),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return BlocBuilder(
      bloc: _customerRefundsListingBloc,
      buildWhen: (_, currentState) {
        return currentState is! RefreshInProgress;
      },
      builder: (context, state) {
        if (state is DataFetched) {
          return _buildStateWidget(context);
        } else if (state is TransientError) {
          return NoInternetWidget(
            onRetryButtonClicked: () {
              _customerRefundsListingBloc.add(
                const CustomerRefundsListingEvent.fetchRequests(),
              );
            },
          );
        } else {
          return const Spinner();
        }
      },
    );
  }

  Widget _buildStateWidget(BuildContext context) {
    return PullToRefreshWidget(
      onRefresh:
          () => _customerRefundsListingBloc.add(
            const CustomerRefundsListingEvent.pullToRefresh(),
          ),
      controller: _customerRefundsListingBloc.refreshController,
      child:
          _customerRefundsListingBloc.refundList.isEmpty
              ? _buildEmptyState(context)
              : _buildRefundRequestList(),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return GenericErrorOrEmptyWidget(
      labelText: context.localizations.customerRefundsEmptyStateTitle,
      iconAssetPath: ABAssets.exchangeAltIcon,
    );
  }

  Widget _buildRefundRequestList() {
    return ListView.separated(
      padding: commonScreenPadding,
      itemBuilder: _getCustomerRefundRequestCard,
      separatorBuilder: (context, index) => verticalGapSixteen,
      itemCount: _customerRefundsListingBloc.refundList.length,
    );
  }

  Widget _getCustomerRefundRequestCard(BuildContext context, int index) {
    return RequestCard(
      onTap: () async {
        await context.navigator.pushNamed(
          CustomerRefundsOTPScreen.id,
          arguments: CustomerRefundsOTPScreenArguments(
            moneyTransferRefundRequest:
                _customerRefundsListingBloc.refundList[index],
            shopCoordinate: widget.shopCoordinate,
          ),
        );
        _refreshScreen();
      },
      requestCardModel: RequestCardModel.customerRefundRequest(
        _customerRefundsListingBloc.refundList[index],
      ),
    );
  }

  void _refreshScreen() {
    if (!mounted) return;
    _customerRefundsListingBloc.refreshController.requestRefresh(
      needMove: false,
    );
  }
}
