import 'package:agency_banking_rpcs/agency/money_transfer_refund_request_type.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/repository/customer_refunds_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../../core/logger.dart';

part 'customer_refunds_listing_bloc.freezed.dart';

class CustomerRefundsListingBloc
    extends Bloc<CustomerRefundsListingEvent, CustomerRefundsListingState>
    with RPCHandler {
  final _customerRefundsRepository = CustomerRefundsRepository();
  final refreshController = RefreshController();
  late List<MoneyTransferRefundRequest> _customerRefundsList;

  CustomerRefundsListingBloc() : super(const Initial()) {
    on<CustomerRefundsListingEvent>((event, emit) async {
      switch (event) {
        case FetchRequests():
          emit(const CustomerRefundsListingState.loading());
          await _fetchCustomerRefundsListing(emit);
        case PullToRefresh():
          emit(const CustomerRefundsListingState.refreshInProgress());
          await _fetchCustomerRefundsListing(emit);
      }
    });
  }

  List<MoneyTransferRefundRequest> get refundList => _customerRefundsList;

  Future<void> _fetchCustomerRefundsListing(
    Emitter<CustomerRefundsListingState> emit,
  ) async {
    l.d("Fetching Customer Refunds");
    await rpcHandler(
      () async {
        final rpcResult =
            await _customerRefundsRepository.fetchMoneyTransferRefundRequests();
        l.d("Response for CustomerRefundsListing $rpcResult");
        rpcResult.when(
          response: (response) {
            l.d("Response for CustomerRefundsListing: $response");
            _customerRefundsList = response.moneyTransferRefundRequests;
            emit(const CustomerRefundsListingState.dataFetched());
            refreshController.refreshCompleted();
          },
          error: (error) {
            l.d("Error for CustomerRefundsListing: $error");
            refreshController.refreshFailed();
            emit(const CustomerRefundsListingState.prominentError());
            error.when(
              agentDisabledForSpecificInterval: (_) {
                ProminentErrorHandler.agentDisabledForSpecificInterval();
              },
            );
          },
        );
      },
      onTransientError: (_) {
        refreshController.refreshFailed();
        emit(const CustomerRefundsListingState.transientError());
      },
    );
  }
}

@freezed
sealed class CustomerRefundsListingState with _$CustomerRefundsListingState {
  const factory CustomerRefundsListingState.initial() = Initial;

  const factory CustomerRefundsListingState.loading() = Loading;

  const factory CustomerRefundsListingState.dataFetched() = DataFetched;

  const factory CustomerRefundsListingState.refreshInProgress() =
      RefreshInProgress;

  const factory CustomerRefundsListingState.prominentError() = ProminentError;

  const factory CustomerRefundsListingState.transientError() = TransientError;
}

@freezed
sealed class CustomerRefundsListingEvent with _$CustomerRefundsListingEvent {
  const factory CustomerRefundsListingEvent.fetchRequests() = FetchRequests;

  const factory CustomerRefundsListingEvent.pullToRefresh() = PullToRefresh;
}
