import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/repository/customer_refunds_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/transaction_success_screen_arguments.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_success_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../session_pin/enter_session_pin/enter_session_pin_screen.dart';

part 'customer_refunds_otp_bloc.freezed.dart';

class CustomerRefundsOTPBloc
    extends Bloc<CustomerRefundsOTPEvent, CustomerRefundsOTPState>
    with RPCHandler {
  String? _nationalId;
  String? _otp;
  final Coordinate shopCoordinate;
  final LocationService _locationService = locator<LocationService>();
  final _customerRefundsRepository = CustomerRefundsRepository();
  final MoneyTransferRefundRequest response;

  CustomerRefundsOTPBloc({required this.shopCoordinate, required this.response})
    : super(const Initial()) {
    on<CustomerRefundsOTPEvent>((event, emit) async {
      switch (event) {
        case AddNationalId(:final nationalId):
          if (state is Loading) return;
          _nationalId = nationalId;
        case AddOTP(:final otp):
          if (state is Loading) return;
          _otp = otp;
        case RequestOTP(:final context, :final recordId):
          await _requestOTP(context, emit, recordId);
        case ResendOTP(:final context, :final recordId):
          if (state is Loading) return;
          await _resendOTP(context, emit, recordId);
        case AttemptRefund(:final context, :final recordId, :final amount):
          await _attemptRefundRequest(context, emit, recordId, amount);
      }
    });
  }

  Future<void> _requestOTP(
    BuildContext context,
    Emitter<CustomerRefundsOTPState> emit,
    LeoUUID recordId,
  ) async {
    if (!isAgentLocationValid(context, emit)) return;
    final isAgentAuthenticated =
        await context.rootNavigator.pushNamed(EnterSessionPinScreen.id)
            as bool?;
    if (!(isAgentAuthenticated ?? false)) return;
    await rpcHandler(
      () async {
        emit(const CustomerRefundsOTPState.loading());
        AgencyAppDialog.showSpinnerDialog(context);
        final result = await _customerRefundsRepository.requestOTP(recordId);
        if (context.mounted) _handleRequestOTPResult(context, emit, result);
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        emit(const CustomerRefundsOTPState.transientError());
      },
    );
  }

  void _handleRequestOTPResult(
    BuildContext context,
    Emitter<CustomerRefundsOTPState> emit,
    LeoRPCResult<RequestRefundSenderOTPResponse, RequestRefundSenderOTPError>
    result,
  ) {
    result.when(
      response: (response) {
        context.rootNavigator.pop();
        emit(CustomerRefundsOTPState.otpSentSuccessfully(response.otpDetails));
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const CustomerRefundsOTPState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          tooManyOtpRequests: (_) {
            ProminentErrorHandler.tooManyOTPRequests();
          },
          refundRequestBlocked: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context.localizations.refundRequestBlockedErrorMessage,
              onOkay: navigateToHomeScreen,
            );
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
        );
      },
    );
  }

  Future<void> _resendOTP(
    BuildContext context,
    Emitter<CustomerRefundsOTPState> emit,
    LeoUUID recordId,
  ) async {
    if (!isAgentLocationValid(context, emit)) return;
    await rpcHandler(
      () async {
        final result = await _customerRefundsRepository.resendOTP(recordId);
        if (context.mounted) _handleResendOTPResult(context, emit, result);
      },
      onTransientError: (_) {
        emit(const CustomerRefundsOTPState.transientError());
      },
    );
  }

  void _handleResendOTPResult(
    BuildContext context,
    Emitter<CustomerRefundsOTPState> emit,
    LeoRPCResult<ResendRefundSenderOTPResponse, ResendRefundSenderOTPError>
    result,
  ) {
    result.when(
      response: (response) {
        emit(
          CustomerRefundsOTPState.otpResentSuccessfully(response.otpDetails),
        );
      },
      error: (error) {
        emit(const CustomerRefundsOTPState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.refundTitle,
            );
          },
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          waitForResend: (_) {
            ProminentErrorHandler.waitForOTPResend();
          },
          refundRequestBlocked: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context.localizations.refundRequestBlockedErrorMessage,
              onOkay: navigateToHomeScreen,
            );
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          tooManyOtpRequests: (tooManyOtpRequests) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText: context.localizations.tooManyOTPRequests,
              onOkay: navigateToHomeScreen,
            );
          },
        );
      },
    );
  }

  Future<void> _attemptRefundRequest(
    BuildContext context,
    Emitter<CustomerRefundsOTPState> emit,
    LeoUUID recordId,
    Amount amount,
  ) async {
    if (!isAgentLocationValid(context, emit)) return;
    if (context.mounted) {
      AgencyAppDialog.showSpinnerDialog(context);
      await rpcHandler(
        () async {
          emit(const CustomerRefundsOTPState.loading());
          final result = await _customerRefundsRepository.confirmRefund(
            recordId,
            NationalId(id: _nationalId!),
            Otp(otp: _otp!),
          );
          if (context.mounted) {
            _handleConfirmRefundRequestResult(
              context,
              emit,
              result,
              recordId,
              amount,
            );
          }
        },
        onTransientError: (_) {
          context.rootNavigator.pop();
          emit(const CustomerRefundsOTPState.transientError());
        },
      );
    }
  }

  void _handleConfirmRefundRequestResult(
    BuildContext context,
    Emitter<CustomerRefundsOTPState> emit,
    LeoRPCResult<
      AgentRefundMoneyTransferRequestResponse,
      AgentRefundMoneyTransferRequestError
    >
    result,
    LeoUUID recordId,
    Amount amount,
  ) {
    result.when(
      response: (response) {
        emit(const CustomerRefundsOTPState.refundRequestSuccessful());
        context.rootNavigator.pop();
        context.rootNavigator.pushNamed(
          TransactionSuccessScreen.id,
          arguments: TransactionSuccessScreenArguments(
            transactionStatusDetail: response.transactionDetail,
            succeededAt: response.succeededAt,
            amount: amount,
            recordId: recordId.uuid,
          ),
        );
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const CustomerRefundsOTPState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          invalidSenderNationalId: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText:
                  context
                      .localizations
                      .invalidSenderNationalIdDialogContentText,
              buttonText: context.localizations.tryAgain,
            );
          },
          otpExpired: (_) {
            ProminentErrorHandler.otpExpired();
          },
          incorrectOtp: (error) {
            ProminentErrorHandler.incorrectOTP(
              numberOfValidationsAttemptsLeft:
                  error.numberOfValidationAttemptsLeft,
              featureName: context.localizations.refund,
            );
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.refund,
            );
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          refundRequestBlocked: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context.localizations.refundRequestBlockedErrorMessage,
              onOkay: navigateToHomeScreen,
            );
          },
        );
      },
    );
  }

  bool isAgentLocationValid(
    BuildContext context,
    Emitter<CustomerRefundsOTPState> emit,
  ) {
    final LocationDetails locationDetails = shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const CustomerRefundsOTPState.prominentError());
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class CustomerRefundsOTPState with _$CustomerRefundsOTPState {
  const factory CustomerRefundsOTPState.initial() = Initial;

  const factory CustomerRefundsOTPState.loading() = Loading;

  const factory CustomerRefundsOTPState.prominentError() = ProminentError;

  const factory CustomerRefundsOTPState.transientError() = TransientError;

  const factory CustomerRefundsOTPState.otpSentSuccessfully(
    OTPValidityDetails otpValidityDetails,
  ) = OTPSentSuccessfully;

  const factory CustomerRefundsOTPState.otpResentSuccessfully(
    OTPResendDetails otpResendDetails,
  ) = OTPResentSuccessfully;

  const factory CustomerRefundsOTPState.refundRequestSuccessful() =
      RefundRequestSuccessful;
}

@freezed
sealed class CustomerRefundsOTPEvent with _$CustomerRefundsOTPEvent {
  const factory CustomerRefundsOTPEvent.addNationalId(String nationalId) =
      AddNationalId;

  const factory CustomerRefundsOTPEvent.addOtp(String otp) = AddOTP;

  const factory CustomerRefundsOTPEvent.attemptRefund(
    BuildContext context,
    LeoUUID recordId,
    Amount amount,
  ) = AttemptRefund;

  const factory CustomerRefundsOTPEvent.requestOTP(
    BuildContext context,
    LeoUUID recordId,
  ) = RequestOTP;

  const factory CustomerRefundsOTPEvent.resendOTP(
    BuildContext context,
    LeoUUID recordId,
  ) = ResendOTP;
}
