import 'package:agency_banking_rpcs/agency/agent_refund_money_transfer_request_rpc.dart';
import 'package:agency_banking_rpcs/agency/get_money_transfer_refund_requests_rpc.dart';
import 'package:agency_banking_rpcs/agency/request_refund_sender_otp_rpc.dart';
import 'package:agency_banking_rpcs/agency/resend_refund_sender_otp_rpc.dart';
import 'package:agency_banking_rpcs/types/national_id_type.dart';
import 'package:agency_banking_rpcs/types/otp_type.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/mock_impls/mock_agent_refund_money_transfer_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/mock_impls/mock_get_money_transfer_refund_requests.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/mock_impls/mock_request_refund_sender_otp.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/mock_impls/mock_resend_refund_sender_otp.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class CustomerRefundsRepository {
  Future<
    LeoRPCResult<
      GetMoneyTransferRefundRequestsResponse,
      GetMoneyTransferRefundRequestsError
    >
  >
  fetchMoneyTransferRefundRequests() async {
    final request = GetMoneyTransferRefundRequestsRequest();

    final GetMoneyTransferRefundRequestsRPC impl =
        currentFlavor.isMock
            ? MockGetMoneyTransferRefundRequests()
            : GetMoneyTransferRefundRequestsRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<RequestRefundSenderOTPResponse, RequestRefundSenderOTPError>
  >
  requestOTP(LeoUUID recordId) async {
    final request = RequestRefundSenderOTPRequest(recordId: recordId);

    final RequestRefundSenderOTPRPC impl =
        currentFlavor.isMock
            ? MockRequestRefundSenderOTP()
            : RequestRefundSenderOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<ResendRefundSenderOTPResponse, ResendRefundSenderOTPError>
  >
  resendOTP(LeoUUID recordId) async {
    final request = ResendRefundSenderOTPRequest(recordId: recordId);

    final ResendRefundSenderOTPRPC impl =
        currentFlavor.isMock
            ? MockResendRefundSenderOTP()
            : ResendRefundSenderOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      AgentRefundMoneyTransferRequestResponse,
      AgentRefundMoneyTransferRequestError
    >
  >
  confirmRefund(LeoUUID recordId, NationalId senderNationalId, Otp otp) async {
    final request = AgentRefundMoneyTransferRequestRequest(
      recordId: recordId,
      senderNationalId: senderNationalId,
      otp: otp,
    );

    final AgentRefundMoneyTransferRequestRPC impl =
        currentFlavor.isMock
            ? MockAgentRefundMoneyTransferRequest()
            : AgentRefundMoneyTransferRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
