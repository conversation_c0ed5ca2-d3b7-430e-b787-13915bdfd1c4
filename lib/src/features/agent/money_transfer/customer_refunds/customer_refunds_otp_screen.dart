import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/validators.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/otp_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_outlined_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/section_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_details_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'blocs/customer_refunds_otp_bloc.dart';

class CustomerRefundsOTPScreen extends StatefulWidget {
  static const id = '/customer-refunds-otp-screen';

  const CustomerRefundsOTPScreen({Key? key}) : super(key: key);

  @override
  State<CustomerRefundsOTPScreen> createState() =>
      _CustomerRefundsOTPScreenState();
}

class _CustomerRefundsOTPScreenState extends State<CustomerRefundsOTPScreen> {
  late final _customerRefundsOtpBloc = BlocProvider.of<CustomerRefundsOTPBloc>(
    context,
  );
  OTPValidityDetails? _otpValidityDetails;
  bool _isOTPRequested = false;
  final GlobalKey<FormState> _customerRefundsFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateCustomerRefunds = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }
        final NavigatorState navigator = Navigator.of(context);
        if (_isOTPRequested) {
          if (await _askForConfirmation(context) ?? false) {
            navigator.pop();
            return;
          }
        } else {
          navigator.pop();
          return;
        }
      },
      child: BlocBuilder(
        bloc: _customerRefundsOtpBloc,
        builder: (context, state) {
          if (state is OTPSentSuccessfully) {
            _isOTPRequested = true;
            _otpValidityDetails = state.otpValidityDetails;
          }
          return Scaffold(
            appBar: _buildAppBar(context),
            body: _buildBody(context, state),
          );
        },
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      centerTitle: false,
      title: Text(
        context.localizations.senderRequest,
        overflow: TextOverflow.ellipsis,
      ),
      leading: IconButton(
        onPressed: () async {
          if (_isOTPRequested) {
            final bool? shouldGoBack = await _askForConfirmation(context);
            if (shouldGoBack == true && context.mounted) {
              context.rootNavigator.pop();
            }
          } else {
            context.rootNavigator.pop();
          }
        },
        icon: IconWidget(
          iconColor: context.appColors.genericWhiteColor,
          assetName:
              _isOTPRequested ? ABAssets.closeIcon : ABAssets.arrowLeftIcon,
        ),
      ),
    );
  }

  Future<bool?> _askForConfirmation(BuildContext context) async {
    return await AgencyAppDialog.showCancelConfirmation(
      context,
      context.localizations.transaction,
    );
  }

  Widget _buildBody(BuildContext context, state) {
    return Padding(
      padding: const EdgeInsets.only(top: dimenEight),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  _buildRequestInfo(context),
                  _buildTransactionDetailsWidget(context),
                  TitleWidget(title: context.localizations.refundAmount),
                  _buildInfoText(context),
                  if (_isOTPRequested) _buildRefundField(context),
                ],
              ),
            ),
          ),
          _buildCTA(context, state),
        ],
      ),
    );
  }

  Widget _buildRequestInfo(BuildContext context) {
    return SectionWidget(
      title: context.localizations.basicDetails,
      children: [
        InfoLabel(
          title: context.localizations.senderNameLabelText,
          bodyText: _customerRefundsOtpBloc.response.senderName.text,
        ),
        InfoLabel(
          title: context.localizations.requestIdHeader,
          bodyText: _customerRefundsOtpBloc.response.requestId.id,
        ),
        InfoLabel(
          title: context.localizations.recipientNationalId,
          bodyText: _customerRefundsOtpBloc.response.recipientNationalId.id,
        ),
      ],
    );
  }

  TransactionDetailsWidget _buildTransactionDetailsWidget(
    BuildContext context,
  ) {
    return TransactionDetailsWidget(
      transactionAmountLabel: context.localizations.amountTransferred,
      transactionAmount: _customerRefundsOtpBloc.response.amount,
      transactionFee: _customerRefundsOtpBloc.response.transactionFee,
      receivingAmountLabel: context.localizations.senderRefundAmount,
      receivingAmount: _customerRefundsOtpBloc.response.senderRefundAmount,
    );
  }

  Widget _buildInfoText(BuildContext context) {
    return Padding(
      padding: commonScreenPadding,
      child: Text(
        context.localizations.customerRefundsInfoText,
        style: context.appTextStyles.smallText1.copyWith(
          color: context.appColors.neutralShadeDefaultColor,
        ),
      ),
    );
  }

  Widget _buildRefundField(BuildContext context) {
    return Form(
      key: _customerRefundsFormKey,
      autovalidateMode: _shouldValidateCustomerRefunds,
      child: Padding(
        padding: horizontalPaddingSixteen,
        child: Column(
          children: [
            PrimaryTextField(
              inputFormatters: [nationalIdFormatter],
              maxLength: nationalIdMaxLength,
              labelText: context.localizations.senderNationalID,
              onChanged: (value) {
                _customerRefundsOtpBloc.add(
                  CustomerRefundsOTPEvent.addNationalId(value),
                );
              },
              validator:
                  (nationalId) => Validators.emptyValidator(
                    context,
                    context.localizations.senderNationalID,
                    nationalId,
                  ),
            ),
            verticalGapSixteen,
            OTPWidget(
              otpValidityDetails: _otpValidityDetails!,
              onChanged: (otp) {
                _customerRefundsOtpBloc.add(
                  CustomerRefundsOTPEvent.addOtp(otp),
                );
              },
              onResend: () async {
                _customerRefundsOtpBloc.add(
                  CustomerRefundsOTPEvent.resendOTP(
                    context,
                    _customerRefundsOtpBloc.response.recordId,
                  ),
                );
                final latestState =
                    await _customerRefundsOtpBloc.stream.firstOrNull;
                return switch (latestState) {
                  OTPResentSuccessfully(:final otpResendDetails) =>
                    otpResendDetails,
                  _ => null,
                };
              },
            ),
            verticalGapSixteen,
          ],
        ),
      ),
    );
  }

  Widget _buildCTA(BuildContext context, CustomerRefundsOTPState state) {
    return Padding(
      padding: commonScreenPadding,
      child: Column(
        children: [
          _isOTPRequested
              ? PrimaryOutlinedButton(
                labelText: context.localizations.refundAmount,
                onPressed: () {
                  dismissKeyboard();
                  final isFormValidated =
                      (_customerRefundsFormKey.currentState?.validate() ??
                          false);
                  if (!isFormValidated) {
                    setState(() {
                      _shouldValidateCustomerRefunds =
                          AutovalidateMode.onUserInteraction;
                    });
                    return;
                  }
                  _customerRefundsOtpBloc.add(
                    CustomerRefundsOTPEvent.attemptRefund(
                      context,
                      _customerRefundsOtpBloc.response.recordId,
                      _customerRefundsOtpBloc.response.senderRefundAmount,
                    ),
                  );
                },
              )
              : Column(
                children: [
                  PrimaryOutlinedButton(
                    isEnabled:
                        !_customerRefundsOtpBloc.response.isTransactionBlocked,
                    labelText: context.localizations.requestOTP,
                    onPressed:
                        () => _customerRefundsOtpBloc.add(
                          CustomerRefundsOTPEvent.requestOTP(
                            context,
                            _customerRefundsOtpBloc.response.recordId,
                          ),
                        ),
                  ),
                  if (_customerRefundsOtpBloc
                      .response
                      .isTransactionBlocked) ...[
                    verticalGapEight,
                    Text(
                      context
                          .localizations
                          .customerRefundTransactionBlockedError,
                      style: context.appTextStyles.smallText1.copyWith(
                        color: context.appColors.neutralShade7Color,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
        ],
      ),
    );
  }
}

class CustomerRefundsOTPScreenArguments {
  final Coordinate shopCoordinate;
  final MoneyTransferRefundRequest moneyTransferRefundRequest;

  CustomerRefundsOTPScreenArguments({
    required this.shopCoordinate,
    required this.moneyTransferRefundRequest,
  });
}
