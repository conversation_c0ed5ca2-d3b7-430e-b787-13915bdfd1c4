import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/blocs/accept_request_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/validators.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AcceptRequestLandingScreen extends StatefulWidget {
  static const id = '/accept-request-landing-screen';

  const AcceptRequestLandingScreen({Key? key}) : super(key: key);

  @override
  State<AcceptRequestLandingScreen> createState() =>
      _AcceptRequestLandingScreenState();
}

class _AcceptRequestLandingScreenState
    extends State<AcceptRequestLandingScreen> {
  late final _acceptRequestBloc = BlocProvider.of<AcceptRequestBloc>(context);
  final GlobalKey<FormState> _acceptRequestFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldAutoValidateAcceptRequest = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return CommonScreenLayout(
      disableBackGesture: false,
      appBarTitle: context.localizations.acceptRequest,
      ctaWidget: _buildCTA(context),
      child: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Form(
      key: _acceptRequestFormKey,
      autovalidateMode: _shouldAutoValidateAcceptRequest,
      child: Column(
        children: <Widget>[
          verticalGapTwentyFour,
          PrimaryTextField(
            inputFormatters: [nationalIdFormatter],
            maxLength: nationalIdMaxLength,
            labelText: context.localizations.recipientNationalId,
            validator:
                (nationalId) => Validators.emptyValidator(
                  context,
                  context.localizations.recipientNationalId,
                  nationalId,
                ),
            onChanged: (nationalId) {
              _acceptRequestBloc.add(
                AcceptRequestEvent.addNationalId(nationalId),
              );
            },
          ),
          verticalGapSixteen,
          PrimaryTextField(
            maxLength: requestIDMaxLength,
            inputFormatters: [requestIdFormatter],
            labelText: context.localizations.requestIdHeader,
            validator:
                (requestId) => Validators.emptyValidator(
                  context,
                  context.localizations.requestIdHeader,
                  requestId,
                ),
            onChanged: (requestId) {
              _acceptRequestBloc.add(
                AcceptRequestEvent.addRequestId(requestId),
              );
            },
          ),
          verticalGapThirtySix,
        ],
      ),
    );
  }

  Widget _buildCTA(BuildContext context) {
    return BlocBuilder<AcceptRequestBloc, AcceptRequestState>(
      bloc: _acceptRequestBloc,
      builder: (context, state) {
        return PrimaryButton(
          labelText: context.localizations.continueText,
          onPressed: () {
            final isFormValidated =
                _acceptRequestFormKey.currentState?.validate() ?? false;
            if (!isFormValidated) {
              setState(() {
                _shouldAutoValidateAcceptRequest =
                    AutovalidateMode.onUserInteraction;
              });
              return;
            }
            _acceptRequestBloc.add(
              AcceptRequestEvent.createMoneyTransferAcceptRequest(context),
            );
          },
        );
      },
    );
  }
}
