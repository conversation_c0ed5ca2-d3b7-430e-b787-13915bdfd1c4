import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/blocs/accept_request_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/repositories/accept_request_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/transaction_success_screen_arguments.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_success_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';
import 'package:leo_dart_runtime/leo_uuid.dart';

import '../../../../../core/logger.dart';

part 'accept_request_otp_bloc.freezed.dart';

class AcceptRequestOTPBloc
    extends Bloc<AcceptRequestOTPEvent, AcceptRequestOTPState>
    with RPCHandler {
  String? _otp;
  final Coordinate shopCoordinate;
  final AcceptRequestRepository _acceptRequestRepository =
      AcceptRequestRepository();
  final LocationService _locationService = locator<LocationService>();

  AcceptRequestOTPBloc({required this.shopCoordinate})
    : super(const AcceptRequestOTPState.initial()) {
    on<AcceptRequestOTPEvent>((event, emit) async {
      switch (event) {
        case RequestOTP(:final context, :final recordId):
          await _requestOTP(context, emit, recordId);
        case AddOTP(:final otp):
          if (state is OTPLoading) return;
          _otp = otp;
        case AttemptTransfer(:final context, :final recordId, :final amount):
          await _attemptAcceptRequest(context, emit, recordId, amount);
        case ResendOTP(:final context, :final recordId):
          if (state is OTPLoading) return;
          await _resendOTP(context, emit, recordId);
      }
    });
  }

  Future<void> _requestOTP(
    BuildContext context,
    Emitter<AcceptRequestOTPState> emit,
    LeoUUID recordId,
  ) async {
    l.d("Requesting OTP for Accept request for record ID: $recordId");
    final LocationDetails locationDetails = shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const AcceptRequestOTPState.prominentError());
      return;
    }
    await rpcHandler(
      () async {
        emit(const AcceptRequestOTPState.loading());
        AgencyAppDialog.showSpinnerDialog(context);
        final result = await _acceptRequestRepository.requestOTP(recordId);
        l.d("Accept Request Request OTP: $result");
        if (context.mounted) {
          _handlerRequestOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        emit(const AcceptRequestOTPState.transientError());
      },
    );
  }

  void _handlerRequestOTPResult(
    BuildContext context,
    Emitter<AcceptRequestOTPState> emit,
    LeoRPCResult<
      RequestMoneyTransferRecipientOTPResponse,
      RequestMoneyTransferRecipientOTPError
    >
    result,
  ) {
    l.d("Handling Accept Request Request OTP result");
    result.when(
      response: (response) {
        l.d("Response for Accept Request Request OTP: $response");
        context.rootNavigator.pop();
        emit(AcceptRequestOTPState.otpSentSuccessfully(response.otpDetails));
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const AcceptRequestOTPState.prominentError());
        l.d("Error for Accept Request Request OTP: $error");
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.otpExpired,
            );
          },
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          tooManyOtpRequests: (_) {
            ProminentErrorHandler.tooManyOTPRequests();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          refundFlowWasInitiated:
              (_) => {
                AgencyAppDialog.showErrorDialog(
                  context: context,
                  isDismissible: false,
                  contentText:
                      context.localizations.refundFlowWasInitiatedErrorMessage,
                  onOkay: navigateToHomeScreen,
                ),
              },
          sendingOtpBlockedTemporarily:
              (_) => {
                AgencyAppDialog.showErrorDialog(
                  context: context,
                  isDismissible: false,
                  contentText:
                      context
                          .localizations
                          .sendingOTPBlockedTemporarilyErrorMsg,
                ),
              },
        );
      },
    );
  }

  Future<void> _attemptAcceptRequest(
    BuildContext context,
    Emitter<AcceptRequestOTPState> emit,
    LeoUUID recordId,
    Amount amount,
  ) async {
    l.d('''Attempting Accept Request. 
    recordId: $recordId,
    amount: $amount
    otp: $_otp''');
    if (!_isAgentLocationValid(context, emit)) return;
    if (context.mounted) {
      AgencyAppDialog.showSpinnerDialog(context);
      emit(const AcceptRequestOTPState.loading());
      await rpcHandler(
        () async {
          final result = await _acceptRequestRepository
              .confirmMoneyTransferAcceptRequest(
                otp: Otp(otp: _otp!),
                recordId: recordId,
              );
          l.d("Confirm MT Accept Request Result: $result");
          if (context.mounted) {
            _handleConfirmMoneyTransferAcceptRequestResult(
              context,
              emit,
              result,
              recordId,
              amount,
            );
          }
        },
        onTransientError: (_) {
          context.rootNavigator.pop();
          emit(const AcceptRequestOTPState.transientError());
        },
      );
    }
  }

  void _handleConfirmMoneyTransferAcceptRequestResult(
    BuildContext context,
    Emitter<AcceptRequestOTPState> emit,
    LeoRPCResult<
      ConfirmMoneyTransferAcceptRequestResponse,
      ConfirmMoneyTransferAcceptRequestError
    >
    result,
    LeoUUID recordId,
    Amount amount,
  ) {
    l.v("Handling Confirm MT Accept request result");
    result.when(
      response: (response) {
        emit(
          const AcceptRequestOTPState.moneyTransferAcceptRequestSuccessful(),
        );
        l.d("Confirm MT Accept request response: $response");
        context.rootNavigator.pop();
        context.rootNavigator.pushNamed(
          TransactionSuccessScreen.id,
          arguments: TransactionSuccessScreenArguments(
            transactionStatusDetail: response.transactionDetail,
            succeededAt: response.succeededAt,
            amount: amount,
            recordId: recordId.uuid,
          ),
        );
      },
      error: (error) {
        l.d("Confirm MT Accept request error: $error");
        context.rootNavigator.pop();
        emit(const AcceptRequestOTPState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          otpExpired: (_) {
            ProminentErrorHandler.otpExpired();
          },
          incorrectOtp: (error) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText:
                  error.numberOfValidationAttemptsLeft != 0
                      ? context.localizations.incorrectOTP
                      : context
                          .localizations
                          .acceptRequestLastAttemptErrorMessage,
              isDismissible: error.numberOfValidationAttemptsLeft != 0,
              buttonText: context.localizations.tryAgain.toUpperCase(),
            );
          },
          refundFlowWasInitiated: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context.localizations.refundFlowWasInitiatedErrorMessage,
              onOkay: navigateToHomeScreen,
            );
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          confirmationTimeout: (_) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.moneyTransfer,
            );
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
        );
      },
    );
  }

  Future<void> _resendOTP(
    BuildContext context,
    Emitter<AcceptRequestOTPState> emit,
    LeoUUID recordId,
  ) async {
    l.d("Resending OTP. Record ID: $recordId");
    if (!_isAgentLocationValid(context, emit)) return;
    await rpcHandler(
      () async {
        final result = await _acceptRequestRepository.resendOTP(recordId);
        l.d("Resend OTP result: $result");
        if (context.mounted) {
          _handleResendOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        l.d("Emitting Transient error");
        emit(const AcceptRequestOTPState.transientError());
      },
    );
  }

  void _handleResendOTPResult(
    BuildContext context,
    Emitter<AcceptRequestOTPState> emit,
    LeoRPCResult<
      ResendMoneyTransferRecipientOTPResponse,
      ResendMoneyTransferRecipientOTPError
    >
    result,
  ) {
    l.v("Handling Resend OTP Result");
    result.when(
      response: (response) {
        l.d("Resend OTP Response: $response");
        emit(AcceptRequestOTPState.otpResentSuccessfully(response.otpDetails));
      },
      error: (error) {
        l.d("Resend OTP error: $error");
        emit(const AcceptRequestOTPState.prominentError());
        error.when(
          invalidRecordId: (e) => throw DeveloperError(e.toString()),
          confirmationTimeout: (error) {
            ProminentErrorHandler.confirmationTimeout(
              context.localizations.moneyTransfer,
            );
          },
          couldNotSendOtp: (_) {
            ProminentErrorHandler.couldNotSendOTP();
          },
          waitForResend: (_) {
            ProminentErrorHandler.waitForOTPResend();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          refundFlowWasInitiated: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              isDismissible: false,
              contentText:
                  context.localizations.refundFlowWasInitiatedErrorMessage,
              onOkay: navigateToHomeScreen,
            );
          },
          tooManyResendRequests: (_) {
            ProminentErrorHandler.tooManyResendRequests();
          },
        );
      },
    );
  }

  bool _isAgentLocationValid(
    BuildContext context,
    Emitter<AcceptRequestOTPState> emit,
  ) {
    final acceptRequestBloc = BlocProvider.of<AcceptRequestBloc>(context);
    final LocationDetails locationDetails =
        acceptRequestBloc.shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const AcceptRequestOTPState.prominentError());
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class AcceptRequestOTPState with _$AcceptRequestOTPState {
  const factory AcceptRequestOTPState.initial() = Initial;

  const factory AcceptRequestOTPState.loading() = OTPLoading;

  const factory AcceptRequestOTPState.prominentError() = ProminentError;

  const factory AcceptRequestOTPState.transientError() = TransientError;

  const factory AcceptRequestOTPState.invalidOTP() = InvalidOTP;

  const factory AcceptRequestOTPState.otpSentSuccessfully(
    OTPValidityDetails otpValidityDetails,
  ) = OTPSentSuccessfully;

  const factory AcceptRequestOTPState.otpResentSuccessfully(
    OTPResendDetails resendDetails,
  ) = OTPResentSuccessfully;

  const factory AcceptRequestOTPState.moneyTransferAcceptRequestSuccessful() =
      MoneyTransferAcceptRequestSuccessful;
}

@freezed
sealed class AcceptRequestOTPEvent with _$AcceptRequestOTPEvent {
  const factory AcceptRequestOTPEvent.addOtp(String otp) = AddOTP;

  const factory AcceptRequestOTPEvent.attemptTransfer(
    BuildContext context,
    LeoUUID recordId,
    Amount amount,
  ) = AttemptTransfer;

  const factory AcceptRequestOTPEvent.resendOTP(
    BuildContext context,
    LeoUUID recordId,
  ) = ResendOTP;

  const factory AcceptRequestOTPEvent.requestOTP(
    BuildContext context,
    LeoUUID recordId,
  ) = RequestOTP;
}
