import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/accept_request_otp_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/repositories/accept_request_repository.dart';
import 'package:bcn_agency_banking_flutter/src/features/session_pin/enter_session_pin/enter_session_pin_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

import '../../../../../core/logger.dart';

part 'accept_request_bloc.freezed.dart';

class AcceptRequestBloc extends Bloc<AcceptRequestEvent, AcceptRequestState>
    with RPCHandler {
  String? _nationalId;
  String? _requestId;
  Amount? _amount;
  final Coordinate shopCoordinate;
  final Currency defaultCurrency;
  final AcceptRequestRepository _acceptRequestRepository =
      AcceptRequestRepository();
  CreateMoneyTransferAcceptRequestResponse? _createMoneyTransferRequestResponse;
  final _locationService = locator<LocationService>();

  AcceptRequestBloc({
    required this.shopCoordinate,
    required this.defaultCurrency,
  }) : super(const AcceptRequestState.initial()) {
    on<AcceptRequestEvent>((event, emit) async {
      switch (event) {
        case AddNationalId(:final nationalId):
          if (state is Loading) return;
          _nationalId = nationalId;
        case AddRequestId(:final requestId):
          if (state is Loading) return;
          _requestId = requestId;
        case CreateMoneyTransferAcceptRequest(:final context):
          await _createMoneyTransferAcceptRequest(emit, context);
      }
    });
  }

  String? get nationalId => _nationalId;

  String? get requestId => _requestId;

  Amount? get amount => _amount;

  CreateMoneyTransferAcceptRequestResponse
  get createMoneyTransferAcceptRequestResponse =>
      _createMoneyTransferRequestResponse!;

  Future<void> _createMoneyTransferAcceptRequest(
    Emitter<AcceptRequestState> emit,
    BuildContext context,
  ) async {
    l.d('''Create Money transfer accept request:
    nationalId: $nationalId,
    requestId: $requestId,
    amount: $amount''');
    final LocationDetails locationDetails = shopCoordinate.locationDetails;
    final bool isAgentLocationValid = _locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid) {
      AgencyAppDialog.showAgentLocationInvalidDialog(context, locationDetails);
      emit(const AcceptRequestState.prominentError());
      return;
    }
    final isAgentAuthenticated =
        await context.rootNavigator.pushNamed(EnterSessionPinScreen.id)
            as bool?;
    if (!(isAgentAuthenticated ?? false)) return;
    await rpcHandler(
      () async {
        emit(const AcceptRequestState.loading());
        _showSpinner(context);
        final rpcResult = await _acceptRequestRepository
            .createMoneyTransferAcceptRequest(
              NationalId(id: _nationalId!),
              ABRequestId(id: _requestId!),
            );
        l.d("Create MT accept request result: $rpcResult");
        if (context.mounted) {
          await _handleCreateMoneyTransferAcceptRequestRPCResult(
            context,
            rpcResult,
            emit,
          );
        }
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        emit(const AcceptRequestState.transientError());
      },
    );
  }

  Future<void> _handleCreateMoneyTransferAcceptRequestRPCResult(
    BuildContext context,
    LeoRPCResult<
      CreateMoneyTransferAcceptRequestResponse,
      CreateMoneyTransferAcceptRequestError
    >
    rpcResult,
    Emitter<AcceptRequestState> emit,
  ) async {
    l.v("Handling Create MT AcceptRequest RPC result");
    await rpcResult.when(
      response: (response) async {
        l.d("Create MT AcceptRequest RPC response: $response");
        if (response.receivingAmount != null) {
          ProminentErrorHandler.exchangeRateNotSupported();
        }
        emit(const AcceptRequestState.createdMoneyTransferAcceptRequest());
        _createMoneyTransferRequestResponse = response;
        _amount = response.amount;
        if (_createMoneyTransferRequestResponse?.receivingAmount != null) {
          throw DeveloperError("Multi currency is not supported yet.");
        }
        if (!context.mounted) return;
        context.rootNavigator.pop();
        context.navigator.pushNamed(
          AcceptRequestOTPScreen.id,
          arguments: AcceptRequestOTPScreenArguments(this, shopCoordinate),
        );
      },
      error: (error) {
        l.d("Create MT AcceptRequest RPC error: $error");
        context.rootNavigator.pop();
        emit(const AcceptRequestState.prominentError());
        error.when(
          invalidRequestIdOrNationalId: (_) {
            AgencyAppDialog.showErrorDialog(
              context: context,
              contentText: context.localizations.incorrectNationalOrRequestId,
              buttonText: context.localizations.tryAgain,
            );
          },
          agentPeriodicTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentPeriodicTransactionLimitExceeded();
          },
          agentMonetaryTransactionLimitExceeded: (_) {
            ProminentErrorHandler.agentMonetaryTransactionLimitExceeded();
          },
          receivingAccountWouldCrossLimit: (_) {
            ProminentErrorHandler.receivingAccountWouldCrossLimit();
          },
          unableToPerformExchange: (_) {
            ProminentErrorHandler.exchangeRateNotSupported();
          },
          agentDisabledForSpecificInterval: (_) {
            ProminentErrorHandler.agentDisabledForSpecificInterval();
          },
          refundFlowWasInitiated:
              (_) => {
                AgencyAppDialog.showErrorDialog(
                  context: context,
                  contentText:
                      context.localizations.refundFlowWasInitiatedErrorMessage,
                  onOkay: navigateToHomeScreen,
                ),
              },
        );
      },
    );
  }

  void _showSpinner(BuildContext context) {
    AgencyAppDialog.showSpinnerDialog(context);
  }
}

@freezed
sealed class AcceptRequestState with _$AcceptRequestState {
  const factory AcceptRequestState.initial() = Initial;

  const factory AcceptRequestState.loading() = Loading;

  const factory AcceptRequestState.prominentError() = ProminentError;

  const factory AcceptRequestState.transientError() = TransientError;

  const factory AcceptRequestState.createdMoneyTransferAcceptRequest() =
      CreatedMoneyTransferAcceptRequest;
}

@freezed
sealed class AcceptRequestEvent with _$AcceptRequestEvent {
  const factory AcceptRequestEvent.addNationalId(String nationalId) =
      AddNationalId;

  const factory AcceptRequestEvent.addRequestId(String requestId) =
      AddRequestId;

  const factory AcceptRequestEvent.createMoneyTransferAcceptRequest(
    BuildContext context,
  ) = CreateMoneyTransferAcceptRequest;
}
