import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/blocs/accept_request_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/blocs/accept_request_otp_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/common_screen_layout.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/otp_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/section_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AcceptRequestOTPScreen extends StatefulWidget {
  static const id = "/accept-request-otp-screen";

  const AcceptRequestOTPScreen({Key? key}) : super(key: key);

  @override
  State<AcceptRequestOTPScreen> createState() => _AcceptRequestOTPScreenState();
}

class _AcceptRequestOTPScreenState extends State<AcceptRequestOTPScreen> {
  late final _acceptRequestBloc = BlocProvider.of<AcceptRequestBloc>(context);
  late final _acceptRequestOtpBloc = BlocProvider.of<AcceptRequestOTPBloc>(
    context,
  );
  late final _userName =
      _acceptRequestBloc.createMoneyTransferAcceptRequestResponse.senderName;
  late final _response =
      _acceptRequestBloc.createMoneyTransferAcceptRequestResponse;
  bool _isOTPRequested = false;
  OTPValidityDetails? _otpValidityDetails;
  final _acceptRequestFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldAutoValidateOTP = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return BlocListener<AcceptRequestOTPBloc, AcceptRequestOTPState>(
      listener: (context, state) {
        if (state is OTPSentSuccessfully) {
          _isOTPRequested = true;
          _otpValidityDetails = state.otpValidityDetails;
        }
      },
      child: CommonScreenLayout(
        isCancellable: true,
        appBarTitle: context.localizations.acceptRequest,
        padding: EdgeInsets.zero,
        ctaWidget: BlocBuilder<AcceptRequestOTPBloc, AcceptRequestOTPState>(
          builder: (context, state) => _buildCTAButton(state, context),
        ),
        child: BlocBuilder<AcceptRequestOTPBloc, AcceptRequestOTPState>(
          builder: (context, state) => _buildBody(state, context),
        ),
      ),
    );
  }

  Widget _buildBody(AcceptRequestOTPState state, BuildContext context) {
    return Column(
      children: [
        verticalGapEight,
        _buildSection(context),
        if (_isOTPRequested) ...{
          TitleWidget(title: context.localizations.otpVerificationString),
          verticalGapSixteen,
          _buildOTPWidget(state),
        },
        verticalGapThirtySix,
      ],
    );
  }

  Widget _buildSection(BuildContext context) {
    return SectionWidget(
      title: context.localizations.basicDetailsString,
      children: <InfoLabel>[
        InfoLabel(
          title: context.localizations.senderNameLabelText,
          bodyText: _userName.text,
        ),
        InfoLabel(
          title: context.localizations.recipientReceives,
          bodyText: _acceptRequestBloc.amount!.localisedFormattedAmount,
        ),
      ],
    );
  }

  Widget _buildCTAButton(AcceptRequestOTPState state, BuildContext context) {
    return _isOTPRequested
        ? _buildAttemptTransferCTAButton(state, context)
        : _buildRequestOTPCTAButton(state, context);
  }

  Widget _buildRequestOTPCTAButton(
    AcceptRequestOTPState state,
    BuildContext context,
  ) {
    return PrimaryButton(
      labelText: context.localizations.requestOTP,
      onPressed:
          () => _acceptRequestOtpBloc.add(
            AcceptRequestOTPEvent.requestOTP(context, _response.recordId),
          ),
    );
  }

  Widget _buildAttemptTransferCTAButton(
    AcceptRequestOTPState state,
    BuildContext context,
  ) {
    return PrimaryButton(
      labelText: context.localizations.attemptTransfer,
      onPressed: () {
        final isFormValidated =
            _acceptRequestFormKey.currentState?.validate() ?? false;
        if (!isFormValidated) {
          setState(() {
            _shouldAutoValidateOTP = AutovalidateMode.onUserInteraction;
          });
          return;
        }
        _acceptRequestOtpBloc.add(
          AcceptRequestOTPEvent.attemptTransfer(
            context,
            _response.recordId,
            _response.receivingAmount ?? _response.amount,
          ),
        );
      },
    );
  }

  Widget _buildOTPWidget(AcceptRequestOTPState state) {
    return Padding(
      padding: horizontalPaddingSixteen,
      child: Form(
        key: _acceptRequestFormKey,
        autovalidateMode: _shouldAutoValidateOTP,
        child: OTPWidget(
          otpValidityDetails: _otpValidityDetails!,
          onChanged: (otp) {
            _acceptRequestOtpBloc.add(AcceptRequestOTPEvent.addOtp(otp));
          },
          onResend: () async {
            _acceptRequestOtpBloc.add(
              AcceptRequestOTPEvent.resendOTP(
                context,
                _acceptRequestBloc
                    .createMoneyTransferAcceptRequestResponse
                    .recordId,
              ),
            );
            final latestState = await _acceptRequestOtpBloc.stream.firstOrNull;
            return switch (latestState) {
              OTPResentSuccessfully(:final resendDetails) => resendDetails,
              _ => null,
            };
          },
        ),
      ),
    );
  }
}

class AcceptRequestOTPScreenArguments {
  AcceptRequestOTPScreenArguments(this.acceptRequestBloc, this.shopCoordinate);

  final AcceptRequestBloc acceptRequestBloc;
  final Coordinate shopCoordinate;
}
