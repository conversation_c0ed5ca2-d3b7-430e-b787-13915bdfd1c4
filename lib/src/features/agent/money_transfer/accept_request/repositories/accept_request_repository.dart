import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/mock_impls/mock_confirm_money_transfer_accept_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/mock_impls/mock_create_money_transfer_accept_request.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/mock_impls/mock_request_money_transfer_recipient_otp.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/mock_impls/mock_resend_money_transfer_recipient_otp.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class AcceptRequestRepository {
  Future<
    LeoRPCResult<
      CreateMoneyTransferAcceptRequestResponse,
      CreateMoneyTransferAcceptRequestError
    >
  >
  createMoneyTransferAcceptRequest(
    NationalId nationalId,
    ABRequestId requestId,
  ) async {
    final request = CreateMoneyTransferAcceptRequestRequest(
      recipientNationalId: nationalId,
      requestId: requestId,
    );
    final CreateMoneyTransferAcceptRequestRPC impl =
        currentFlavor.isMock
            ? MockCreateMoneyTransferAcceptRequest()
            : CreateMoneyTransferAcceptRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      RequestMoneyTransferRecipientOTPResponse,
      RequestMoneyTransferRecipientOTPError
    >
  >
  requestOTP(LeoUUID recordId) async {
    final request = RequestMoneyTransferRecipientOTPRequest(recordId: recordId);
    final RequestMoneyTransferRecipientOTPRPC impl =
        currentFlavor.isMock
            ? MockRequestMoneyTransferRecipientOTP()
            : RequestMoneyTransferRecipientOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      ResendMoneyTransferRecipientOTPResponse,
      ResendMoneyTransferRecipientOTPError
    >
  >
  resendOTP(LeoUUID recordId) async {
    final request = ResendMoneyTransferRecipientOTPRequest(recordId: recordId);
    final ResendMoneyTransferRecipientOTPRPC impl =
        currentFlavor.isMock
            ? MockResendMoneyTransferRecipientOTP()
            : ResendMoneyTransferRecipientOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }

  Future<
    LeoRPCResult<
      ConfirmMoneyTransferAcceptRequestResponse,
      ConfirmMoneyTransferAcceptRequestError
    >
  >
  confirmMoneyTransferAcceptRequest({
    required Otp otp,
    required LeoUUID recordId,
  }) async {
    final request = ConfirmMoneyTransferAcceptRequestRequest(
      recordId: recordId,
      otp: otp,
    );
    final ConfirmMoneyTransferAcceptRequestRPC impl =
        currentFlavor.isMock
            ? MockConfirmMoneyTransferAcceptRequest()
            : ConfirmMoneyTransferAcceptRequestRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    return await impl.execute(request);
  }
}
