import 'package:agency_banking_rpcs/agency/resend_money_transfer_recipient_otp_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockResendMoneyTransferRecipientOTP
    extends ResendMoneyTransferRecipientOTPRPC {
  @override
  Future<
    LeoRPCResult<
      ResendMoneyTransferRecipientOTPResponse,
      ResendMoneyTransferRecipientOTPError
    >
  >
  execute(ResendMoneyTransferRecipientOTPRequest request) {
    final response = ResendMoneyTransferRecipientOTPResponse(
      otpDetails: OTPConstants.resendDetails,
    );

    final error =
        ResendMoneyTransferRecipientOTPError.ResendMoneyTransferRecipientOTPErrorAgentDisabledForSpecificIntervalError(
          errorCode: "123",
        );

    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
