import 'package:agency_banking_rpcs/agency/request_money_transfer_recipient_otp_rpc.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockRequestMoneyTransferRecipientOTP
    extends RequestMoneyTransferRecipientOTPRPC {
  @override
  Future<
    LeoRPCResult<
      RequestMoneyTransferRecipientOTPResponse,
      RequestMoneyTransferRecipientOTPError
    >
  >
  execute(RequestMoneyTransferRecipientOTPRequest request) {
    final response = RequestMoneyTransferRecipientOTPResponse(
      otpDetails: OTPConstants.otpValidityDetails,
    );
    final error =
        RequestMoneyTransferRecipientOTPError.RequestMoneyTransferRecipientOTPErrorConfirmationTimeoutError(
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return Future.delayed(1.seconds, () => result);
  }
}
