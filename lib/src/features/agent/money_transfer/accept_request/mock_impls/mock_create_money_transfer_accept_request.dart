import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockCreateMoneyTransferAcceptRequest
    extends CreateMoneyTransferAcceptRequestRPC {
  @override
  Future<
    LeoRPCResult<
      CreateMoneyTransferAcceptRequestResponse,
      CreateMoneyTransferAcceptRequestError
    >
  >
  execute(CreateMoneyTransferAcceptRequestRequest request) async {
    final response = CreateMoneyTransferAcceptRequestResponse(
      recordId: LeoUUID(null),
      senderName: ABUserName(text: 'Zikomo Malawa'),
      amount: Amount(amount: ********, currency: Currency(currencyCode: "MWK")),
      confirmationExpiringAt: DateTime.now().add(10.minutes),
    );

    final error =
        CreateMoneyTransferAcceptRequestError.CreateMoneyTransferAcceptRequestErrorInvalidRequestIdOrNationalIdError(
          errorCode: "123",
        );
    final result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );
    return await Future.delayed(1.seconds, () => result);
  }
}
