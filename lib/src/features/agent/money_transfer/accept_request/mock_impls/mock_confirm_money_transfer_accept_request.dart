import 'package:agency_banking_rpcs/agency/confirm_money_transfer_accept_request_rpc.dart';
import 'package:agency_banking_rpcs/assets/localized_image_type.dart';
import 'package:agency_banking_rpcs/assets/localized_text_type.dart';
import 'package:agency_banking_rpcs/assets/themed_image_type.dart';
import 'package:agency_banking_rpcs/types/transaction_status_detail_type.dart';
import 'package:agency_banking_rpcs/types/transaction_status_item_detail_type.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockConfirmMoneyTransferAcceptRequest
    extends ConfirmMoneyTransferAcceptRequestRPC {
  @override
  Future<
    LeoRPCResult<
      ConfirmMoneyTransferAcceptRequestResponse,
      ConfirmMoneyTransferAcceptRequestError
    >
  >
  execute(ConfirmMoneyTransferAcceptRequestRequest request) {
    final response = ConfirmMoneyTransferAcceptRequestResponse(
      succeededAt: DateTime.now(),
      transactionDetail: TransactionStatusDetail(
        description: LocalizedText(en: 'Fee charged for customer'),
        itemDetail: [
          TransactionStatusItemDetail(
            label: LocalizedText(en: "Debited From"),
            valueType:
                TransactionStatusItemDetailValueTypeEnum.LARGE_ICON_WITH_TEXT(
                  title: "Zikomo Malawa",
                  description: '+91 **********',
                  image: LocalizedImage(
                    en: ThemedImage(dark: mockImage, light: mockImage),
                  ),
                ),
          ),
        ],
      ),
    );

    final error =
        ConfirmMoneyTransferAcceptRequestError.ConfirmMoneyTransferAcceptRequestErrorIncorrectOtpError(
          errorCode: "123",
          numberOfValidationAttemptsLeft: 0,
        );
    final result = getLeoRPCResult(
      shouldThrowError: !checkWhetherOTPValid(request.otp),
      response: response,
      error: error,
    );
    return Future.delayed(2.seconds, () => result);
  }
}
