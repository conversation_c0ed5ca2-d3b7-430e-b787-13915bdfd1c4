import 'dart:io';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/mock_impls/mock_agent_confirm_sign_in_otp_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/mock_impls/mock_agent_resend_sign_in_otp_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/mock_impls/mock_request_sign_in_otp_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/mock_impls/mock_sign_in_agent_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AgentSignInRepository {
  Future<
    LeoRPCResult<AgencyRequestSignInOTPResponse, AgencyRequestSignInOTPError>
  >
  requestSignInOTP(LeoPhoneNumber phoneNumber) async {
    final AgencyRequestSignInOTPRPC requestSignInOTPRPCImpl =
        currentFlavor.isMock
            ? MockRequestSignInOTPRPCImpl()
            : AgencyRequestSignInOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final AgencyRequestSignInOTPRequest agencyRequestSignInOTPRequest =
        AgencyRequestSignInOTPRequest(phoneNumber: phoneNumber);
    final LeoRPCResult<
      AgencyRequestSignInOTPResponse,
      AgencyRequestSignInOTPError
    >
    agencyRequestSignInOTPResponse = await requestSignInOTPRPCImpl.execute(
      agencyRequestSignInOTPRequest,
    );
    return agencyRequestSignInOTPResponse;
  }

  Future<
    LeoRPCResult<AgencyConfirmSignInOTPResponse, AgencyConfirmSignInOTPError>
  >
  confirmSignInOTP(Otp otp, LeoUUID otpId, LeoPhoneNumber phoneNumber) async {
    final AgencyConfirmSignInOTPRPC confirmSignInOTPRPCImpl =
        currentFlavor.isMock
            ? MockAgentConfirmSignInOTPRPCImpl()
            : AgencyConfirmSignInOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final AgencyConfirmSignInOTPRequest confirmSignInOTPRequest =
        AgencyConfirmSignInOTPRequest(
          otp: otp,
          otpId: otpId,
          phoneNumber: phoneNumber,
        );
    final LeoRPCResult<
      AgencyConfirmSignInOTPResponse,
      AgencyConfirmSignInOTPError
    >
    confirmSignInOTPResponse = await confirmSignInOTPRPCImpl.execute(
      confirmSignInOTPRequest,
    );
    return confirmSignInOTPResponse;
  }

  Future<
    LeoRPCResult<AgencyResendSignInOTPResponse, AgencyResendSignInOTPError>
  >
  resendSignInOTP(LeoUUID otpId) async {
    final AgencyResendSignInOTPRPC resendSignInOTPRPCImpl =
        currentFlavor.isMock
            ? MockAgentResendSignInOTPRPCImpl()
            : AgencyResendSignInOTPRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final AgencyResendSignInOTPRequest resendSignInOTPRequest =
        AgencyResendSignInOTPRequest(otpId: otpId);
    final LeoRPCResult<
      AgencyResendSignInOTPResponse,
      AgencyResendSignInOTPError
    >
    resendSignInOTPResponse = await resendSignInOTPRPCImpl.execute(
      resendSignInOTPRequest,
    );
    return resendSignInOTPResponse;
  }

  Future<LeoRPCResult<SignInAgentResponse, SignInAgentError>> signInAgent(
    Password password,
    LeoPhoneNumber phoneNumber,
    LeoUUID phoneNUmberValidationToken,
  ) async {
    final packageInfo = await PackageInfo.fromPlatform();
    final SignInAgentRPC signInAgentRPCImpl =
        currentFlavor.isMock
            ? MockSignInAgentRPCImpl()
            : SignInAgentRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );
    final SignInAgentRequest signInAgentRequest = SignInAgentRequest(
      password: password,
      phoneNumber: phoneNumber,
      phoneNumberValidatedToken: phoneNUmberValidationToken,
      deviceInformation: DeviceInformation(
        frontEndPlatform:
            Platform.isAndroid
                ? FrontEndPlatformEnum.ANDROID
                : FrontEndPlatformEnum.IOS,
        applicationType: AppEnum.AGENT,
        applicationVersion: packageInfo.version,
      ),
    );
    final LeoRPCResult<SignInAgentResponse, SignInAgentError>
    signInAgentResponse = await signInAgentRPCImpl.execute(signInAgentRequest);
    return signInAgentResponse;
  }
}
