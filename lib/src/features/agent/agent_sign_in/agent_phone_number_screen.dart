import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/bloc/agent_sign_in_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_number_text_field/phone_number_field_with_icon.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sub_screen_layout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../widgets/bcn_logo.dart';

class AgentPhoneNumberScreen extends StatefulWidget {
  static const id = "/agent-phone-number-screen";

  const AgentPhoneNumberScreen({super.key});

  @override
  State<AgentPhoneNumberScreen> createState() => _AgentPhoneNumberScreenState();
}

class _AgentPhoneNumberScreenState extends State<AgentPhoneNumberScreen> {
  late final AgentSignInBloc _signInBloc = BlocProvider.of<AgentSignInBloc>(
    context,
  );
  final GlobalKey<FormState> _phoneNumberFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateAgentCredentials = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return SubScreenLayout(
      primaryButton: PrimaryButton(
        labelText: context.localizations.next,
        onPressed: () {
          if (!(_phoneNumberFormKey.currentState?.validate() ?? false)) {
            setState(() {
              _shouldValidateAgentCredentials =
                  AutovalidateMode.onUserInteraction;
            });
            return;
          }
          _signInBloc.add(AgentSignInEvent.requestOTP(context));
        },
      ),
      child: Form(
        key: _phoneNumberFormKey,
        autovalidateMode: _shouldValidateAgentCredentials,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalGapEight,
            const BCNLogo(),
            verticalGapTwentyFour,
            Text(
              context.localizations.signInAsAgent,
              style: context.appTextStyles.titleBold.copyWith(
                color: context.appColors.titleColor,
              ),
            ),
            verticalGapFour,
            Text(
              context.localizations.signInWithMobileNumber,
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShadeDefaultColor,
              ),
            ),
            verticalGapForty,
            PhoneNumberFieldWithIcon(
              onPhoneNumberValidated: (phoneNumber) {
                _signInBloc.add(
                  AgentSignInEvent.onPhoneNumberChange(phoneNumber),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
