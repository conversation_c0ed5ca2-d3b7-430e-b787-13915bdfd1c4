import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/bloc/agent_sign_in_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/password_field.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sub_screen_layout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../exceptions/url_destination_missing.dart';

class AgentPasswordScreen extends StatefulWidget {
  static const id = "/agent-password-screen";

  const AgentPasswordScreen({super.key});

  @override
  State<AgentPasswordScreen> createState() => _AgentPasswordScreenState();
}

class _AgentPasswordScreenState extends State<AgentPasswordScreen> {
  late final AgentSignInBloc _signInBloc = BlocProvider.of<AgentSignInBloc>(
    context,
  );
  final GlobalKey<FormState> _passwordFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateAgentCredentials = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return SubScreenLayout(
      title: context.localizations.enterPasswordTitle,
      primaryButton: Column(
        children: [
          PrimaryTextButton(
            text: context.localizations.forgotPassword,
            // buttonHeight is not used here because it passes double.infinity
            // as the width but that value is ignored by fixedSize property.
            // Reference: https://api.flutter.dev/flutter/material/ButtonStyle/fixedSize.html
            // double.maxFinite value needs to be passed here as the width.
            fixedSize: const Size(double.maxFinite, dimenForty),
            onTap: _showForgotPasswordConfirmation,
          ),
          verticalGapSixteen,
          PrimaryButton(
            labelText: context.localizations.signIn,
            onPressed: () {
              if (!(_passwordFormKey.currentState?.validate() ?? false)) {
                _shouldValidateAgentCredentials =
                    AutovalidateMode.onUserInteraction;
                setState(() {
                  _shouldValidateAgentCredentials =
                      AutovalidateMode.onUserInteraction;
                });
                return;
              }
              _signInBloc.add(AgentSignInEvent.signInAgent(context));
            },
          ),
        ],
      ),
      child: Form(
        key: _passwordFormKey,
        autovalidateMode: _shouldValidateAgentCredentials,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            verticalGapEight,
            Text(
              context.localizations.enterPasswordMessage,
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShadeDefaultColor,
              ),
              textAlign: TextAlign.center,
            ),
            verticalGapForty,
            PasswordField(
              enableVisibility: true,
              onPasswordChanged: (password) {
                _signInBloc.add(
                  AgentSignInEvent.onPasswordChange(password.trim()),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showForgotPasswordConfirmation() {
    AgencyAppDialog.showAppDialog(
      context: context,
      contentText: context.localizations.forgotPasswordMessage,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.dismiss.toUpperCase(),
              onTap: () => dialogContext.navigator.pop(),
            ),
            PrimaryTextButton(
              text: context.localizations.open.toUpperCase(),
              onTap: () {
                dialogContext.navigator.pop();
                _handleResetPassword(screenContext: context);
              },
            ),
          ],
    );
  }

  Future<void> _handleResetPassword({
    required BuildContext screenContext,
  }) async {
    try {
      await launchExternalUrl(resetPasswordUrl);
    } on UrlDestinationMissingException catch (_) {
      if (screenContext.mounted) {
        _communicateMissingBrowser(screenContext);
      }
    }
  }

  void _communicateMissingBrowser(BuildContext context) {
    AgencyAppDialog.showAppDialog(
      context: context,
      contentText:
          context.localizations.unableToOpenBCNMissingWebBrowserErrorMessage,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.dismiss.toUpperCase(),
              onTap: () => dialogContext.navigator.pop(),
            ),
          ],
    );
  }
}
