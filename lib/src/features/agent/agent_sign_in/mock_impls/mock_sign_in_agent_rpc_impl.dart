import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockSignInAgentRPCImpl extends SignInAgentRPC {
  @override
  Future<LeoRPCResult<SignInAgentResponse, SignInAgentError>> execute(
    SignInAgentRequest request,
  ) {
    final SignInAgentResponse response = SignInAgentResponse(
      llt: "MOCKLLT",
      slt: "MOCKSLT",
      locale: Locale(code: "EN_US"),
    );

    final SignInAgentError error =
        SignInAgentError.SignInAgentErrorPhoneNumberRegisteredAsAgentManagerError(
          errorCode: "",
        );

    final LeoRPCResult<SignInAgentResponse, SignInAgentError> result =
        getLeoRPCResult(
          shouldThrowError: false,
          response: response,
          error: error,
        );

    return Future.delayed(2.seconds, () => result);
  }
}
