import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockRequestSignInOTPRPCImpl extends AgencyRequestSignInOTPRPC {
  @override
  Future<
    LeoRPCResult<AgencyRequestSignInOTPResponse, AgencyRequestSignInOTPError>
  >
  execute(AgencyRequestSignInOTPRequest request) {
    final AgencyRequestSignInOTPResponse response =
        AgencyRequestSignInOTPResponse(
          otpDetails: OTPDetails(
            otpId: LeoUUID("6b030092-e058-48fb-81be-6d7d60ac5a0d"),
            validityDetails: OTPValidityDetails(
              nextResendAt: DateTime.now().add(5.minutes),
              expiresAt: DateTime.now().add(10.minutes),
            ),
          ),
        );

    final AgencyRequestSignInOTPError error =
        AgencyRequestSignInOTPError.AgencyRequestSignInOTPErrorPhoneNumberRegisteredAsAgentManagerError(
          errorCode: "",
        );

    final LeoRPCResult<
      AgencyRequestSignInOTPResponse,
      AgencyRequestSignInOTPError
    >
    result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
