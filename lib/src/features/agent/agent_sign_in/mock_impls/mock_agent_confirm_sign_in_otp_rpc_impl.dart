import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockAgentConfirmSignInOTPRPCImpl extends AgencyConfirmSignInOTPRPC {
  @override
  Future<
    LeoRPCResult<AgencyConfirmSignInOTPResponse, AgencyConfirmSignInOTPError>
  >
  execute(AgencyConfirmSignInOTPRequest request) {
    final AgencyConfirmSignInOTPResponse response =
        AgencyConfirmSignInOTPResponse(
          phoneNumberValidatedToken: LeoUUID(
            "6b030092-e058-48fb-81be-6d7d60ac5a0d",
          ),
        );

    final AgencyConfirmSignInOTPError error =
        AgencyConfirmSignInOTPError.AgencyConfirmSignInOTPErrorAgentDisabledError(
          errorCode: "",
        );

    final LeoRPCResult<
      AgencyConfirmSignInOTPResponse,
      AgencyConfirmSignInOTPError
    >
    result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
