import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockAgentResendSignInOTPRPCImpl extends AgencyResendSignInOTPRPC {
  @override
  Future<
    LeoRPCResult<AgencyResendSignInOTPResponse, AgencyResendSignInOTPError>
  >
  execute(AgencyResendSignInOTPRequest request) {
    final AgencyResendSignInOTPResponse response =
        AgencyResendSignInOTPResponse(
          otpDetails: OTPResendDetails(
            numberOfResendsLeft: 2,
            validityDetails: OTPValidityDetails(
              nextResendAt: DateTime.now().add(5.minutes),
              expiresAt: DateTime.now().add(10.minutes),
            ),
          ),
        );

    final AgencyResendSignInOTPError error =
        AgencyResendSignInOTPError.AgencyResendSignInOTPErrorIncorrectIdError(
          errorCode: "",
        );

    final LeoRPCResult<
      AgencyResendSignInOTPResponse,
      AgencyResendSignInOTPError
    >
    result = getLeoRPCResult(
      shouldThrowError: false,
      response: response,
      error: error,
    );

    return Future.delayed(2.seconds, () => result);
  }
}
