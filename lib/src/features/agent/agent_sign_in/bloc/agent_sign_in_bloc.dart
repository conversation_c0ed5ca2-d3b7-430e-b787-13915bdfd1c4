import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/core/route_generator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/agent_otp_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/agent_password_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/repository/agent_sign_in_repository.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../../../../core/logger.dart';
import '../../agent_setup/application_status/agent_application_status_screen.dart';

part 'agent_sign_in_bloc.freezed.dart';

class AgentSignInBloc extends Bloc<AgentSignInEvent, AgentSignInState>
    with RPCHandler {
  final AgentSignInRepository _repository = AgentSignInRepository();
  String? _phoneNumber;
  OTPDetails? _otpDetails;
  String? _otp;
  String? _password;
  LeoUUID? _phoneNumberValidationToken;

  AgentSignInBloc() : super(const Initial()) {
    on<AgentSignInEvent>((event, emit) async {
      switch (event) {
        case OnPhoneNumberChange(:final phoneNumber):
          _phoneNumber = phoneNumber;
        case OnPasswordChange(:final password):
          _password = password;
        case RequestOTP(:final context):
          await _requestOTP(context, emit);
        case AddOTP(:final otp):
          if (state is Loading) return;
          _otp = otp;
        case ConfirmOTP(:final context):
          await _confirmOTP(context, emit);
        case ResendOTP(:final context):
          await _resendOTP(context, emit);
        case SignInAgent(:final context):
          await _signInAgent(emit, context);
      }
    });
  }

  Future<void> _requestOTP(BuildContext context, Emitter emit) async {
    l.d("Agent Sign In Request OTP: $phoneNumber");
    AgencyAppDialog.showSpinnerDialog(context);
    emit(const AgentSignInState.loading());
    final NavigatorState rootNavigator = context.rootNavigator;
    try {
      final LeoPhoneNumber phoneNumber = LeoPhoneNumber(_phoneNumber!);
      await rpcHandler(
        () async {
          final rpcResult = await _repository.requestSignInOTP(phoneNumber);
          l.d("Request OTP RPC result: $rpcResult");
          if (context.mounted) {
            _handleRequestOTPResponse(context, emit, rpcResult);
          }
        },
        onTransientError: (_) {
          rootNavigator.pop();
          emit(const AgentSignInState.transientError());
        },
      );
    } catch (_) {
      rootNavigator.pop();
      emit(const AgentSignInState.prominentError());
      if (context.mounted) {
        _handlePhoneNumberUnknownError(context);
      }
    }
  }

  void _handleRequestOTPResponse(
    BuildContext context,
    Emitter emit,
    LeoRPCResult<AgencyRequestSignInOTPResponse, AgencyRequestSignInOTPError>
    rpcResult,
  ) {
    rpcResult.when(
      response: (response) {
        _otpDetails = response.otpDetails;
        emit(const AgentSignInState.initial());
        context.rootNavigator.pop();
        context.navigator.push(
          Routes.getMaterialRoute(
            AgentOtpScreen.id,
            BlocProvider.value(value: this, child: const AgentOtpScreen()),
          ),
        );
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const AgentSignInState.prominentError());
        error.when(
          tooManyRequests: (_) => ProminentErrorHandler.tooManyRequests(),
          phoneNumberUnknown: (_) => _handlePhoneNumberUnknownError(context),
          kycNotCompleted: (_) => _showKycNotCompletedError(context),
          phoneNumberRegisteredAsAgentManager:
              (_) => _showRegisteredAsAgentManagerError(context),
          couldNotSendOtp: (_) => ProminentErrorHandler.couldNotSendOTP(),
          signInTemporarilyBlocked:
              (_) => ProminentErrorHandler.signInTemporarilyBlocked(),
        );
      },
    );
  }

  void _handlePhoneNumberUnknownError(BuildContext context) {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.signInPhoneNumberUnknown,
      buttonText: context.localizations.tryAgain,
    );
  }

  Future<void> _confirmOTP(
    BuildContext context,
    Emitter<AgentSignInState> emit,
  ) async {
    l.d("Confirm OTP: $_otp");
    AgencyAppDialog.showSpinnerDialog(context);
    emit(const AgentSignInState.loading());
    await rpcHandler(
      () async {
        final result = await _repository.confirmSignInOTP(
          Otp(otp: _otp!),
          _otpDetails!.otpId,
          LeoPhoneNumber(_phoneNumber!),
        );
        l.d("Agent Sign In Confirm OTP result: $result");
        if (context.mounted) {
          _handleConfirmOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        context.rootNavigator.pop();
        l.d("Emitting Transient Error");
        emit(const AgentSignInState.transientError());
      },
    );
  }

  void _handleConfirmOTPResult(
    BuildContext context,
    Emitter<AgentSignInState> emit,
    LeoRPCResult<AgencyConfirmSignInOTPResponse, AgencyConfirmSignInOTPError>
    result,
  ) {
    result.when(
      response: (response) {
        _phoneNumberValidationToken = response.phoneNumberValidatedToken;
        emit(const AgentSignInState.initial());
        context.rootNavigator.pop();
        context.navigator.push(
          Routes.getMaterialRoute(
            AgentPasswordScreen.id,
            BlocProvider.value(value: this, child: const AgentPasswordScreen()),
          ),
        );
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const AgentSignInState.prominentError());
        error.when(
          // This can never happen as the otpId is received from the server.
          incorrectOtpId: (e) => throw DeveloperError(e.toString()),
          // This can never happen as this is checked in the previous RPC call.
          phoneNumberUnknown: (e) => throw DeveloperError(e.toString()),
          signinSessionExpired: (_) => _showSignInSessionExpiredError(context),
          otpExpired: (_) => ProminentErrorHandler.otpExpired(),
          incorrectOtp:
              (error) => ProminentErrorHandler.incorrectOTP(
                numberOfValidationsAttemptsLeft:
                    error.numberOfValidationAttemptsLeft,
                featureName: context.localizations.signIn,
                onOkay: navigateToLandingScreen,
              ),
          tooManyOtpAttempts:
              (_) => ProminentErrorHandler.tooManyOTPRequests(
                onOkay: navigateToLandingScreen,
              ),
          tooManyRequests: (_) => ProminentErrorHandler.tooManyRequests(),
          inactiveState:
              (_) => ProminentErrorHandler.genericError(
                onOkay: navigateToLandingScreen,
              ),
          agentDisabled: (_) => _showAgentDisabledError(context),
          userDisabled: (_) => ProminentErrorHandler.userDisabled(),
          kycNotCompleted: (_) => _showKycNotCompletedError(context),
          phoneNumberRegisteredAsAgentManager:
              (_) => _showRegisteredAsAgentManagerError(context),
        );
      },
    );
  }

  Future<void> _resendOTP(
    BuildContext context,
    Emitter<AgentSignInState> emit,
  ) async {
    l.d("Resend OTP. OTP ID: ${_otpDetails!.otpId}");
    await rpcHandler(
      () async {
        final result = await _repository.resendSignInOTP(_otpDetails!.otpId);
        l.d("Resend OTP result: $result");
        if (context.mounted) {
          await _handleResendOTPResult(context, emit, result);
        }
      },
      onTransientError: (_) {
        emit(const AgentSignInState.transientError());
      },
    );
  }

  Future<void> _handleResendOTPResult(
    BuildContext context,
    Emitter<AgentSignInState> emit,
    LeoRPCResult<AgencyResendSignInOTPResponse, AgencyResendSignInOTPError>
    result,
  ) async {
    result.when(
      response: (response) {
        emit(AgentSignInState.otpResentSuccessfully(response.otpDetails));
      },
      error: (error) {
        emit(const AgentSignInState.prominentError());
        error.when(
          // This can never happen as the otpId is received from the server.
          incorrectId: (e) => throw DeveloperError(e.toString()),
          // This can never happen as this is checked in the previous RPC call.
          phoneNumberUnknown: (e) => throw DeveloperError(e.toString()),
          signinSessionExpired: (_) => _showSignInSessionExpiredError(context),
          couldNotSendOtp: (_) => ProminentErrorHandler.couldNotSendOTP(),
          waitForResend: (_) => ProminentErrorHandler.waitForOTPResend(),
          kycNotCompleted: (_) => _showKycNotCompletedError(context),
          tooManyRequests: (_) => ProminentErrorHandler.tooManyRequests(),
          phoneNumberRegisteredAsAgentManager:
              (_) => _showRegisteredAsAgentManagerError(context),
        );
      },
    );
  }

  Future<void> _signInAgent(
    Emitter<AgentSignInState> emit,
    BuildContext context,
  ) async {
    AgencyAppDialog.showSpinnerDialog(context);
    emit(const AgentSignInState.loading());
    final NavigatorState rootNavigator = context.rootNavigator;
    try {
      final Password password = Password(password: _password!);
      await rpcHandler(
        () async {
          final rpcResult = await _repository.signInAgent(
            password,
            LeoPhoneNumber(_phoneNumber!),
            _phoneNumberValidationToken!,
          );
          if (context.mounted) {
            _handleSignInAgentResponse(context, emit, rpcResult);
          }
        },
        onTransientError: (_) {
          context.rootNavigator.pop();
          emit(const AgentSignInState.transientError());
        },
      );
    } catch (_) {
      rootNavigator.pop();
      emit(const AgentSignInState.prominentError());
      if (context.mounted) {
        _showIncorrectPasswordError(context);
      }
    }
  }

  Future<void> _handleSignInAgentResponse(
    BuildContext context,
    Emitter<AgentSignInState> emit,
    LeoRPCResult<SignInAgentResponse, SignInAgentError> result,
  ) async {
    result.when(
      response: (response) async {
        await AuthProvider.instance.setCredentials(
          slt: response.slt,
          llt: response.llt,
          userType: UserType.agent,
        );
        await BCNLocale.changeDefaultLocale(response.locale);
        if (context.mounted) {
          context.rootNavigator.pop();
          context.navigator.pushNamedAndRemoveUntil(
            AgentApplicationStatusScreen.id,
            (_) => false,
          );
        }
      },
      error: (error) {
        context.rootNavigator.pop();
        emit(const AgentSignInState.prominentError());
        error.when(
          // This can never happen as the phone number token is received from the server.
          invalidToken: (e) => throw DeveloperError(e.toString()),
          // This can never happen as this is checked in the previous RPC call.
          phoneNumberUnknown: (e) => throw DeveloperError(e.toString()),
          signinSessionExpired: (_) => _showSignInSessionExpiredError(context),
          phonenumberValidatedTokenExpired:
              (_) => ProminentErrorHandler.genericError(
                onOkay: navigateToLandingScreen,
              ),
          kycNotCompleted: (_) => _showKycNotCompletedError(context),
          tooManyRequests: (_) => ProminentErrorHandler.tooManyRequests(),
          agentDisabled: (_) => _showAgentDisabledError(context),
          phoneNumberRegisteredAsAgentManager:
              (_) => _showRegisteredAsAgentManagerError(context),
          userDisabled: (_) => ProminentErrorHandler.userDisabled(),
          incorrectPassword: (_) => _showIncorrectPasswordError(context),
        );
      },
    );
  }

  void _showSignInSessionExpiredError(BuildContext context) {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.signInSessionExpired,
      isDismissible: false,
      onOkay: navigateToLandingScreen,
    );
  }

  void _showKycNotCompletedError(BuildContext context) {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.kycNotCompleted,
      isDismissible: false,
      onOkay: navigateToLandingScreen,
    );
  }

  void _showRegisteredAsAgentManagerError(BuildContext context) {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.registeredAsAgentManager,
      isDismissible: false,
      onOkay: navigateToLandingScreen,
    );
  }

  void _showAgentDisabledError(BuildContext context) {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.agentDisabled,
      isDismissible: false,
      onOkay: navigateToLandingScreen,
    );
  }

  void _showIncorrectPasswordError(BuildContext context) {
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.incorrectPassword,
      buttonText: context.localizations.tryAgain,
    );
  }

  String? get phoneNumber => _phoneNumber;

  OTPDetails? get otpDetails => _otpDetails;
}

@freezed
sealed class AgentSignInState with _$AgentSignInState {
  const factory AgentSignInState.initial() = Initial;

  const factory AgentSignInState.loading() = Loading;

  const factory AgentSignInState.prominentError() = ProminentError;

  const factory AgentSignInState.invalidOTP() = InvalidOTP;

  const factory AgentSignInState.transientError() = TransientError;

  const factory AgentSignInState.otpResentSuccessfully(
    OTPResendDetails resendDetails,
  ) = OTPResentSuccessfully;

  const factory AgentSignInState.otpConfirmed() = OTPConfirmed;
}

@freezed
sealed class AgentSignInEvent with _$AgentSignInEvent {
  const factory AgentSignInEvent.onPhoneNumberChange(String? phoneNumber) =
      OnPhoneNumberChange;

  const factory AgentSignInEvent.onPasswordChange(String password) =
      OnPasswordChange;

  const factory AgentSignInEvent.requestOTP(BuildContext context) = RequestOTP;

  const factory AgentSignInEvent.confirmOTP(BuildContext context) = ConfirmOTP;

  const factory AgentSignInEvent.resendOTP(BuildContext context) = ResendOTP;

  const factory AgentSignInEvent.addOtp(String otp) = AddOTP;

  const factory AgentSignInEvent.signInAgent(BuildContext context) =
      SignInAgent;
}
