import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/bloc/agent_sign_in_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sign_in_otp_validation_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AgentOtpScreen extends StatefulWidget {
  static const id = '/agent-otp-screen';

  const AgentOtpScreen({super.key});

  @override
  State<AgentOtpScreen> createState() => _AgentOtpScreenState();
}

class _AgentOtpScreenState extends State<AgentOtpScreen> {
  late final AgentSignInBloc _signInBloc = BlocProvider.of<AgentSignInBloc>(
    context,
  );
  final GlobalKey<FormState> _otpFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateAgentCredentials = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AgentSignInBloc, AgentSignInState>(
      builder: (context, state) {
        return SignInOTPValidationScreen(
          formKey: _otpFormKey,
          autoValidateMode: _shouldValidateAgentCredentials,
          phoneNumber: _signInBloc.phoneNumber!,
          otpValidityDetails: _signInBloc.otpDetails!.validityDetails,
          onOTPResend: () async {
            _signInBloc.add(AgentSignInEvent.resendOTP(context));

            /// Wait For Bloc to send some State.
            final latestState = await _signInBloc.stream.firstOrNull;
            return switch (latestState) {
              OTPResentSuccessfully(:final resendDetails) => resendDetails,
              _ => null,
            };
          },
          onOTPChanged: (otp) {
            _signInBloc.add(AgentSignInEvent.addOtp(otp));
          },
          validateOTPButton: BlocBuilder<AgentSignInBloc, AgentSignInState>(
            builder: (context, state) {
              return PrimaryButton(
                labelText: context.localizations.continueText.toUpperCase(),
                onPressed: () {
                  if (!(_otpFormKey.currentState?.validate() ?? false)) {
                    _shouldValidateAgentCredentials =
                        AutovalidateMode.onUserInteraction;
                    setState(() {
                      _shouldValidateAgentCredentials =
                          AutovalidateMode.onUserInteraction;
                    });
                    return;
                  }
                  _signInBloc.add(AgentSignInEvent.confirmOTP(context));
                },
              );
            },
          ),
        );
      },
    );
  }
}
