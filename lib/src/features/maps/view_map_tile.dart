import 'package:bcn_agency_banking_flutter/src/features/maps/map_location_tile.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:flutter/material.dart';

/// Widget used to display the information of the selected address.
/// Widget also tappable allowing the user to change the selected location.
class ViewMapTile extends StatelessWidget {
  final VoidCallback onTap;

  /// The main contents of the address to be displayed.
  final String? labelText;

  /// The additional information of the address to be displayed.
  final String? infoText;

  final TextStyle? labelStyle;

  final String? assetName;

  const ViewMapTile({
    Key? key,
    required this.onTap,
    this.labelText,
    this.infoText,
    this.labelStyle,
    this.assetName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TitleWidget(title: context.localizations.shopLocation),
        Padding(
          padding: verticalPaddingEight,
          child: InkWell(
            onTap: onTap,
            child: Ink(
              padding: const EdgeInsets.symmetric(
                vertical: dimenEight,
                horizontal: dimenSixteen,
              ),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: MapLocationTile(
                      labelText:
                          labelText ?? context.localizations.tapToAddLocation,
                      infoText: infoText,
                      labelStyle: labelStyle,
                    ),
                  ),
                  horizontalGapSixteen,
                  IconWidget(
                    assetName: assetName ?? ABAssets.arrowRightIcon,
                    iconColor: context.appColors.neutralShadeDefaultColor,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
