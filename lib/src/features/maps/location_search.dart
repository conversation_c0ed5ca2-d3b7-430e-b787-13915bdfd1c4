import 'package:bcn_agency_banking_flutter/src/core/location_services/location_search_provider.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/map_location_tile.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_search.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:dedwig/dedwig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LocationSearch extends SearchDelegate<PlaceDetails?> {
  LocationSearch({
    required String searchFieldLabel,
    required InputDecorationTheme searchFieldDecorationTheme,
  }) : super(
         searchFieldLabel: searchFieldLabel,
         searchFieldDecorationTheme: searchFieldDecorationTheme,
       );
  final LocationSearchProvider _provider = LocationSearchProvider();

  List<Prediction> _lastFetchedPredictions = [];
  // Initializing this bool lately crashes the app when user presses the search
  // button earlier than the search has completed fetching data for the very
  // first time. In that case, this bool won't have been initialized yet, and
  // will throw LateInitializationError. To save this, we're initializing it
  // rather to true.
  bool _isInternetConnected = true;

  @override
  ThemeData appBarTheme(BuildContext context) {
    return super
        .appBarTheme(context)
        .copyWith(
          appBarTheme: AppBarTheme(
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: context.appColors.primaryDefaultColor,
            ),
            elevation: appBarElevation,
            color: context.appColors.backgroundColor,
            shape: Border(
              bottom: BorderSide(
                color: context.appColors.neutralShade3Color,
                width: appBarBorderWidth,
              ),
            ),
          ),
          textTheme: TextTheme(
            titleLarge: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
          ),
        );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.trim().isNotEmpty)
        IconButton(
          onPressed: () async {
            query = '';
          },
          icon: IconWidget(
            assetName: ABAssets.closeIcon,
            iconColor: context.appColors.neutralShade9Color,
          ),
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      onPressed: () async {
        close(context, null);
      },
      icon: IconWidget(
        assetName: ABAssets.arrowLeftIcon,
        iconColor: context.appColors.neutralShade9Color,
      ),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return FutureBuilder<List<Prediction>>(
      future: query.trim().isEmpty ? null : _provider.fetchSuggestions(query),
      builder: (context, snapshot) {
        if (query.trim().isEmpty) {
          return const SizedBox();
        } else if (snapshot.hasData) {
          _isInternetConnected = true;
          _lastFetchedPredictions = snapshot.data!;
          return _buildSuggestions(_lastFetchedPredictions, context);
        } else if (snapshot.hasError) {
          if (snapshot.error is NetworkException) {
            _isInternetConnected = false;
            // Not resetting this list will show the old searched results when
            // pressed on the search button (with changed search query now after
            // internet was disconnected) after the device is back online.
            _lastFetchedPredictions = [];
            return _buildSuggestions(_lastFetchedPredictions, context);
          } else {
            throw DeveloperError(snapshot.error.toString());
          }
        } else {
          return const Spinner();
        }
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    // Rechecks the data if the _isInternetConnected flag is false. This
    // prevents no internet connected state even when the device is back online
    // but the buildSuggestions future widget method didn't get a chance to try
    // to fetch data again.
    if (!_isInternetConnected) return buildSuggestions(context);
    return _buildSuggestions(_lastFetchedPredictions, context);
  }

  Widget _buildSuggestions(List<Prediction> predictions, BuildContext context) {
    if (query.trim().isEmpty) {
      return const SizedBox();
    }
    if (predictions.isEmpty) {
      return Padding(
        padding: allPaddingSixteen,
        child: Text(
          _isInternetConnected
              ? context.localizations.noLocationsFound(query)
              : context.localizations.noInternetTryAgainLater,
          style: context.appTextStyles.labelText2.copyWith(
            color: context.appColors.neutralShade1Color,
          ),
        ),
      );
    }
    return Column(
      children: [
        verticalGapEight,
        Expanded(
          child: ListView.builder(
            itemBuilder: (context, index) {
              if (predictions[index].name != null &&
                  predictions[index].formattedAddress != null) {
                return InkWell(
                  child: Ink(
                    padding: allPaddingSixteen,
                    child: MapLocationTile(
                      labelText: predictions[index].name!,
                      infoText: predictions[index].formattedAddress!,
                    ),
                  ),
                  onTap: () async {
                    AgencyAppDialog.showSpinnerDialog(context);
                    final placeDetails = await _provider.fetchDetails(
                      predictions[index],
                    );
                    if (context.mounted) {
                      context.navigator.pop();
                      close(context, placeDetails);
                    }
                  },
                );
              } else {
                return const SizedBox();
              }
            },
            itemCount: predictions.length,
          ),
        ),
      ],
    );
  }
}
