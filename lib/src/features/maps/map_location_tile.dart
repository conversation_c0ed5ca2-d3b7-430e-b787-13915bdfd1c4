import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:flutter/material.dart';

/// Widget used to display the address information of a particular location.
class MapLocationTile extends StatelessWidget {
  /// The main contents of the address to be displayed.
  final String labelText;

  /// The additional information of the address to be displayed.
  final String? infoText;
  final TextStyle? labelStyle;

  const MapLocationTile({
    super.key,
    required this.labelText,
    this.infoText,
    this.labelStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _getLocationPointIcon(context),
        horizontalGapSixteen,
        _getAddressText(context),
      ],
    );
  }

  Container _getLocationPointIcon(BuildContext context) {
    return Container(
      height: dimenForty,
      width: dimenForty,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: context.appColors.neutralShade4Color,
      ),
      child: IconWidget(
        assetName: ABAssets.locationPointIcon,
        height: dimenTwenty,
        width: dimenTwenty,
      ),
    );
  }

  Expanded _getAddressText(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            labelText,
            style: (labelStyle ?? context.appTextStyles.labelText2).copyWith(
              color: context.appColors.neutralShade1Color,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (infoText != null) ...[
            verticalGapFour,
            Text(
              infoText!,
              style: context.appTextStyles.labelText3.copyWith(
                color: context.appColors.neutralShade7Color,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}
