import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../core/logger.dart';
import '../../resources/map_styles.dart';
import '../../utils/constants.dart';

/// Class to display the preview of a given location on the map.
class MapPreviewWidget extends StatefulWidget {
  const MapPreviewWidget({
    Key? key,
    required double latitude,
    required double longitude,
    this.height,
    this.isCurrentLocationEnabled = false,
    this.canInteract = true,
  }) : _latitude = latitude,
       _longitude = longitude,
       super(key: key);

  final double _latitude;
  final double _longitude;
  final double? height;

  /// Enables all types of interactions in the widget.
  /// Setting this to false will remove of all the possible interactions and
  /// only display the preview of the given location on the map.
  final bool canInteract;

  /// Enables myLocationButton on Google Map. This button navigates the user to their current location on the map.
  final bool isCurrentLocationEnabled;

  @override
  MapPreviewWidgetState createState() {
    return MapPreviewWidgetState();
  }
}

class MapPreviewWidgetState extends State<MapPreviewWidget>
    with WidgetsBindingObserver {
  final Map<MarkerId, Marker> _markers = <MarkerId, Marker>{};
  final String _markerIdString = 'current_location_marker_id';
  bool _isMapCreated = false;
  LatLng? _currentLatLong;
  late GoogleMapController _mapController;
  bool _isThemeChanged = false;
  String? _mapStyle;

  @override
  void initState() {
    super.initState();
    MapStyles.loadMapStyles().then((_) {
      _updateMapStyle();
    });
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    // Store a reference to the variable and let
    // `_getLocationView` synchronously update the
    // `_currentLatLong` object be set to the current coordinate.
    // Then, the following `refresh` will skip the check
    // and code will work.
    // However, in cases where the build is called on a
    // widget that is already created, the `refreshMarker` method
    // will update the lat long to the current value.
    final locationView = ClipRRect(
      borderRadius: BorderRadius.circular(4),
      child: SizedBox(
        width: double.infinity,
        height: widget.height,
        child: _getLocationView(),
      ),
    );
    _refreshMarkerIfNeeded();
    return locationView;
  }

  void _refreshMarkerIfNeeded() {
    final latestLatLong = LatLng(widget._latitude, widget._longitude);
    // If device theme is switched while this widget is rendered, the
    // location marker, being a PNG instead of an SVG, needs to be updated to a
    // different PNG now (light/dark). This is not the case with SVG where only
    // color of the widget is changed, and that's handled by flutter rebuild.
    l.d("Refresh marker. LatestLatLong: $latestLatLong");
    if (_isMapCreated && latestLatLong != _currentLatLong || _isThemeChanged) {
      _addMarker(latestLatLong);
    }
  }

  Widget _getLocationView() {
    l.d('''Initial Latitude: ${widget._latitude} and
    Longitude: ${widget._longitude}''');
    CameraPosition initialCameraPosition = CameraPosition(
      target: LatLng(widget._latitude, widget._longitude),
      zoom: defaultMapZoom,
    );

    return GoogleMap(
      myLocationButtonEnabled: widget.isCurrentLocationEnabled,
      myLocationEnabled: widget.isCurrentLocationEnabled,
      mapType: MapType.normal,
      onMapCreated: (mapController) async {
        _mapController = mapController;
        _isMapCreated = true;
        _addMarker(LatLng(widget._latitude, widget._longitude));
        await _updateMapStyle();
      },
      style: _mapStyle,
      initialCameraPosition: initialCameraPosition,
      markers: Set<Marker>.of(_markers.values),
      zoomControlsEnabled: false,
      rotateGesturesEnabled: widget.canInteract,
      scrollGesturesEnabled: widget.canInteract,
      zoomGesturesEnabled: widget.canInteract,
      tiltGesturesEnabled: widget.canInteract,
    );
  }

  void _addMarker(LatLng newCoordinates) async {
    final MarkerId markerId = MarkerId(_markerIdString);
    final Marker marker = Marker(
      markerId: markerId,
      position: newCoordinates,
      icon: await _getMarkerIcon(),
    );
    setState(() {
      _markers.clear();
      _markers[markerId] = marker;
    });
    if (_currentLatLong != newCoordinates) {
      _currentLatLong = newCoordinates;
      await _animateCamera(newCoordinates);
    }

    if (_isThemeChanged) _isThemeChanged = false;
  }

  Future<void> _animateCamera(LatLng coordinates) async {
    await _mapController.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(target: coordinates, zoom: defaultMapZoom),
      ),
    );
  }

  Future<BitmapDescriptor> _getMarkerIcon() {
    // This is the only case where we are using PNG instead of SVG, specifically
    // because Google Maps flutter library doesn't accept SVG icons.
    return BitmapDescriptor.asset(
      ImageConfiguration(
        devicePixelRatio: context.mq.devicePixelRatio,
        size: const Size(_iconWidth, _iconHeight),
      ),
      context.appIcons.mapMarker,
    );
  }

  Future<void> _updateMapStyle() async {
    Brightness theme =
        WidgetsBinding.instance.platformDispatcher.platformBrightness;
    _mapStyle =
        theme == Brightness.dark
            ? MapStyles.darkMapStyle
            : MapStyles.lightMapStyle;
  }

  @override
  void didChangePlatformBrightness() {
    _isThemeChanged = true;
    _updateMapStyle();
  }

  @override
  dispose() {
    _mapController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}

const double _iconHeight = 40.0;
const double _iconWidth = 40.0;
