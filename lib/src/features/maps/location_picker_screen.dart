import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/location_search.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/map_location_tile.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/resources/map_styles.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../helpers/helpers.dart';
import '../../models/location_search.dart';
import '../../utils/constants.dart';

/// Class to display a screen which allows the user to search and select a location on the Map.
class LocationPickerScreen extends StatefulWidget {
  static const id = "/location-picker-screen";
  final LocationDetails? locationDetails;

  const LocationPickerScreen({Key? key, this.locationDetails})
    : super(key: key);

  @override
  LocationPickerScreenState createState() {
    return LocationPickerScreenState();
  }
}

class LocationPickerScreenState extends State<LocationPickerScreen>
    with WidgetsBindingObserver {
  late final GoogleMapController _controller;
  LocationDetails? _selectedLocation;

  // The `_cameraPositionLatitude` and `_cameraPositionLongitude`
  // is required to set the marker once the camera is idle.
  double? _cameraPositionLatitude;
  double? _cameraPositionLongitude;

  bool _isCameraMoving = false;

  final LocationService _locationService = locator<LocationService>();

  String? _mapStyle;

  @override
  void initState() {
    super.initState();
    _setInitialLocation();
    MapStyles.loadMapStyles().then((_) {
      _updateMapStyle();
    });
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      left: false,
      right: false,
      top: false,
      child:
          _selectedLocation != null
              ? Scaffold(
                appBar: _getAppBar(),
                body: Column(
                  children: [_getMapView(), _getCurrentLocationDetails()],
                ),
              )
              : const Scaffold(body: Spinner()),
    );
  }

  PrimaryAppBar _getAppBar() {
    return PrimaryAppBar(
      title: context.localizations.addShopLocation,
      actionIcon: IconButton(
        onPressed: () async {
          final result = await showSearch(
            context: context,
            delegate: LocationSearch(
              searchFieldLabel: context.localizations.searchLocationPlaceHolder,
              searchFieldDecorationTheme: InputDecorationTheme(
                hintStyle: context.appTextStyles.smallText1.copyWith(
                  color: context.appColors.neutralShade2Color,
                ),
                border: InputBorder.none,
              ),
            ),
          );
          if (result != null) {
            _setSelectedLocation(result);
          }
        },
        icon: IconWidget(
          assetName: ABAssets.searchIcon,
          iconColor: context.appColors.genericWhiteColor,
        ),
      ),
    );
  }

  Widget _getMapView() {
    return Expanded(child: _getLocationView());
  }

  Widget _getLocationView() {
    final CameraPosition initialCameraPosition = CameraPosition(
      target: LatLng(_selectedLocation!.latitude, _selectedLocation!.longitude),
      zoom: defaultMapZoom,
    );

    return Stack(
      alignment: Alignment.center,
      children: [
        GoogleMap(
          myLocationButtonEnabled: true,
          myLocationEnabled: true,
          mapType: MapType.normal,
          initialCameraPosition: initialCameraPosition,
          onMapCreated: _onMapCreated,
          style: _mapStyle,
          onCameraMove: _onCameraMove,
          onCameraIdle: _onCameraIdle,
          zoomControlsEnabled: false,
        ),
        // The pointed foot of the location marker icon should be exactly on the
        // location the map is pointing to. With center alignment, the icon is
        // at the center of the screen, but not it's pointed foot. To solve
        // this, we are adding a calculated bottom padding of 30px.
        Padding(
          padding: const EdgeInsets.only(bottom: dimenThirty),
          child: SvgPicture.asset(
            ABAssets.locationIcon,
            height: dimenForty,
            width: dimenForty,
            colorFilter: ColorFilter.mode(
              context.appColors.neutralShade1Color,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    );
  }

  Widget _getCurrentLocationDetails() {
    return Container(
      padding: allPaddingSixteen,
      child: Column(
        children: [_getAddressDetails(), verticalGapSixteen, _getSaveButton()],
      ),
    );
  }

  MapLocationTile _getAddressDetails() {
    return MapLocationTile(
      labelText:
          _selectedLocation?.address?.addressLabel ??
          "${_selectedLocation!.latitude}, ${_selectedLocation!.longitude}",
      infoText:
          _selectedLocation?.address?.addressText ??
          context.localizations.locationSelected,
      labelStyle: context.appTextStyles.labelText2Bold.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
    );
  }

  Widget _getSaveButton() {
    return PrimaryButton(
      labelText: context.localizations.addLocation,
      onPressed: () {
        context.navigator.pop(_selectedLocation!);
      },
    );
  }

  void _onMapCreated(GoogleMapController controller) async {
    _controller = controller;
    await _updateMapStyle();
  }

  void _onCameraMove(CameraPosition cameraPosition) {
    setState(() {
      _cameraPositionLatitude = cameraPosition.target.latitude;
      _cameraPositionLongitude = cameraPosition.target.longitude;
    });
  }

  void _onCameraIdle() async {
    if (_isCameraMoving) {
      setState(() {
        _isCameraMoving = false;
      });
      return;
    } else if (_cameraPositionLatitude == null ||
        _cameraPositionLongitude == null) {
      return;
    } else {
      await _changePosition(
        _cameraPositionLatitude!,
        _cameraPositionLongitude!,
        null,
      );
    }
  }

  void _setInitialLocation() async {
    late double setLatitude, setLongitude;
    if (widget.locationDetails != null) {
      setLatitude = widget.locationDetails!.latitude;
      setLongitude = widget.locationDetails!.longitude;
      await _setGeocodeAddress(setLatitude, setLongitude);
    } else {
      final LocationDetails? currentLocation =
          _locationService.getCurrentLocation;
      if (currentLocation != null) {
        setLatitude = currentLocation.latitude;
        setLongitude = currentLocation.longitude;
        await _setGeocodeAddress(setLatitude, setLongitude);
      }
    }
  }

  Future<void> _setGeocodeAddress(double latitude, double longitude) async {
    final FormattedAddress? geocodeAddress = await getReverseGeocode(
      latitude,
      longitude,
    );
    if (!mounted) return;
    setState(() {
      _cameraPositionLatitude = latitude;
      _cameraPositionLongitude = longitude;
      _selectedLocation = LocationDetails(
        latitude: latitude,
        longitude: longitude,
        address: geocodeAddress,
      );
    });
  }

  Future<void> _setSelectedLocation(PlaceDetails placeDetails) async {
    if (!mounted) return;
    _isCameraMoving = true;
    await _changePosition(
      placeDetails.geometry!.location.lat,
      placeDetails.geometry!.location.lng,
      placeDetails.formattedAddress != null && placeDetails.name != null
          ? FormattedAddress(
            addressLabel: placeDetails.name!,
            addressText: placeDetails.formattedAddress!,
          )
          : null,
    );
    await _animateCamera(
      placeDetails.geometry!.location.lat,
      placeDetails.geometry!.location.lng,
    );
  }

  Future<void> _changePosition(
    double latitude,
    double longitude,
    FormattedAddress? address,
  ) async {
    final FormattedAddress? locationAddress;
    if (address == null) {
      locationAddress = await getReverseGeocode(latitude, longitude);
    } else {
      locationAddress = address;
    }
    if (!mounted) return;
    setState(() {
      _selectedLocation = LocationDetails(
        latitude: latitude,
        longitude: longitude,
        address: locationAddress,
      );
    });
  }

  Future<void> _animateCamera(double latitude, double longitude) async {
    await _controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(latitude, longitude),
          zoom: defaultMapZoom,
        ),
      ),
    );
  }

  Future<void> _updateMapStyle() async {
    Brightness theme =
        WidgetsBinding.instance.platformDispatcher.platformBrightness;
    _mapStyle =
        theme == Brightness.dark
            ? MapStyles.darkMapStyle
            : MapStyles.lightMapStyle;
  }

  @override
  void didChangePlatformBrightness() => _updateMapStyle();

  @override
  dispose() {
    _controller.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
