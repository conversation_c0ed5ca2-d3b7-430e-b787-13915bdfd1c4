import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// [FallbackMaterialLocalisationsDelegate] class is custom Localization delegate for Material Components
/// that notifies the application to fallback to the default system locale for system widgets (i.e.: Cale<PERSON>, DateTimePicker)
/// when the enabled locale is not among the supported locales from flutter for example: Nyanja (ny).
/// support locale list : https://api.flutter.dev/flutter/flutter_localizations/GlobalMaterialLocalizations-class.html.
class FallbackMaterialLocalisationsDelegate
    extends LocalizationsDelegate<MaterialLocalizations> {
  const FallbackMaterialLocalisationsDelegate();

  @override
  bool isSupported(Locale locale) => true;

  @override
  Future<MaterialLocalizations> load(Locale locale) =>
      DefaultMaterialLocalizations.load(locale);

  @override
  bool shouldReload(FallbackMaterialLocalisationsDelegate old) => false;
}

/// Fallback Localisation delegate for Cupertino widgets.
class FallbackCupertinoLocalisationsDelegate
    extends LocalizationsDelegate<CupertinoLocalizations> {
  const FallbackCupertinoLocalisationsDelegate();

  @override
  bool isSupported(Locale locale) => true;

  @override
  Future<CupertinoLocalizations> load(Locale locale) =>
      DefaultCupertinoLocalizations.load(locale);

  @override
  bool shouldReload(FallbackCupertinoLocalisationsDelegate old) => false;
}
