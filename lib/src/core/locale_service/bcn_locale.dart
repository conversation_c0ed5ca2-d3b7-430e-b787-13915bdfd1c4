import 'package:agency_banking_rpcs/assets/localized_image_type.dart';
import 'package:agency_banking_rpcs/assets/localized_text_type.dart';
import 'package:agency_banking_rpcs/assets/multi_resolution_bitmap_image_type.dart';
import 'package:agency_banking_rpcs/assets/themed_image_type.dart';
import 'package:agency_banking_rpcs/types/locale_type.dart' as rpc_locale;
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/bcn_agency_app.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// [BCNLocale] to update locale
/// across the whole app or get the current locale.
/// There are two types of [Locale] in this provider
/// viz:
/// 1. Flutter [Locale]
/// 2. RPC [Locale]
/// Note: This provider only changes the locale across the app
/// and not on the server.
class BCNLocale {
  BCNLocale._();

  /// Main App key will be used to change the locale across the whole app.
  static final mainAppKey = GlobalKey<BCNAgencyAppState>();

  // All the locale codes.
  // All the language codes should be in upper case.
  // Language codes should be in lower case when
  // passing it through the Flutter [Locale.languageCode].
  static const _englishLocaleCode = "EN_US";
  static const _nyanjaLocaleCode = "NY";
  static const _englishLanguageCode = "EN";
  static const _usCountryCode = "US";

  static const _localeCode = "localeCode";

  static const String _localeLanguageAndRegionSeparator = "_";

  // All the Flutter locales.
  static final Locale nyanjaFlutterLocale = Locale(
    _nyanjaLocaleCode.toLowerCase(),
  );
  static final Locale englishFlutterLocale = Locale(
    _englishLanguageCode.toLowerCase(),
    _usCountryCode,
  );

  // All the RPC locales.
  static final rpc_locale.Locale englishRpcLocale = rpc_locale.Locale(
    code: _englishLocaleCode,
  );
  static final rpc_locale.Locale nyanjaRpcLocale = rpc_locale.Locale(
    code: _nyanjaLocaleCode,
  );

  // To get locale from SharedPreferences if exists.
  static Future<Locale> fetchCurrentLocale() async {
    final prefs = await SharedPreferences.getInstance();

    final String localeCode =
        prefs.getString(_localeCode) ?? _englishLocaleCode;

    if (localeCode.contains(_localeLanguageAndRegionSeparator)) {
      // Get the list of String separating languageCode and regionCode.
      final List<String> localeCodeSeparated = localeCode.split(
        _localeLanguageAndRegionSeparator,
      );
      final String languageCode = localeCodeSeparated[0];
      final String regionCode = localeCodeSeparated[1];
      return Locale(languageCode, regionCode);
    }

    return Locale(localeCode);
  }

  // To get RPC locale from SharedPreferences if exists.
  static Future<rpc_locale.Locale> fetchCurrentRPCLocale() async {
    return getRPCLocaleFromFlutterLocale(await fetchCurrentLocale());
  }

  /// A Method to check whether the current Flutter app [Locale] is same
  /// as the [rpc_locale.Locale].
  static bool isCurrentAppLocaleSameAsRPCLocale(
    BuildContext context,
    rpc_locale.Locale rpcLocale,
  ) {
    final localeCode = rpcLocale.code;
    late final String languageCode;
    if (localeCode.contains(_localeLanguageAndRegionSeparator)) {
      // Get the list of String separating languageCode and regionCode.
      final List<String> localeCodeSeparated = localeCode.split(
        _localeLanguageAndRegionSeparator,
      );
      languageCode = localeCodeSeparated[0];
    } else {
      languageCode = rpcLocale.code;
    }
    final Locale currentAppLocale = Localizations.localeOf(context);
    return languageCode.toLowerCase() ==
        currentAppLocale.languageCode.toLowerCase();
  }

  /// All the supported locales.
  static List<Locale> get supportedLocales => [
    englishFlutterLocale,
    nyanjaFlutterLocale,
  ];

  /// This method will save locale preferences in the [SharedPreferences]
  /// and change the locale across the whole app.
  static Future<void> _setNewLocale(Locale newLocale) async {
    final prefs = await SharedPreferences.getInstance();
    final String localeCode =
        newLocale.countryCode != null
            ? "${newLocale.languageCode}_${newLocale.countryCode}"
            : newLocale.languageCode;
    await prefs.setString(_localeCode, localeCode);

    // Using state to change the locale.
    mainAppKey.currentState!.locale = newLocale;
  }

  // Public method to change locale from anywhere in the app.
  /// Changes locale across the whole app and saves the locale
  /// preference in the [SharedPreferences].
  static Future<void> changeDefaultLocale(rpc_locale.Locale rpcLocale) async {
    final Locale flutterLocale = getFlutterLocaleFromRPCLocale(rpcLocale);
    await _setNewLocale(flutterLocale);
  }

  /// Get RPC based [rpc_locale.Locale] object from traditional Flutter [Locale].
  static rpc_locale.Locale getRPCLocaleFromFlutterLocale(Locale flutterLocale) {
    switch (flutterLocale.languageCode.toUpperCase()) {
      case _englishLanguageCode:
        return englishRpcLocale;
      case _nyanjaLocaleCode:
        return nyanjaRpcLocale;
      default:
        throw DeveloperError("Case $flutterLocale is handled");
    }
  }

  /// Get Flutter [Locale] object from the RPC locale [rpc_locale.Locale].
  static Locale getFlutterLocaleFromRPCLocale(rpc_locale.Locale rpcLocale) {
    switch (rpcLocale.code) {
      case _englishLocaleCode:
        return englishFlutterLocale;
      case _nyanjaLocaleCode:
        return nyanjaFlutterLocale;
      default:
        throw DeveloperError("Case $rpcLocale is handled");
    }
  }

  /// Get localised text based on the [LocalizedText] and
  /// the app's current locale.
  static String getLocalisedString(
    BuildContext context,
    LocalizedText localizedText,
  ) {
    final Locale currentLocale = Localizations.localeOf(context);
    late final String? resultString;
    switch (currentLocale.languageCode.toUpperCase()) {
      case _nyanjaLocaleCode:
        resultString = localizedText.ny?.replaceAll('\\n', '\n');
        break;
      case _englishLanguageCode:
        resultString = localizedText.en.replaceAll('\\n', '\n');
        break;
      default:
        throw DeveloperError(
          "Language '${currentLocale.languageCode}' is not supported by LocalizedText",
        );
    }

    // If the localizedText doesn't contain the text for the current locale of the app
    // then get the en locale text.
    return resultString ?? localizedText.en.replaceAll('\\n', '\n');
  }

  /// Get Image URL based on
  /// the [localizedImage], the current app theme and the
  /// current locale of the app.
  static String getLocalisedImageUrl(
    BuildContext context,
    LocalizedImage localizedImage,
  ) {
    final Locale currentLocale = Localizations.localeOf(context);
    ThemedImage? resultImage;
    switch (currentLocale.languageCode.toUpperCase()) {
      case _nyanjaLocaleCode:
        resultImage = localizedImage.ny;
        break;
      case _englishLanguageCode:
        resultImage = localizedImage.en;
        break;
      default:
        throw DeveloperError(
          "Language '${currentLocale.languageCode}' is not supported by LocalizedText",
        );
    }

    // If the resultImage is not found for the current locale of the app
    // then get the en locale image.
    resultImage ??= localizedImage.en;

    // If the resultImage doesn't have dark version or
    // the current app theme is not dark then store light theme
    // part of the ThemedImage.
    final MultiResolutionBitmapImage multiResolutionBitmapImage =
        (resultImage.dark != null && context.isDarkMode)
            ? resultImage.dark!
            : resultImage.light;
    return multiResolutionBitmapImage.getNetworkPhotoURL(context);
  }
}
