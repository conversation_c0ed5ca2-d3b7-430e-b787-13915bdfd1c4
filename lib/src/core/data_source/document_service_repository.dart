import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/mock_rpc_impls/mock_get_bcn_user_document_url_rpc_impl.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:dedwig/dedwig.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class DocumentServiceRepository {
  final AsyncAPIClient _apiClient = apiClient;
  final LeoClientToServerAuthenticationProvider _authenticationProvider =
      RPCAuthProvider.instance;

  Future<LeoRPCResult<GetUrlForSignedInUserResponse, Never>> getUrlForDocument(
    String sha256Photo,
    FileAttributesFileExtensionEnum fileExtension,
    int fileSize,
  ) async {
    final getUrlForSignedInUserRPCImpl = GetUrlForSignedInUserRPCImpl(
      client: _apiClient,
      authenticationProvider: _authenticationProvider,
    );
    final getAgentApplicationRequestsRequest = GetUrlForSignedInUserRequest(
      fileAttributes: FileAttributes(
        sha256: sha256Photo,
        fileExtension: fileExtension,
        fileSize: fileSize,
      ),
    );

    final getUrlForSignedInUseResponse = await getUrlForSignedInUserRPCImpl
        .execute(getAgentApplicationRequestsRequest);
    return getUrlForSignedInUseResponse;
  }

  Future<
    LeoRPCResult<
      GetDocumentIdForSignedInUserResponse,
      GetDocumentIdForSignedInUserError
    >
  >
  getDocumentId(String sha256Url) async {
    final getDocumentIdForSignedInUserRPCImpl =
        GetDocumentIdForSignedInUserRPCImpl(
          client: _apiClient,
          authenticationProvider: _authenticationProvider,
        );
    final getDocumentIdForSignedInUserRequest =
        GetDocumentIdForSignedInUserRequest(sha256: sha256Url);

    final getDocumentIdForSignedInUserResponse =
        await getDocumentIdForSignedInUserRPCImpl.execute(
          getDocumentIdForSignedInUserRequest,
        );
    return getDocumentIdForSignedInUserResponse;
  }

  Future<
    LeoRPCResult<GetBCNUserDocumentURLResponse, GetBCNUserDocumentURLError>
  >
  getDocumentUrlFromId(LeoUUID requestId, LeoUUID imageId) async {
    final getBCNUserDocumentURLRPCImpl =
        currentFlavor.isMock
            ? MockGetBCNUserDocumentURLRPCImpl()
            : GetBCNUserDocumentURLRPCImpl(
              client: _apiClient,
              authenticationProvider: _authenticationProvider,
            );
    final getDocumentIdForSignedInUserRequest = GetBCNUserDocumentURLRequest(
      agentApplicationId: requestId,
      documentId: imageId,
    );

    final response = await getBCNUserDocumentURLRPCImpl.execute(
      getDocumentIdForSignedInUserRequest,
    );
    return response;
  }
}
