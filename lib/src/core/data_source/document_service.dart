import 'dart:io';
import 'dart:typed_data';

import 'package:agency_banking_rpcs/document/file_attributes_type.dart';
import 'package:bcn_agency_banking_flutter/src/core/data_source/document_service_repository.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:crypto/crypto.dart';
import 'package:dedwig/dedwig.dart';
import 'package:http/http.dart' as http;
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class DocumentService {
  final _documentServiceRepository = DocumentServiceRepository();

  static const _documentUploadTimeout = 300000;

  /// Uploading photo has three steps procedure which includes
  /// two RPC calls and an HTTP call.
  /// This method should be called with the [rpcHandler]
  /// wrapper method.
  Future<LeoUUID> uploadPhoto(Photo photo) async {
    late final LeoUUID photoUUID;
    final sha256Photo = _getSHA256(photo.photo.readAsBytesSync());
    final rpcResult = await _documentServiceRepository.getUrlForDocument(
      sha256Photo,
      _getFileExtensions(photo.fileExtension)!,
      photo.size,
    );
    await rpcResult.when(
      response: (response) async {
        final Uri uploadDestinationURL = response.uploadDestinationURL;
        final uploadPhotoResponse = await _uploadPhotoToURL(
          uploadDestinationURL,
          photo.photo.readAsBytesSync(),
        );
        if (uploadPhotoResponse.statusCode == 200) {
          photoUUID = await _getPhotoUUID(sha256Photo);
        } else {
          throw DeveloperError("""
          Something went wrong while uploading image 
          Upload Image response : $uploadPhotoResponse
          """);
        }
      },
      error: (error) {
        // Error type Never is thrown.
        throw DeveloperError("$error");
      },
    );
    return photoUUID;
  }

  Future<Uri> getPhotoUrl(LeoUUID requestId, LeoUUID imageId) async {
    late Uri photoUrl;
    final rpcResult = await _documentServiceRepository.getDocumentUrlFromId(
      requestId,
      imageId,
    );
    rpcResult.when(
      response: (response) {
        photoUrl = response.documentURL;
      },
      error: (error) {
        throw error;
      },
    );
    return photoUrl;
  }

  Future<http.Response> _uploadPhotoToURL(
    Uri uploadDestinationURL,
    Uint8List photo,
  ) async {
    try {
      // TimeoutException will be thrown on timeout which is caught by the rpcHandler.
      final response = await http
          .put(
            uploadDestinationURL,
            body: photo,
            headers: {"Content-Type": "multipart/form-data"},
          )
          .timeout(_documentUploadTimeout.milliseconds);
      return response;
    } on SocketException catch (e) {
      // Throw NetworkException for internet issues.
      // This will be caught by the rpcHandler.
      throw NetworkException(e.toString());
    } on Error catch (e) {
      throw DeveloperError(e.toString());
    }
  }

  Future<LeoUUID> _getPhotoUUID(String sha256Photo) async {
    late LeoUUID photoUUID;
    final rpcResult = await _documentServiceRepository.getDocumentId(
      sha256Photo,
    );
    rpcResult.when(
      response: (response) {
        photoUUID = response.recordId;
      },
      error: (error) {
        throw error;
      },
    );
    return photoUUID;
  }

  String _getSHA256(Uint8List document) {
    final List<int> parsedDocument = List<int>.from(document);
    final Digest encryptedDocument = sha256.convert(parsedDocument);
    return encryptedDocument.toString();
  }

  FileAttributesFileExtensionEnum? _getFileExtensions(String fileExtension) {
    switch (fileExtension) {
      case ".png":
        return FileAttributesFileExtensionEnum.PNG();
      case ".jpg":
        return FileAttributesFileExtensionEnum.JPG();
      case ".pdf":
        return FileAttributesFileExtensionEnum.PDF();
      default:
        return null;
    }
  }
}
