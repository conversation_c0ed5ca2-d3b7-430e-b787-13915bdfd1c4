import 'dart:async';

import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/features/session_pin/enter_session_pin/enter_session_pin_screen.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/agency_location_error_dialog_route.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import './location_service.dart';
import '../../widgets/dialogs.dart';
import '../logger.dart';

part 'location_bloc.freezed.dart';

/// This BLOC is a global BLOC that masters the location service in the app.
/// A [LocationService.isLocationAccessibleStream] runs every [LocationService._updateLocationDuration]
/// seconds checking the device location service and app's location permission
/// and acts upon in it a desired way.
/// For example, if the location service is disabled by the user, the stream
/// will inform the [LocationBloc] that the service is disabled and the BLOC will act on it
/// by showing an [AgencyLocationErrorDialogRoute] which is non dismissible and can't be popped using
/// [Navigator.pop] method but using [Navigator.removeRoute] method.
///
/// [LocationBloc] has two events that checks for location.
/// One is called to let the BLOC to start the stream of location checking.
/// Another is a manual check to check location access requirement.
/// We usually do manual location check in a recursive manner.
/// For example,
/// If the user has disabled the location service and stream's cycle hits letting the BLOC
/// know that the service is disabled, [LocationBloc] shows a [_agencyServiceDialogRoute]
/// and starts a stream [_locationServiceSubscription] to let the BLOC know
/// whether the service is enabled or not and dismiss the [_agencyServiceDialogRoute], based on that.
/// Further, the BLOC in itself calls the manual check of location
/// and calls the [LocationEvent.checkAndSetLocation] event which further
/// re-verifies the location access and acts upon it.
///
/// Manual check event of [LocationEvent.checkAndSetLocation] is usually called in the following scenarios:
/// 1. When the user disables the service while the app is fetching the location coordinates as
/// the location fetching can take more than 1 seconds and we need to handle that as well.
/// 2. When the location check cycle tells that the service is disabled, and when the user enables the service,
/// we call this event to re-verify and try to fetch the location coordinates.
/// 3. When the app comes to foreground from background.
class LocationBloc extends Bloc<LocationEvent, LocationState> {
  // Stream that sends [LocationAccessRequirement].
  late final StreamSubscription<LocationAccessRequirement>?
  _locationStreamSubscription;
  final LocationService _locationService = locator<LocationService>();
  StreamSubscription<ServiceStatus>? _locationServiceSubscription;
  StreamSubscription? _navigationHistoryChangeStream;

  AgencyLocationErrorDialogRoute? _agencyServiceDialogRoute;
  AgencyLocationErrorDialogRoute? _agencyPermissionDialogRoute;

  static const permissionDialogRouteName = "location-permission-dialog";
  static const serviceDialogRouteName = "location-service-dialog";

  LocationBloc() : super(const LocationState.initial()) {
    on<LocationEvent>((event, emit) async {
      switch (event) {
        case CheckLocationWithinAcceptableRange(
          :final location,
          :final allowedDistance,
        ):
          emit(const LocationState.loading());
          final bool isLocationWithinAcceptableRange = _locationService
              .checkCurrentLocationWithinAcceptableRange(
                location: location,
                allowedDistance: allowedDistance,
              );
          emit(
            LocationState.onLocationRangeData(isLocationWithinAcceptableRange),
          );

        case StartLocationStream():
          l.d("Start Location Stream");
          _locationStreamSubscription = _locationService
              .isLocationAccessibleStream()
              .listen((locationAccessRequirement) async {
                l.d("Location Access Requirement: $locationAccessRequirement");
                await _evaluateLocationAccess(locationAccessRequirement);
              });

        case CheckAndSetLocation():
          // Only update the location if there is no location
          // update in progress.
          final LocationAccessRequirement locationAccessRequirement =
              await _locationService.isLocationAccessibleStream().first;

          await _evaluateLocationAccess(locationAccessRequirement);
      }
    });
  }

  // This method checks whether the location can be fetched or not.
  // This method is terminated after handling a situation of
  // location requirement not being met.
  // The order of priority of location checking is this:
  // 1. Service.
  // 2. Permission.
  //
  // For example,
  // If the service is not enabled, this method will show
  // an appropriate dialog for this and get terminated.
  // Why is it getting terminated?
  // To avoid piling up of a dialog over dialog in the app.
  // If in case, the user enables the service, we will call this method,
  // Since, the service is already enabled, this method will check
  // whether the permission is given or not.
  // If that too is satisfied, it will further go on to
  // fetch the location.
  Future<void> _evaluateLocationAccess(
    LocationAccessRequirement locationAccessRequirement,
  ) async {
    l.v("Evaluate Location Access");
    if (!locationAccessRequirement.isServiceEnabled) {
      l.d("location service is disabled");
      _listenToDeviceService();
      if (!(_agencyServiceDialogRoute?.isCurrent ?? false)) {
        final BuildContext? currentContext =
            NavigationService.navigatorKey.currentContext;

        // Remove any existing Service dialog route from the Navigation stack.
        final navigationContainsServiceDialogRoute =
            _doesNavigationStackContainRoute(_agencyServiceDialogRoute);
        final shouldRemoveServiceDialogRoute =
            _agencyServiceDialogRoute != null &&
            navigationContainsServiceDialogRoute;
        if (shouldRemoveServiceDialogRoute) {
          currentContext!.rootNavigator.removeRoute(_agencyServiceDialogRoute!);
        }
        _agencyServiceDialogRoute = _getServiceDialog(currentContext);
        if (!_isAppPinScreenOnTop()) {
          currentContext!.rootNavigator.push(_agencyServiceDialogRoute!);
          return;
        } else {
          // If `AppPinScreen` is on top of
          // the navigation stack, wait for the
          // user to get away from the `AppPinScreen`
          // and check for the Location access.
          _listenToNavigationRouteChanges();
        }
      } else {
        return;
      }
    } else if (locationAccessRequirement.isServiceEnabled &&
        _agencyServiceDialogRoute != null &&
        _doesNavigationStackContainRoute(_agencyServiceDialogRoute!)) {
      // Remove the Location service dialog if
      // the user has already enabled service on the device.
      final BuildContext? currentContext =
          NavigationService.navigatorKey.currentContext;
      currentContext!.rootNavigator.removeRoute(_agencyServiceDialogRoute!);
      _agencyServiceDialogRoute = null;
    }

    final bool shouldShowDialogForPermission =
        !locationAccessRequirement.isPermissionAccessible;
    l.d("Should Show Dialog for permission. $shouldShowDialogForPermission");
    // Avoid showing repetitive dialog after every cycle of location check.
    if (shouldShowDialogForPermission &&
        !(_agencyPermissionDialogRoute?.isCurrent ?? false)) {
      final BuildContext? currentContext =
          NavigationService.navigatorKey.currentContext;

      // Remove any existing Permission dialog route from the Navigation stack.
      final navigationContainsPermissionDialogRoute =
          _doesNavigationStackContainRoute(_agencyPermissionDialogRoute);
      final shouldRemovePermissionDialog =
          _agencyPermissionDialogRoute != null &&
          navigationContainsPermissionDialogRoute;
      if (shouldRemovePermissionDialog) {
        currentContext!.rootNavigator.removeRoute(
          _agencyPermissionDialogRoute!,
        );
      }
      _agencyPermissionDialogRoute = _getPermissionDialog(currentContext);
      if (!_isAppPinScreenOnTop()) {
        currentContext!.rootNavigator.push(_agencyPermissionDialogRoute!);
      } else {
        // If `AppPinScreen` is on top of
        // the navigation stack, wait for the
        // user to get away from the `AppPinScreen`
        // and check for the Location access.
        _listenToNavigationRouteChanges();
      }
    } else if (locationAccessRequirement.isPermissionAccessible &&
        _agencyPermissionDialogRoute != null &&
        _doesNavigationStackContainRoute(_agencyPermissionDialogRoute!)) {
      // Remove the Location permission dialog if
      // the user has already given the location permission.
      final BuildContext? currentContext =
          NavigationService.navigatorKey.currentContext;
      currentContext!.rootNavigator.removeRoute(_agencyPermissionDialogRoute!);
      _agencyPermissionDialogRoute = null;
    }

    if (locationAccessRequirement.canFetchLocation) {
      await _updateLocation();
    }
  }

  AgencyLocationErrorDialogRoute _getServiceDialog(
    BuildContext? currentContext,
  ) {
    return AgencyLocationErrorDialogRoute(
      context: currentContext!,
      builder:
          (context) => AgencyAppDialog.alertDialog(
            dialogContext: context,
            contentText: context.localizations.locationServiceNeeded,
            actions:
                (_) => [
                  PrimaryTextButton(
                    text: context.localizations.openSettings,
                    onTap: () async => Geolocator.openLocationSettings(),
                  ),
                ],
          ),
      dialogRouteName: serviceDialogRouteName,
    );
  }

  AgencyLocationErrorDialogRoute _getPermissionDialog(
    BuildContext? currentContext,
  ) {
    return AgencyLocationErrorDialogRoute(
      context: currentContext!,
      builder:
          (context) => AgencyAppDialog.alertDialog(
            dialogContext: context,
            contentText: context.localizations.locationPermissionNeeded,
            actions:
                (_) => [
                  PrimaryTextButton(
                    text: context.localizations.openSettings,
                    onTap: () async => await Geolocator.openAppSettings(),
                  ),
                ],
          ),
      dialogRouteName: permissionDialogRouteName,
    );
  }

  Future<void> _updateLocation() async {
    await _locationService.updateCurrentLocation();
  }

  void _listenToDeviceService() {
    l.d("Listening to device locations service");
    _locationServiceSubscription?.cancel();

    _locationServiceSubscription = _locationService
        .locationServiceStream()
        .listen((serviceStatus) {
          l.d("Service Status: $serviceStatus");
          final BuildContext currentContext =
              NavigationService.navigatorKey.currentContext!;
          if (serviceStatus == ServiceStatus.enabled) {
            if (_agencyServiceDialogRoute != null) {
              l.d("Removing Agency Service Dialog route");
              if (currentContext.mounted) {
                currentContext.rootNavigator.removeRoute(
                  _agencyServiceDialogRoute!,
                );
              }
            }
            _locationServiceSubscription?.cancel();
            _locationServiceSubscription = null;
            _agencyServiceDialogRoute = null;

            // If the location service is enabled by the user,
            // do a manual check of location again.
            add(const LocationEvent.checkAndSetLocation());
          }
        });
  }

  bool _isAppPinScreenOnTop() {
    final String? topRouteName = NavigationHistoryObserver().top?.settings.name;
    final bool isAppScreenOnTop =
        topRouteName == EnterSessionPinScreen.id ||
        topRouteName == EnterSessionPinScreen.launchId;
    l.d("IsAppPinScreenOnTop: $isAppScreenOnTop");
    return isAppScreenOnTop;
  }

  bool _doesNavigationStackContainRoute(Route? route) {
    final doesNavigationStackContainRoute = NavigationHistoryObserver().history
        .contains(route);
    l.d("Searching for route: $route");
    l.d("doesNavigationStackContainRoute: $doesNavigationStackContainRoute");
    return doesNavigationStackContainRoute;
  }

  void _listenToNavigationRouteChanges() {
    l.d("Listening to navigation route changes");
    _navigationHistoryChangeStream?.cancel();

    _navigationHistoryChangeStream = NavigationHistoryObserver()
        .historyChangeStream
        .listen((historyChangeEvent) {
          l.d(
            "Navigation History change event. historyChangeEvent: $historyChangeEvent",
          );
          final HistoryChange? historyChange =
              historyChangeEvent as HistoryChange?;
          final String? newRouteName = historyChange?.newRoute?.settings.name;
          final bool isNewRouteNotAppPinScreen =
              newRouteName != EnterSessionPinScreen.launchId &&
              newRouteName != EnterSessionPinScreen.id;
          l.d("isNewRouteNotAppPinScreen: $isNewRouteNotAppPinScreen");
          if (isNewRouteNotAppPinScreen) {
            add(const LocationEvent.checkAndSetLocation());
            _navigationHistoryChangeStream?.cancel();
            _navigationHistoryChangeStream = null;
          }
        });
  }

  @override
  Future<void> close() async {
    l.d("Closing Location Bloc");
    super.close();
    _locationStreamSubscription?.cancel();
    _locationServiceSubscription?.cancel();
  }
}

@freezed
sealed class LocationState with _$LocationState {
  const factory LocationState.initial() = Initial;

  const factory LocationState.loading() = Loading;

  const factory LocationState.onLocationRangeData(
    bool isLocationWithinAcceptableRange,
  ) = OnLocationRangeData;
}

@freezed
sealed class LocationEvent with _$LocationEvent {
  const factory LocationEvent.checkLocationWithinAcceptableRange(
    LocationDetails location,
    int allowedDistance,
  ) = CheckLocationWithinAcceptableRange;

  const factory LocationEvent.startLocationStream() = StartLocationStream;

  const factory LocationEvent.checkAndSetLocation() = CheckAndSetLocation;
}
