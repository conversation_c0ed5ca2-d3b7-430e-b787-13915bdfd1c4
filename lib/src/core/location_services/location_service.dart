import 'dart:async';

import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';

import '../logger.dart';
import '../navigation_service.dart';
import 'location_bloc.dart';

class LocationService {
  LocationDetails? _currentLocation;
  static const int _updateLocationDuration = 100;

  /// This value will hold default value of maximum
  /// radius distance the agent can be within to do any
  /// transaction. This can be modified only using the [locator]
  /// since it's static across the whole app. This value is updated
  /// from the GetHomeData RPC response.
  int agentMaxAllowedDistance = 300;

  LocationDetails? get getCurrentLocation => _currentLocation;

  Stream<LocationAccessRequirement> isLocationAccessibleStream() async* {
    while (true) {
      final isLocationPermissionGranted = await isPermissionGranted();
      final isServiceEnabled = await isLocationServiceEnabled();
      yield LocationAccessRequirement(
        isServiceEnabled,
        isLocationPermissionGranted,
      );
      await Future.delayed(const Duration(seconds: _updateLocationDuration));
    }
  }

  Future<Position> _getLocation() async {
    return await Geolocator.getCurrentPosition();
  }

  Future<bool> isLocationServiceEnabled() async =>
      await Geolocator.isLocationServiceEnabled();

  Future<bool> isPermissionGranted() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      try {
        permission = await Geolocator.requestPermission();
      } on PermissionRequestInProgressException catch (_) {
        // Don't do anything since a permission request is already going on.
      }
    }
    return permission == LocationPermission.whileInUse ||
        permission == LocationPermission.always;
  }

  Future<void> updateCurrentLocation() async {
    try {
      final Position deviceCurrentPosition = await _getLocation();
      l.d("Device Current position: $deviceCurrentPosition");
      final LocationDetails locationDetails = LocationDetails(
        latitude: deviceCurrentPosition.latitude,
        longitude: deviceCurrentPosition.longitude,
      );
      _currentLocation = locationDetails;
      // This Exception is usually thrown when we fetch the data in Background mode in iOS.
    } on PositionUpdateException catch (e) {
      l.d("PositionUpdateException: $e");
      return;
    } on LocationServiceDisabledException catch (e) {
      l.d("LocationServiceDisabledException: $e");
      final currentContext = NavigationService.navigatorKey.currentContext;
      if (currentContext != null && currentContext.mounted) {
        final locationBloc = BlocProvider.of<LocationBloc>(currentContext);
        locationBloc.add(const LocationEvent.checkAndSetLocation());
      }
    }
  }

  /// Get the distance in meters between two location coordinates.
  double getDistance(
    LocationDetails startLocation,
    LocationDetails endLocation,
  ) {
    return Geolocator.distanceBetween(
      startLocation.latitude,
      startLocation.longitude,
      endLocation.latitude,
      endLocation.longitude,
    );
  }

  /// Check whether current location of the device is
  /// within a radius of [allowedDistance] meter or not.
  /// [allowedDistance] defaults to [agentMaxAllowedDistance]
  /// which can be changed using [locator].
  bool checkCurrentLocationWithinAcceptableRange({
    required LocationDetails location,
    int? allowedDistance,
  }) {
    if (_currentLocation != null) {
      final double distance = getDistance(_currentLocation!, location);
      l.d("Distance between currentLocation and provided location: $distance");
      final double minimumPossibleDistance =
          distance - _currentLocation!.accuracy - location.accuracy;
      l.d("minimumPossibleDistance: $minimumPossibleDistance");
      return minimumPossibleDistance <=
          (allowedDistance ?? agentMaxAllowedDistance);
    } else {
      // This will never happen as the app is blocked when current location is null.
      l.e("Current location is null");
      throw DeveloperError("Current location cannot be null");
    }
  }

  /// Check whether the distance between two locations is within
  /// [allowedDistanceChangeInMeters]. Returns true if distance is less than or
  /// equal to [allowedDistanceChangeInMeters].
  bool checkLocationWithinAcceptableRange(
    LocationDetails location1,
    LocationDetails location2,
  ) {
    final double distance = Geolocator.distanceBetween(
      location1.latitude,
      location1.longitude,
      location2.latitude,
      location2.longitude,
    );
    return distance <= allowedDistanceChangeInMeters;
  }

  Stream<ServiceStatus> locationServiceStream() {
    return Geolocator.getServiceStatusStream();
  }
}

// Abstraction of things we required for fetching the location.
class LocationAccessRequirement {
  final bool isServiceEnabled;
  final bool isPermissionAccessible;

  LocationAccessRequirement(this.isServiceEnabled, this.isPermissionAccessible);

  bool get canFetchLocation => isServiceEnabled && isPermissionAccessible;

  @override
  String toString() =>
      "(LocationAccessRequirement isServiceEnabled: $isServiceEnabled, isPermissionAccessible: $isPermissionAccessible )";
}
