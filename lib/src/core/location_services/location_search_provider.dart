import 'package:bcn_agency_banking_flutter/src/core/app_config.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_search.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';
import 'dart:async';
import 'package:dedwig/dedwig.dart' as dedwig;
import 'package:bcn_agency_banking_flutter/src/core/temporary_logger.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';

class LocationSearchProvider {
  static const _searchUrl = "textsearch/json";
  static const _detailsUrl = "details/json";
  static const _placeIdKey = "placeid";
  static const _sessionTokenKey = "sessiontoken";
  static const _key = "key";
  static const _queryKey = "query";
  static const _timeout = 60000;

  final String _sessionToken = const Uuid().v4();
  static final _logLevelBasedOnFlavor =
      currentFlavor.isDev ? dedwig.LogLevel.debug : dedwig.LogLevel.none;
  final dedwig.AsyncAPIClient _apiClient = dedwig.AsyncAPIClient(
    configuration: dedwig.APIClientConfiguration(
      baseURL: Uri.parse(googlePlacesUrl),
      logConfiguration: dedwig.LogConfiguration(
        logger: TemporaryConsoleLogger(),
        requestBody: _logLevelBasedOnFlavor,
        requestMetadata: _logLevelBasedOnFlavor,
        responseBody: _logLevelBasedOnFlavor,
        responseMetadata: _logLevelBasedOnFlavor,
      ),
      timeout: _timeout,
    ),
  );

  Future<List<Prediction>> fetchSuggestions(String queryString) async {
    final PlacesTextSearchResponse placesTextSearchResponse = await _textSearch(
      queryString,
    );
    return placesTextSearchResponse.results;
  }

  Future<PlaceDetails?> fetchDetails(Prediction prediction) async {
    final PlacesDetailsResponse placesDetailsResponse =
        await _getDetailsByPlaceId(prediction.placeId!);
    if (placesDetailsResponse.isOkay) {
      return placesDetailsResponse.result;
    }
    return null;
  }

  Future<PlacesTextSearchResponse> _textSearch(String queryString) async {
    final response = await _apiClient.sendRequest(
      dedwig.Request(
        method: dedwig.Method.get,
        path: Uri.parse(_searchUrl),
        queryParameters: dedwig.QueryParameters(
          queryParameters: [
            dedwig.QueryParameter(_queryKey, queryString),
            dedwig.QueryParameter(_key, googleMapsApiKey),
          ],
        ),
      ),
    );
    return _decodeTextSearchResponse(response);
  }

  Future<PlacesDetailsResponse> _getDetailsByPlaceId(String placeId) async {
    final response = await _apiClient.sendRequest(
      dedwig.Request(
        method: dedwig.Method.get,
        path: Uri.parse(_detailsUrl),
        queryParameters: dedwig.QueryParameters(
          queryParameters: [
            dedwig.QueryParameter(_placeIdKey, placeId),
            dedwig.QueryParameter(_sessionTokenKey, _sessionToken),
            dedwig.QueryParameter(_key, googleMapsApiKey),
          ],
        ),
      ),
    );
    return _decodeDetailsResponse(response);
  }

  PlacesTextSearchResponse _decodeTextSearchResponse(
    dedwig.Response response,
  ) => PlacesTextSearchResponse.fromJson(json.decode(response.body));

  PlacesDetailsResponse _decodeDetailsResponse(dedwig.Response response) =>
      PlacesDetailsResponse.fromJson(json.decode(response.body));
}
