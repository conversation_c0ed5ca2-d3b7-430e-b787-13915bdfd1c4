class Logger {
  static final Logger _logger = Logger._internal();

  // Make Logger <PERSON>ton across the app.
  factory Logger() {
    return _logger;
  }

  Logger._internal();

  Future<void> initialiseLogger() async {}

  /// Log info.
  void i(String message) {}

  /// Log error.
  void e(String message) {}

  /// Log warning.
  void w(String message) {}

  /// Log debug.
  void d(String message) {}

  /// Log verbose.
  void v(String message) {}
}

final l = Logger();
