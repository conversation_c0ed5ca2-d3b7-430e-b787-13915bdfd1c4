import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/session_key_not_found_error.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// This class should not be used in any context other than passing
/// it as the auth provider for the RPCs we use.
/// Programmers on this codebase must use the [AuthProvider] class'
/// `instance` variable instead.
/// The getLLT is a public method used to access the LLT. It assumes an LLT is already stored.
class RPCAuthProvider extends LeoClientToServerAuthenticationProvider {
  static final instance = RPCAuthProvider._();

  RPCAuthProvider._();

  @override
  Future<String> getSLT() async => await _getStringForKey(_sltKey);

  @override
  Future<void> refreshSLT() async {
    final rpc = SignUpInRefreshSLTRPCImpl(client: apiClient);
    final llt = await getLLT();
    final request = SignUpInRefreshSLTRequest(llt: llt);
    final response = await rpc.execute(request);
    response.when(
      response: (response) async => await setSLT(response.slt),
      error: (e) {
        e.when(
          invalidLlt: (_) => throw LeoInvalidLLTException(e.code),
          userDisabled: (__) => throw LeoUserDisabledException(e.code),
        );
      },
    );
  }

  @override
  Future<void> setSLT(String value) async {
    final storage = await SharedPreferences.getInstance();
    final succeeded = await storage.setString(_sltKey, value);
    if (!succeeded) {
      throw RPCAuthProviderException("Unable to set SLT");
    }
  }

  Future<void> setLLT(String value) async {
    final storage = await SharedPreferences.getInstance();
    final succeeded = await storage.setString(_lltKey, value);
    if (!succeeded) {
      throw RPCAuthProviderException("Unable to set LLT");
    }
  }

  Future<String> getLLT() async => await _getStringForKey(_lltKey);

  Future<String> _getStringForKey(String key) async {
    final storage = await SharedPreferences.getInstance();
    String? keyStringFromStorage = storage.getString(key);
    if (keyStringFromStorage == null) {
      throw SessionKeyNotFoundError(
        "String for Session Key: $key, not found in local storage.",
      );
    }
    return keyStringFromStorage;
  }

  Future<void> deleteCredentials() async {
    final storage = await SharedPreferences.getInstance();
    storage.remove(_sltKey);
    storage.remove(_lltKey);
  }
}

class RPCAuthProviderException implements LeoRPCException {
  @override
  final String? message;

  RPCAuthProviderException(this.message) : super();

  String _getMessage() => message ?? "null";

  @override
  String toString() => "RPCAuthProviderException(\"${_getMessage()}\")";
}

const _sltKey = "com.resoluttech.agencybanking.SLTKey";
const _lltKey = "com.resoluttech.agencybanking.LLTKey";
