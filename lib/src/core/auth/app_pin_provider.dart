import 'package:flutter/services.dart';
import 'package:leo_flutter_ui/leo_flutter_ui.dart';
import 'package:local_auth/error_codes.dart' as local_auth_error_codes;
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/logger.dart';

class AppPinProvider {
  static final instance = AppPinProvider._();

  static const _initPreferences = EncryptedSharedPreferencesAuthHandler();

  AppPinProvider._();

  static const _appPinRemainingKey = "auth_handler.app_pin_remaining_attempts";

  static Future<void> setRemainingAppPinAttempts(int count) async {
    l.v("Setting Remaining AppPin Attempts to $count");
    final preferences = await SharedPreferences.getInstance();
    await preferences.setInt(_appPinRemainingKey, count);
  }

  static Future<int> getRemainingAppPinAttempts() async {
    final preferences = await SharedPreferences.getInstance();
    final int attemptsLeft = preferences.getInt(_appPinRemainingKey)!;
    l.v("Remaining App pin attempts: $attemptsLeft");
    return attemptsLeft;
  }

  static Future<void> deleteRemainingAppPinAttempts() async {
    l.v("Deleting remaining app pin attempts");
    final preferences = await SharedPreferences.getInstance();
    await preferences.remove(_appPinRemainingKey);
  }

  static Future<String?> getAppPin() async {
    return await _initPreferences.getAuthPin();
  }

  static Future<void> setAppPin(String appPin) async {
    l.v("Setting app pin.");
    return await _initPreferences.saveAuthPin(appPin);
  }

  static Future<void> deleteAppPin() async {
    return await _initPreferences.deleteAuthPin();
  }

  /// Check whether the device has hardware level
  /// compatibility for the biometrics.
  static Future<bool> canDeviceCheckBiometrics() async {
    final biometrics = LocalAuthentication();
    return await biometrics.canCheckBiometrics;
  }

  static Future<bool> isBiometricEnrolled() async {
    final biometrics = LocalAuthentication();
    final enrolledBiometrics = await biometrics.getAvailableBiometrics();
    l.v("isUserEnrolledToBiometrics: ${enrolledBiometrics.isNotEmpty}");
    return enrolledBiometrics.isNotEmpty;
  }

  /// Setting the authentication preference depending if the user has enabled
  /// the biometrics to bypass the application pin.
  /// This will prompt the user to verify themself using biometrics
  /// if [isEnabled] is true.
  static Future<void> setBiometricsAuthenticationPreference({
    required bool isEnabled,
    required String localisedReason,
    required VoidCallback onSuccess,
    VoidCallback? onLockedOut,
    VoidCallback? onAuthenticationCancelled,
  }) async {
    if (isEnabled) {
      await _authenticateWithBiometrics(
        onSuccess: () async {
          await _initPreferences.setBiometricsEnabled(isEnabled);
          onSuccess();
        },
        localizedReason: localisedReason,
        onLockedOut: onLockedOut,
        onAuthenticationCancelled: onAuthenticationCancelled,
        // Not authenticating is as go
        onNotAuthenticated: onAuthenticationCancelled,
      );
    } else {
      await _initPreferences.setBiometricsEnabled(isEnabled);
      onSuccess();
    }
  }

  static Future<void> checkBiometricsAndAuthenticate({
    required void Function() onSuccess,
    required String localisedReason,
    VoidCallback? onLockedOut,
    VoidCallback? onCancelled,
    VoidCallback? onPermanentlyLockedOut,
    VoidCallback? onBiometricsNotEnrolled,
  }) async {
    final bool? isBiometricsEnabled =
        await _initPreferences.isBiometricEnabled();
    if (isBiometricsEnabled == true) {
      await _authenticateWithBiometrics(
        onSuccess: onSuccess,
        localizedReason: localisedReason,
        onLockedOut: onLockedOut,
        onAuthenticationCancelled: onCancelled,

        /// Not authenticating is as good as cancelling
        /// the authentication.
        onNotAuthenticated: onCancelled,
        onPermanentlyLockedOut: onPermanentlyLockedOut,
        onBiometricsNotEnrolled: onBiometricsNotEnrolled,
      );
    }
  }

  static Future<void> resetBiometricsPreferences() async {
    await _initPreferences.setBiometricsEnabled(false);
  }

  static Future<void> _authenticateWithBiometrics({
    required VoidCallback onSuccess,
    required String localizedReason,
    VoidCallback? onLockedOut,
    VoidCallback? onAuthenticationCancelled,
    VoidCallback? onNotAuthenticated,
    VoidCallback? onPermanentlyLockedOut,
    VoidCallback? onBiometricsNotEnrolled,
  }) async {
    try {
      final authenticated = await _getBiometricsAuthentication(localizedReason);
      if (authenticated) {
        l.d("User is authenticated on biometrics");
        onSuccess();
      } else {
        l.d("User is not authenticated on biometrics");
        onNotAuthenticated?.call();
      }
    } on PlatformException catch (e) {
      final bool shouldCancelAuthentication =
          e.code == local_auth_error_codes.notAvailable;
      final isLockedOut = e.code == local_auth_error_codes.lockedOut;
      final isPermanentlyLockedOut =
          e.code == local_auth_error_codes.permanentlyLockedOut;
      final isBiometricsNotEnrolled =
          e.code == local_auth_error_codes.notEnrolled;
      if (isLockedOut) {
        l.d("User is locked out");
        onLockedOut?.call();
      } else if (shouldCancelAuthentication) {
        l.d("User cancelled authentication");
        onAuthenticationCancelled?.call();
      } else if (isPermanentlyLockedOut) {
        l.d("User permanently locked out");
        onPermanentlyLockedOut?.call();
      } else if (isBiometricsNotEnrolled) {
        l.d("User is not biometrics enrolled");
        onBiometricsNotEnrolled?.call();
      } else {
        l.e("An Unhandled Biometrics error came up: $e");
        rethrow;
      }
    }
  }

  static Future<bool> _getBiometricsAuthentication(
    String localizedReason,
  ) async {
    final biometrics = LocalAuthentication();
    return await biometrics.authenticate(
      localizedReason: localizedReason,
      options: const AuthenticationOptions(
        biometricOnly: true,
        stickyAuth: false,
        useErrorDialogs: true,
      ),
    );
  }

  static Future<bool> isAppPinSetup() async {
    final appPin = await getAppPin();
    return appPin != null && appPin.isNotEmpty;
  }
}
