import 'package:bcn_agency_banking_flutter/src/core/auth/rpc_auth_provider.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// This class supersedes [RPCAuthProvider].
/// Use this class to keep track of when the user signs in, whether they are
/// signed in, and notify when the user signs out.
/// This class lets you determine the type of signed in user also.
class AuthProvider {
  static final instance = AuthProvider._();

  AuthProvider._();

  Future<void> setCredentials({
    required String slt,
    required String llt,
    required UserType userType,
  }) async {
    final storage = await SharedPreferences.getInstance();
    storage.setString(_userTypeKey, userType.name);
    await RPCAuthProvider.instance.setSLT(slt);
    await RPCAuthProvider.instance.setLLT(llt);
  }

  Future<String> getLLT() async => await RPCAuthProvider.instance.getLLT();

  /// Returns the type of user signed in so that
  /// UI can make choices about which screen to show.
  /// A `null` value being returned implies that the user is not
  /// signed in yet.
  Future<UserType?> getSignedInUserType() async {
    // Here, we don't check for SLT or LLT being present.
    // Though this may be tempting, that is not the job of this method.
    // If indeed we have a developer error where SLT/LLT were not set but the
    // user type was present, an RPC calling for SLT
    // will end up crashing and we will identify a developer error.
    final storage = await SharedPreferences.getInstance();
    final possibleUserType = storage.getString(_userTypeKey);
    if (possibleUserType == null) {
      return null;
    }
    return _getUserType(possibleUserType);
  }

  /// Clears all credentials stored about the user, including
  /// those stored in [RPCAuthProvider].
  Future<void> clearCredentials() async {
    final storage = await SharedPreferences.getInstance();
    storage.remove(_userTypeKey);
    await RPCAuthProvider.instance.deleteCredentials();
  }
}

enum UserType { agent, agentManager }

UserType _getUserType(String candidate) {
  if (candidate == UserType.agent.name) {
    return UserType.agent;
  } else if (candidate == UserType.agentManager.name) {
    return UserType.agentManager;
  } else {
    // Candidate string is not known
    throw LeoIllegalStateException(
      "Candidate string `$candidate` is not a known `UserType`.",
    );
  }
}

const _userTypeKey = "com.resoluttech.agencybanking.UserTypeKey";
