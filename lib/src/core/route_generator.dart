import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/application_status/agent_application_status_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/create_agent_application/bloc/submit_application_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/create_agent_application/enter_shop_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/agent_phone_number_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_sign_in/bloc/agent_sign_in_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/blocs/cash_out_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/cash_out/cash_out_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/blocs/commissions_breakdown_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/blocs/completed_commission_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/commission/commissions_breakdown_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/accept_request/accept_request_otp_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/active_requests_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/active_requests_sender_request_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/blocs/active_requests_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/active_requests/blocs/active_requests_sender_request_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/create_request_landing_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/mt_recipient_details_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/mt_sender_details_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/blocs/mt_sender_verification_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/create_request/mt_create_request_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/customer_refunds_listing_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/customer_refunds/customer_refunds_otp_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/money_transfer/money_transfer_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/profile/change_session_pin/change_session_pin_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/profile/profile_information/profile_information_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/profile/third_party_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/agent_application_request_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/agent_application_requests_list_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/bloc/agent_application_requests_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/bloc/edit_shop_details_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/bloc/evaluate_agent_application_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_application_requests/edit_shop_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_manager_home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/refund_amount/blocs/refund_amount_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/agent_manager_phone_number_password_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/bloc/sign_in_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/bloc/submit_reset_password_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/sign_in/submit_reset_password_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/blocked_refund_request_details_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/unblock_refunds/blocked_refund_requests_list_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/landing_screen/bloc/landing_screen_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/features/maps/location_picker_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/session_pin/setup_session_pin/setup_session_pin_screen.dart';
import 'package:bcn_agency_banking_flutter/src/models/cash_transactions_arguments.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/shop_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/transaction_success_screen_arguments.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/transaction_success_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../errors/developer_error.dart';
import '../features/agent/agent_setup/application_status/bloc/agent_application_bloc.dart';
import '../features/agent/cash_in/blocs/cash_in_bloc.dart';
import '../features/agent/cash_in/cash_in_screen.dart';
import '../features/agent/home_screen.dart';
import '../features/agent/money_transfer/accept_request/accept_request_landing_screen.dart';
import '../features/agent/money_transfer/accept_request/blocs/accept_request_bloc.dart';
import '../features/agent/money_transfer/accept_request/blocs/accept_request_otp_bloc.dart';
import '../features/agent/money_transfer/customer_refunds/blocs/customer_refunds_listing_bloc.dart';
import '../features/agent/money_transfer/customer_refunds/blocs/customer_refunds_otp_bloc.dart';
import '../features/agent/profile/app_info_screen.dart';
import '../features/agent/profile/blocs/change_language_bloc.dart';
import '../features/agent/profile/blocs/change_session_pin_bloc.dart';
import '../features/agent/profile/blocs/manage_wallet_bloc.dart';
import '../features/agent/profile/change_language_screen.dart';
import '../features/agent/profile/manage_wallet_screen.dart';
import '../features/agent/profile/privacy_policy_screen.dart';
import '../features/agent/profile/profile_information/shop_location_map_preview.dart';
import '../features/agent_manager/refund_amount/refund_amount_details_screen.dart';
import '../features/agent_manager/refund_amount/refund_amount_request_screen.dart';
import '../features/agent_manager/unblock_refunds/blocs/blocked_refund_requests_bloc.dart';
import '../features/agent_manager/unblock_refunds/blocs/unblock_mt_refund_request_bloc.dart';
import '../features/landing_screen/landing_screen.dart';
import '../features/session_pin/enter_session_pin/enter_session_pin_screen.dart';
import '../models/submit_reset_password_screen_arguments.dart';

class Routes {
  Routes._();

  static String initialRoute = LandingScreen.id;

  static Route<dynamic>? generateRoute(RouteSettings settings) {
    final args = settings.arguments;

    switch (settings.name) {
      case HomeScreen.id:
        return getMaterialRoute(HomeScreen.id, const HomeScreen());
      case CashInScreen.id:
        if (args is CashTransactionArguments) {
          return getMaterialRoute(
            CashInScreen.id,
            BlocProvider(
              create:
                  (context) => CashInBloc(
                    defaultCurrency: args.defaultCurrency,
                    shopCoordinate: args.shopCoordinate,
                  ),
              child: const CashInScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case CashOutScreen.id:
        if (args is CashTransactionArguments) {
          return getMaterialRoute(
            CashOutScreen.id,
            BlocProvider(
              create:
                  (context) => CashOutBloc(
                    defaultCurrency: args.defaultCurrency,
                    shopCoordinate: args.shopCoordinate,
                  ),
              child: const CashOutScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case AgentManagerHomeScreen.id:
        return getMaterialRoute(
          AgentManagerHomeScreen.id,
          const AgentManagerHomeScreen(),
        );
      case AgentApplicationRequestsListScreen.id:
        return getMaterialRoute(
          AgentApplicationRequestsListScreen.id,
          BlocProvider(
            create:
                (context) =>
                    AgentApplicationsRequestsBloc()..add(
                      const AgentApplicationsRequestsEvent.getApplications(),
                    ),
            child: const AgentApplicationRequestsListScreen(),
          ),
        );
      case AgentApplicationRequestDetailsScreen.id:
        if (args is AgentApplication) {
          return getMaterialRoute(
            AgentManagerHomeScreen.id,
            BlocProvider<EvaluateAgentApplicationBloc>(
              lazy: false,
              create:
                  (context) => EvaluateAgentApplicationBloc(application: args),
              child: const AgentApplicationRequestDetailsScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case BlockedRefundRequestsListScreen.id:
        return getMaterialRoute(
          BlockedRefundRequestsListScreen.id,
          BlocProvider(
            create:
                (context) =>
                    BlockedRefundRequestsBloc()..add(
                      const BlockedRefundRequestsEvent.getBlockedRefundRequests(),
                    ),
            child: const BlockedRefundRequestsListScreen(),
          ),
        );
      case BlockedRefundRequestDetailsScreen.id:
        if (args is BlockedMoneyTransferRequest) {
          return getMaterialRoute(
            BlockedRefundRequestDetailsScreen.id,
            BlocProvider<UnblockMoneyTransferRefundRequestBloc>(
              create: (context) => UnblockMoneyTransferRefundRequestBloc(),
              child: BlockedRefundRequestDetailsScreen(
                blockedRefundRequest: args,
              ),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case MoneyTransferScreen.id:
        if (args is CashTransactionArguments) {
          return getMaterialRoute(
            MoneyTransferScreen.id,
            MoneyTransferScreen(cashTransactionArguments: args),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }

      case MTCreateRequestScreen.id:
        if (args is CashTransactionArguments) {
          return getMaterialRoute(
            MTCreateRequestScreen.id,
            MultiBlocProvider(
              providers: [
                BlocProvider<CreateRequestLandingBloc>(
                  create:
                      (BuildContext context) => CreateRequestLandingBloc(
                        defaultCurrency: args.defaultCurrency,
                        shopCoordinate: args.shopCoordinate,
                      ),
                ),
                BlocProvider<MTSenderVerificationBloc>(
                  create: (BuildContext context) => MTSenderVerificationBloc(),
                ),
                BlocProvider<MTSenderDetailsBloc>(
                  create: (BuildContext context) => MTSenderDetailsBloc(),
                ),
                BlocProvider<MTRecipientDetailsBloc>(
                  create: (BuildContext context) => MTRecipientDetailsBloc(),
                ),
              ],
              child: const MTCreateRequestScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }

      case TransactionSuccessScreen.id:
        if (args is TransactionSuccessScreenArguments) {
          return getMaterialRoute(
            TransactionSuccessScreen.id,
            TransactionSuccessScreen(
              transactionStatusDetail: args.transactionStatusDetail,
              succeededAt: args.succeededAt,
              amount: args.amount,
              recordId: args.recordId,
              onDone: args.onDone,
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case AcceptRequestLandingScreen.id:
        if (args is CashTransactionArguments) {
          return getMaterialRoute(
            AcceptRequestLandingScreen.id,
            BlocProvider(
              create:
                  (context) => AcceptRequestBloc(
                    shopCoordinate: args.shopCoordinate,
                    defaultCurrency: args.defaultCurrency,
                  ),
              child: const AcceptRequestLandingScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case AcceptRequestOTPScreen.id:
        if (args is AcceptRequestOTPScreenArguments) {
          return getMaterialRoute(
            AcceptRequestOTPScreen.id,
            MultiBlocProvider(
              providers: [
                BlocProvider.value(value: args.acceptRequestBloc),
                BlocProvider(
                  create:
                      (context) => AcceptRequestOTPBloc(
                        shopCoordinate: args.shopCoordinate,
                      ),
                ),
              ],
              child: const AcceptRequestOTPScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case EditShopDetailsScreen.id:
        if (args is ShopDetails) {
          return getMaterialRoute(
            EditShopDetailsScreen.id,
            BlocProvider(
              create: (context) => EditShopDetailsBloc(),
              child: EditShopDetailsScreen(shopDetails: args),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case ActiveRequestsScreen.id:
        if (args is CashTransactionArguments) {
          return getMaterialRoute(
            ActiveRequestsScreen.id,
            BlocProvider(
              create:
                  (context) =>
                      ActiveRequestsBloc()
                        ..add(const ActiveRequestsEvent.fetchRequests()),
              child: ActiveRequestsScreen(shopCoordinates: args.shopCoordinate),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }

      case LocationPickerScreen.id:
        return getMaterialRoute(
          LocationPickerScreen.id,
          LocationPickerScreen(locationDetails: args as LocationDetails?),
        );
      case EnterSessionPinScreen.id:
        final enterSessionPinArgs = args as EnterSessionPinScreenArguments?;
        return getMaterialRoute(
          EnterSessionPinScreen.id,
          EnterSessionPinScreen(
            isCancellable: enterSessionPinArgs?.isCancellable ?? true,
            onSignOut: enterSessionPinArgs?.onSignOut,
            onSuccessfulAuthentication:
                enterSessionPinArgs?.onSuccessfulAuthentication,
          ),
        );

      case ActiveRequestsSenderRequestScreen.id:
        if (args is ActiveRequestsSenderRequestScreenArguments) {
          return getMaterialRoute(
            ActiveRequestsSenderRequestScreen.id,
            BlocProvider(
              create:
                  (context) => ActiveRequestsSenderRequestBloc(
                    shopCoordinates: args.shopCoordinates,
                    activeRequestInfo: args.activeRequestInfo,
                  ),
              child: const ActiveRequestsSenderRequestScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args is not provided or are invalid.");
        }
      case LandingScreen.id:
        return getMaterialRoute(
          LandingScreen.id,
          BlocProvider<LandingScreenBloc>(
            create:
                (context) =>
                    LandingScreenBloc()
                      ..add(LandingScreenEvent.getSupportedCountries(context)),
            child: const LandingScreen(),
          ),
        );
      case AgentManagerPhoneNumberPasswordScreen.id:
        return getMaterialRoute(
          AgentManagerPhoneNumberPasswordScreen.id,
          BlocProvider(
            create: (context) => SignInBloc(),
            child: const AgentManagerPhoneNumberPasswordScreen(),
          ),
        );
      case SubmitResetPasswordScreen.id:
        if (args is SubmitResetPasswordScreenArguments) {
          return getMaterialRoute(
            SubmitResetPasswordScreen.id,
            BlocProvider(
              create: (context) => SubmitResetPasswordBloc(),
              child: SubmitResetPasswordScreen(
                submitResetPasswordScreenArguments: args,
              ),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }

      case CommissionsBreakdownScreen.id:
        if (args is CompletedCommissionBloc) {
          return getMaterialRoute(
            CommissionsBreakdownScreen.id,
            MultiBlocProvider(
              providers: [
                BlocProvider.value(value: args),
                BlocProvider(
                  create:
                      (context) =>
                          CommissionsBreakdownBloc()..add(
                            CommissionsBreakdownEvent.fetchBreakdownList(
                              args.currentCompletedTransactionRecordId,
                              args.currentTotalCommissionAmount,
                            ),
                          ),
                ),
              ],
              child: const CommissionsBreakdownScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case CustomerRefundsListingScreen.id:
        if (args is CashTransactionArguments) {
          return getMaterialRoute(
            CustomerRefundsListingScreen.id,
            BlocProvider(
              create:
                  (context) =>
                      CustomerRefundsListingBloc()..add(
                        const CustomerRefundsListingEvent.fetchRequests(),
                      ),
              child: CustomerRefundsListingScreen(
                shopCoordinate: args.shopCoordinate,
              ),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case CustomerRefundsOTPScreen.id:
        if (args is CustomerRefundsOTPScreenArguments) {
          return getMaterialRoute(
            CustomerRefundsOTPScreen.id,
            BlocProvider(
              create:
                  (context) => CustomerRefundsOTPBloc(
                    shopCoordinate: args.shopCoordinate,
                    response: args.moneyTransferRefundRequest,
                  ),
              child: const CustomerRefundsOTPScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case AMRefundAmountRequestScreen.id:
        return getMaterialRoute(
          AMRefundAmountRequestScreen.id,
          BlocProvider(
            create: (context) => RefundAmountBloc(),
            child: const AMRefundAmountRequestScreen(),
          ),
        );
      case AMRefundAmountDetailsScreen.id:
        if (args is RefundAmountBloc) {
          return Routes.getMaterialRoute(
            AMRefundAmountDetailsScreen.id,
            BlocProvider.value(
              value: args,
              child: const AMRefundAmountDetailsScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case AgentPhoneNumberScreen.id:
        return getMaterialRoute(
          AgentPhoneNumberScreen.id,
          BlocProvider(
            create: (context) => AgentSignInBloc(),
            child: const AgentPhoneNumberScreen(),
          ),
        );
      case AgentApplicationStatusScreen.id:
        return getMaterialRoute(
          AgentApplicationStatusScreen.id,
          BlocProvider(
            create:
                (context) =>
                    AgentApplicationBloc()..add(
                      AgentApplicationEvent.getApplicationStatus(context),
                    ),
            child: const AgentApplicationStatusScreen(),
          ),
        );
      case SetupSessionPinScreen.id:
        if (args is bool?) {
          return getMaterialRoute(
            SetupSessionPinScreen.id,
            const SetupSessionPinScreen(),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case ProfileInformationScreen.id:
        if (args is ProfileInformationScreenArguments) {
          return getMaterialRoute(
            ProfileInformationScreen.id,
            ProfileInformationScreen(
              profileData: args.profileData,
              locationDetails: args.locationDetails,
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case EnterShopDetailsScreen.id:
        return getMaterialRoute(
          EnterShopDetailsScreen.id,
          BlocProvider(
            create: (context) => SubmitApplicationBloc(),
            child: const EnterShopDetailsScreen(),
          ),
        );
      case ManageWalletScreen.id:
        if (args is GetAgentProfileDataResponse) {
          return getMaterialRoute(
            ManageWalletScreen.id,
            BlocProvider(
              create: (context) => ManageWalletBloc(args.agentAccount),
              child: ManageWalletScreen(profileData: args),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case PrivacyPolicyScreen.id:
        return getMaterialRoute(
          PrivacyPolicyScreen.id,
          const PrivacyPolicyScreen(),
        );
      case ShopLocationMapPreview.id:
        if (args is ShopLocationMapPreviewArguments) {
          return getMaterialRoute(
            ShopLocationMapPreview.id,
            ShopLocationMapPreview(
              shopLocationDetails: args.shopLocationDetails,
              shopName: args.shopName,
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case ChangeSessionPinScreen.id:
        return getMaterialRoute(
          ChangeSessionPinScreen.id,
          BlocProvider(
            create:
                (context) =>
                    ChangeSessionPinBloc()
                      ..add(ChangeSessionPinEvent.initialise(context)),
            child: const ChangeSessionPinScreen(),
          ),
        );
      case ChangeLanguageScreen.id:
        if (args is List<LocaleInformation>) {
          return getMaterialRoute(
            ChangeLanguageScreen.id,
            BlocProvider(
              create:
                  (context) =>
                      ChangeLanguageBloc(args)
                        ..add(ChangeLanguageEvent.getCurrentLocale(context)),
              child: const ChangeLanguageScreen(),
            ),
          );
        } else {
          throw DeveloperError("Args not provided or are invalid.");
        }
      case ThirdPartySoftwareScreen.id:
        return getMaterialRoute(
          ThirdPartySoftwareScreen.id,
          const ThirdPartySoftwareScreen(),
        );
      case AppInfoScreen.id:
        return getMaterialRoute(AppInfoScreen.id, const AppInfoScreen());
    }
    return null;
  }

  // It is public so that it can be used by nested navigators.
  static MaterialPageRoute getMaterialRoute(String routeName, Widget screen) {
    return MaterialPageRoute(
      settings: RouteSettings(name: routeName),
      builder: (context) => screen,
    );
  }
}
