import 'package:bcn_agency_banking_flutter/src/core/data_source/app_data_source.dart';
import 'package:bcn_agency_banking_flutter/src/core/data_source/document_service.dart';
import 'package:dedwig/dedwig.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:get_it/get_it.dart';

final GetIt locator = GetIt.I;

/// API Client which can be accessed anywhere in the app.
AsyncAPIClient get apiClient => locator<AppDataSource>().apiClient;

void setupLocator() {
  locator.registerLazySingleton(() => LocationService());
  locator.registerLazySingleton(() => DocumentService());
}
