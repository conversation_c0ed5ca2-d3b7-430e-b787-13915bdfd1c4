// Silencing the print warnings since we want to print on the console
// in the absence of a more formal logging process.
// ignore_for_file: avoid_print
// Temporary logger until this is moved into Dedwig.
import 'package:dedwig/dedwig.dart';

class TemporaryConsoleLogger extends Logger {
  TemporaryConsoleLogger();

  @override
  void logDebug(String Function() message, {Exception? exception}) {
    print(message());
  }

  @override
  void logError(String Function() message, {Exception? exception}) {
    print(message());
  }

  @override
  void logInfo(String Function() message, {Exception? exception}) {
    print(message());
  }

  @override
  void logWarn(String Function() message, {Exception? exception}) {
    print(message());
  }
}
