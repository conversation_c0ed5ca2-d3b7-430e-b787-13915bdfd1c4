import 'dart:io';

import 'package:native_flutter_proxy/native_flutter_proxy.dart';

/// Call this method at app startup to have Dart
/// respect the device's proxy settings if there are any.
Future<void> setupNetworkProxy() async {
  ProxySetting settings = await NativeProxyReader.proxySetting;

  bool isProxyEnabled = settings.enabled;
  String? proxyHost = settings.host;
  int? proxyPort = settings.port;

  if (isProxyEnabled && proxyHost != null && proxyPort != null) {
    HttpOverrides.global = _ProxiedHttpOverrides(proxyHost, proxyPort);
  } else {
    // If a proxy is not found, we don't do anything and it's safe to return.
    return;
  }
}

class _ProxiedHttpOverrides extends HttpOverrides {
  final int _port;
  final String _host;

  _ProxiedHttpOverrides(this._host, this._port);

  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..findProxy = (uri) {
        return "PROXY $_host:$_port;";
      }
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
