import 'dart:async';
import 'dart:convert';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:async/async.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/resources/agency_banking_theme.dart';
import 'package:bcn_agency_banking_flutter/src/resources/app_colors.dart';
import 'package:bcn_agency_banking_flutter/src/resources/app_text_styles.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/amount_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/phone_number_formatter.dart';
import 'package:flutter/material.dart';
import 'package:bcn_agency_banking_flutter/i18n/generated/app_localizations.dart';
import 'package:geolocator/geolocator.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

import '../models/location_details.dart';
import '../resources/app_icons.dart';

extension ContextX on BuildContext {
  NavigatorState get navigator => Navigator.of(this);

  /// Root Navigator will be used in case we are using Nested Navigator.
  NavigatorState get rootNavigator => Navigator.of(this, rootNavigator: true);

  MediaQueryData get mq => MediaQuery.of(this);

  AppLocalizations get localizations => AppLocalizations.of(this);

  bool get isDarkMode => mq.platformBrightness == Brightness.dark;

  /// Flutter defined theme
  ThemeData get theme => Theme.of(this);

  /// Custom App based theme
  ABTheme get appTheme => AgencyBankingTheme.of(this);

  AppIcons get appIcons => appTheme.icons;

  AppTextStyles get appTextStyles => appTheme.appTextStyles;

  AppColors get appColors => appTheme.appColors;

  bool get mounted {
    try {
      widget;
      return true;
    } catch (e) {
      return false;
    }
  }
}

extension NetworkPhoto on MultiResolutionBitmapImage {
  /// Returns a `String` that's a URL to be used
  /// inside a Network Image Widget.
  /// This method gives you the right image for the density of the
  /// display.
  String getNetworkPhotoURL(BuildContext context) {
    // Thanks: https://stackoverflow.com/a/********
    final queryData = context.mq;
    final double devicePixelRatio = queryData.devicePixelRatio;

    if (devicePixelRatio < _1x) {
      return mdpi.imageURL.toString();
    } else if (devicePixelRatio.isBetween(_1x, _2x)) {
      return xhdpi.imageURL.toString();
    } else if (devicePixelRatio.isBetween(_2x, _3x)) {
      return xxhdpi.imageURL.toString();
    } else if (devicePixelRatio.isBetween(_3x, _4x)) {
      return xxxhdpi.imageURL.toString();
    } else {
      // Anything larger than 4.0x will have to fall back
      // onto 4.0x--our largest size asset available.
      return xxxhdpi.imageURL.toString();
    }
  }
}

// Thanks: https://stackoverflow.com/a/64226842
extension Range on num {
  bool isBetween(num from, num to) {
    return from < this && this < to;
  }
}

// Silencing the linter since I am sure these
// are the names I want for my constants but the linter
// doesn't like that.
// ignore_for_file: constant_identifier_names
const _1x = 1.0;
const _2x = 2.0;
const _3x = 3.0;
const _4x = 4.0;

extension AmountX on Amount {
  String get localisedFormattedAmount =>
      AmountFormatter.getLocaleFormattedAmountFromRPCAmount(
        amount,
        currency.currencyCode,
      );

  double get actualAmount => AmountFormatter.getActualAmount(amount);

  bool get isZero => amount == 0;
}

extension IntX on int {
  Duration get minutes => Duration(minutes: this);

  Duration get seconds => Duration(seconds: this);

  Duration get milliseconds => Duration(milliseconds: this);

  Duration get days => Duration(days: this);

  int get rpcAmount => AmountFormatter.toRPCAmount(this);
}

extension DoubleX on double {
  int get rpcAmount => AmountFormatter.toRPCAmount(this);
}

extension StringX on String {
  // Thanks: https://github.com/flutter/flutter/issues/18761#issuecomment-812390920
  /// This overflow can be used for ellipsis string text.
  String get overflow =>
      Characters(
        this,
      ).replaceAll(Characters(''), Characters('\u{200B}')).toString();

  bool get isValidOtp => length >= 6;

  bool get isValidNationalIdNumber => (isNotEmpty && length <= 20);

  bool get isValidRequestId => (isNotEmpty && length <= 20);

  bool get isValidName => (isNotEmpty && length <= 200);

  bool get isValidAddress => (isNotEmpty && length <= 400);

  String removeSpaces() => replaceAll(" ", "");

  int get utf8Length => utf8.encode(this).length;
}

extension FlavorX on AppFlavor {
  bool get isDev => this is DevFlavor;

  bool get isMock => this is MockFlavor;

  bool get isUAT => this is UatFlavor;

  bool get isProd => this is ProdFlavor;
}

extension CoordinateX on Coordinate {
  /// Get the [LocationDetails] object from [Coordinate].
  LocationDetails get locationDetails =>
      LocationDetails(latitude: latitude, longitude: longitude);
}

extension PositionX on Position {
  /// Get the [LocationDetails] object from [Position].
  LocationDetails get locationDetails =>
      LocationDetails(latitude: latitude, longitude: longitude);
}

extension DateTimeX on DateTime {
  /// Check if the date is today or not
  bool get isToday =>
      DateTime.now().onlyDate.difference(onlyDate.toLocal()).inDays == 0;

  bool get isYesterday =>
      DateTime.now().onlyDate.difference(onlyDate.toLocal()).inDays == 1;

  /// Get only the date and remove the time.
  DateTime get onlyDate => DateTime(year, month, day);

  /// Get the first date of the given date's cycle
  DateTime get firstDayOfCycle => DateTime(year, month, 1);

  /// Check if the date is within a week from today or not. Returns true if the
  /// difference is not more than 7 days.
  bool get isWithinAWeek =>
      DateTime.now().onlyDate.difference(onlyDate.toLocal()).inDays <= 7;

  /// Get the string formatted day-of-week, from the date.
  String getWeekdayInString(BuildContext context) {
    switch (toLocal().weekday) {
      case DateTime.monday:
        return context.localizations.monday;
      case DateTime.tuesday:
        return context.localizations.tuesday;
      case DateTime.wednesday:
        return context.localizations.wednesday;
      case DateTime.thursday:
        return context.localizations.thursday;
      case DateTime.friday:
        return context.localizations.friday;
      case DateTime.saturday:
        return context.localizations.saturday;
      case DateTime.sunday:
        return context.localizations.sunday;
      default:
        throw DeveloperError(
          "DateTime.weekday expected range is 1-7. Received value: $weekday",
        );
    }
  }
}

extension ThemedImageUrl on ThemedImage {
  /// Get Image URL from the [ThemedImage]
  /// based on the current app theme.
  String getThemedImageUrl(BuildContext context) {
    final MultiResolutionBitmapImage multiResolutionBitmapImage =
        (dark != null && context.isDarkMode) ? dark! : light;
    return multiResolutionBitmapImage.getNetworkPhotoURL(context);
  }
}

extension LeoPhoneNumberX on LeoPhoneNumber {
  String get formattedPhoneNumber => PhoneNumberFormatter.format(phoneNumber);
}

extension StreamX<T> on Stream<T> {
  Future<T?> get firstOrNull => StreamExtensions(this).firstOrNull;
}

extension PageControllerX on PageController {
  /// Jumps to the next page of the PageView (similar to `this.nextPage`)
  /// but without animation.
  void get jumpToNextPage => jumpToPage(page!.round() + 1);

  /// Jumps to the previous page of the PageView (similar to `this.previousPage`)
  /// but without animation.
  void get jumpToPreviousPage => jumpToPage(page!.round() - 1);
}
