import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateFormatter {
  DateFormatter._();

  static const _dobFormatString = "dd, MMM yyyy";
  static const _defaultDateFormat = "dd/MM/yyyy";
  static const _otpValidityString24Hrs = "HH:mm:ss";
  static const _otpValidityString12Hrs = "hh:mm:ss a";

  static DateFormat dobFormat() {
    return DateFormat(_dobFormatString);
  }

  static DateFormat defaultDateFormat() {
    return DateFormat(_defaultDateFormat);
  }

  /// Gets instance of [DateFormat]
  /// which will format instance of [DateTime]
  /// to localised "16 Dec 2020 at 05:30 PM" for 12 hrs device time format
  /// and to localised "16 Dec 2020 at 17:30" for 24 hrs device time format.
  static DateFormat exactTimeFormat(BuildContext context) {
    final isDevice24HrsFormat = context.mq.alwaysUse24HourFormat;
    return DateFormat(
      isDevice24HrsFormat
          ? context.localizations.exactTime24HrsFormat
          : context.localizations.exactTime12HrsFormat,
    );
  }

  /// Gets instance of [DateFormat]
  /// which will format instance of [DateTime]
  /// to localised "05:30 PM on Jan 03 2021" for 12 hrs device time format
  /// and to localised "17:30 on Jan 03 2021" for 24 hrs device time format.
  static DateFormat transactionDateTimeFormat(BuildContext context) {
    final isDevice24HrsFormat = context.mq.alwaysUse24HourFormat;
    return DateFormat(
      isDevice24HrsFormat
          ? context.localizations.transactionDateTime24HrsFormat
          : context.localizations.transactionDateTime12HrsFormat,
    );
  }

  /// Gets instance of [DateFormat]
  /// which will format instance of [DateTime]
  /// to localised "05:30 PM" for 12 hrs device time format
  /// and to localised "17:30" for 24 hrs device time format.
  static DateFormat transactionClockTimeFormat(BuildContext context) {
    final isDevice24HrsFormat = context.mq.alwaysUse24HourFormat;
    return isDevice24HrsFormat ? DateFormat.Hm() : DateFormat.jm();
  }

  /// Gets instance of [DateFormat]
  /// which will format instance of [DateTime]
  /// to localised "05:30:30 PM" for 12 hrs device time format
  /// and to localised "17:30:30" for 24 hrs device time format.
  static DateFormat otpValidityFormat(BuildContext context) {
    final isDevice24HrsFormat = context.mq.alwaysUse24HourFormat;
    return isDevice24HrsFormat
        ? DateFormat(_otpValidityString24Hrs)
        : DateFormat(_otpValidityString12Hrs);
  }
}
