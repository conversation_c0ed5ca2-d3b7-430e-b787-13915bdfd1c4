import 'dart:core';

import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:intl/intl.dart';

class AmountFormatter {
  AmountFormatter._();

  static const amountMultiplicationFactor = 10000;

  /// Returns Flutter based [NumberFormat] instance
  /// [decimalDigits] decides the precision of the number,
  /// by default, [decimalDigits] will be disabled
  /// if [currencySymbol] is not provided, then no currency symbol will be shown.
  static NumberFormat amountIntlFormatter({
    int decimalDigits = 0,
    String currencySymbol = "",
  }) {
    // Hardcoding locale for amount formatting.
    const String currentLocale = "en_US";
    return NumberFormat.currency(
      locale: currentLocale,
      symbol: currencySymbol.isNotEmpty ? "$currencySymbol " : "",
      decimalDigits: decimalDigits,
    );
  }

  /// Converts [rpcAmount] (number multiplied by [amountMultiplicationFactor])
  /// to an actual amount.
  /// For Example:
  /// For RPC amount as 1230000
  /// Actual amount will be 123, given the condition that
  /// [amountMultiplicationFactor] is 10000.
  static double getActualAmount(int rpcAmount) =>
      rpcAmount / amountMultiplicationFactor;

  /// Converts actual amount
  /// to RPC amount(number multiplied by [amountMultiplicationFactor])
  /// For Example:
  /// For Actual amount as 123
  /// RPC amount will be 1230000, given the condition that
  /// [amountMultiplicationFactor] is 10000.
  static int toRPCAmount(num actualAmount) =>
      (actualAmount * amountMultiplicationFactor).toInt();

  /// Converts [rpcAmount] to an actual amount
  /// and returns formatted amount with [currencySymbol] as per device locale
  /// if [currencySymbol] is not provided, only formatted number will be returned
  /// For Example:
  /// For RPC Amount as 12340000 and MWK as [currencySymbol]
  /// This method returns "MWK 1,234".
  static String getLocaleFormattedAmountFromRPCAmount(
    int rpcAmount, [
    String currencySymbol = "",
  ]) {
    final double actualAmount = getActualAmount(rpcAmount);
    final bool isAmountInteger = (actualAmount.roundToDouble()) == actualAmount;
    final NumberFormat amountFormatter = amountIntlFormatter(
      decimalDigits: isAmountInteger ? 0 : 2,
      currencySymbol: currencySymbol,
    );
    return amountFormatter.format(actualAmount);
  }

  /// Get Differences between [firstAmount] and [secondAmount]
  /// in the same currency.
  /// [firstAmount] should always be greater than [secondAmount]
  static Amount getDifferenceBetweenAmounts(
    Amount firstAmount,
    Amount secondAmount,
  ) {
    if (firstAmount.amount < secondAmount.amount) {
      throw DeveloperError(
        "firstAmount should always be greater than secondAmount",
      );
    }
    if (firstAmount.currency.currencyCode.toLowerCase() !=
        secondAmount.currency.currencyCode.toLowerCase()) {
      throw DeveloperError(
        "Differences can be calculated only between two same currencies amount.",
      );
    }
    final int finalAmountReceivable = firstAmount.amount - secondAmount.amount;
    return Amount(
      amount: finalAmountReceivable,
      currency: firstAmount.currency,
    );
  }

  static Amount addAmounts(Amount firstAmount, Amount secondAmount) {
    if (firstAmount.currency.currencyCode.toLowerCase() !=
        secondAmount.currency.currencyCode.toLowerCase()) {
      throw DeveloperError("Amounts can only be added for the same currency");
    }
    final int finalAmountReceivable = firstAmount.amount + secondAmount.amount;
    return Amount(
      amount: finalAmountReceivable,
      currency: firstAmount.currency,
    );
  }
}
