import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

import 'amount_formatter.dart';

/// Format amount input as you type based on the locale.
class AmountTextInputFormatter extends TextInputFormatter {
  final int maxAmount;
  int? _amountIntegerPart;
  int? _amountFractionPart;
  final Currency currency;
  static const _thousandSeparator = ",";
  static const _decimalSeparator = ".";

  /// RegExp that will any double amount with 2 precision.
  final _amountRegExp = RegExp(r'(^\d*\.?\d{0,2})');

  AmountTextInputFormatter({
    required this.currency,
    this.maxAmount = ************,
  });

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final onlyNumbers = newValue.text.replaceAll(_thousandSeparator, "");
    final filteredEnteredAmount = _amountRegExp.firstMatch(onlyNumbers)![0];
    final doesContainDecimal =
        filteredEnteredAmount?.contains(_decimalSeparator) ?? false;
    final double? value = double.tryParse(filteredEnteredAmount!);
    if (value == null) {
      return newValue.copyWith(
        text: filteredEnteredAmount,
        selection: TextSelection.collapsed(
          offset: _getSelectionOffset(newValue, filteredEnteredAmount),
          affinity: newValue.selection.affinity,
        ),
        composing: TextRange.collapsed(filteredEnteredAmount.length),
      );
    }
    // Check if entered amount is not exceeding maxAmount.
    if (value.toInt() > maxAmount) return oldValue;
    _amountIntegerPart = value.toInt();
    late final NumberFormat numberFormatter =
        AmountFormatter.amountIntlFormatter();
    final String localeFormattedAmount = numberFormatter.format(
      _amountIntegerPart,
    );
    final String decimalValue =
        doesContainDecimal
            ? filteredEnteredAmount.split(_decimalSeparator).last
            : "";
    _amountFractionPart = int.tryParse(decimalValue);
    // Amount formatted with decimal and fraction digits if any.
    final fullAmountWithDecimal =
        "$localeFormattedAmount${doesContainDecimal ? "$_decimalSeparator$decimalValue" : ""}";
    return newValue.copyWith(
      text: fullAmountWithDecimal,
      selection: TextSelection.collapsed(
        offset: _getSelectionOffset(newValue, fullAmountWithDecimal),
        affinity: newValue.selection.affinity,
      ),
      composing: TextRange.empty,
    );
  }

  static int parseEnteredAmount(String amount) {
    final numberFormatter = AmountFormatter.amountIntlFormatter();
    final num rawAmount = numberFormatter.parse(amount);
    return rawAmount.toInt();
  }

  /// [Amount] type object directly from the formatter.
  Amount? get amountObject {
    if (_amountIntegerPart == null) return null;
    final double fractionalPartDouble =
        double.tryParse("$_decimalSeparator$_amountFractionPart") ?? 0;
    final double amountWithDecimal = _amountIntegerPart! + fractionalPartDouble;
    return Amount(amount: amountWithDecimal.rpcAmount, currency: currency);
  }

  int _getSelectionOffset(TextEditingValue newValue, String formattedValue) {
    int leadingCount =
        newValue.text
            .substring(0, newValue.selection.start)
            .replaceAll(_thousandSeparator, "")
            .length;
    int position = 0;
    while (leadingCount > 0 && position < formattedValue.length) {
      if (formattedValue[position] != _thousandSeparator) {
        leadingCount -= 1;
      }
      position += 1;
    }
    return position;
  }
}
