import 'package:phone_numbers_parser/phone_numbers_parser.dart';

class PhoneNumberFormatter {
  PhoneNumberFormatter._();

  /// Formats the phone number as per the phone number country code.
  static String format(String phoneNumber) {
    final phoneNumberObject = PhoneNumber.parse(phoneNumber);
    final phoneNumberCountryCode = '+${phoneNumberObject.countryCode}';
    final phoneNumberNsn = phoneNumberObject.formatNsn();
    final phoneNumberString = '$phoneNumberCountryCode $phoneNumberNsn';
    return phoneNumberString;
  }
}
