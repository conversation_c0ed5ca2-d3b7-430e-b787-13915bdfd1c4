import 'dart:convert';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CachedSupportedCountries {
  static const _supportedCountriesKey = "supportedCountries";

  static Future<void> setSupportedCountries(List<Country> countries) async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> jsonEncodedCountries =
        countries.map((country) => jsonEncode(country.toJson())).toList();
    await prefs.setStringList(_supportedCountriesKey, jsonEncodedCountries);
  }

  static Future<List<Country>?> getSupportedCountries() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? supportedCountriesJson = prefs.getStringList(
      _supportedCountriesKey,
    );
    final List<Country>? supportedCountries =
        supportedCountriesJson
            ?.map((country) => Country.fromJson(jsonDecode(country)))
            .toList();
    return supportedCountries;
  }

  static void removeSupportedCountries() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_supportedCountriesKey);
  }
}
