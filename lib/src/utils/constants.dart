import 'package:bcn_agency_banking_flutter/src/helpers/reg_exp_helper.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_with_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import '../resources/dimensions.dart';

const appName = "YAFIKA_AGENCY_BANKING";

const double commonPaddingNumber = 36;

const commonPadding = EdgeInsets.all(commonPaddingNumber);

const walletIdMaskLength = 4;

const int passwordMaxLength = 200;

const String googlePlacesUrl = 'https://maps.googleapis.com/maps/api/place/';
const String googleLocationQueryURL =
    "https://www.google.com/maps/search/?api=1&query=%s,%s";

const String resetPasswordUrl =
    "https://app-redirect.resoluttech.link/bcn?endpoint=RESET_PASSWORD&shouldNavigate=true";

const String supportEmailAddress = "<EMAIL>";
const String supportPhoneNumber = "+************";

const String privacyPolicyUrl =
    "https://s3.af-south-1.amazonaws.com/images.resoluttech.link/Privacy-Policy.pdf";

final nationalIdFormatter = FilteringTextInputFormatter.allow(
  RegExpHelper.alphaNumericPattern,
);

final requestIdFormatter = FilteringTextInputFormatter.allow(
  RegExpHelper.alphaNumericPattern,
);

const double maxVisibilityPercentage = 100;

const double secondsOfInActivityAllowed = 20;

const String obscureCharacter = "•";

// 100 is the max length defined in RPC types for password
// Link to yml:https://github.com/Resolut-Tech/bcn-rpcs/blob/master/types/password-type.yml
const int maxPasswordLength = 100;
const passwordMaxLines = 1;

/// Used in [AppPinScreen], [SetupAppPinScreen] and [EnterCurrentAppPinScreen].
const EdgeInsets authFieldPadding = EdgeInsets.symmetric(vertical: 36);

/// Used in [AppPinScreen], [SetupAppPinScreen] and [EnterCurrentAppPinScreen].
const double pinCircleRadius = 16;

/// Used in [AppPinScreen], [SetupAppPinScreen] and [EnterCurrentAppPinScreen].
const double spacingBetweenLogoAndTitleText = 16;

/// Used in [AppPinScreen], [SetupAppPinScreen] and [EnterCurrentAppPinScreen].
const double spacingBetweenTitleTextAndPinField = 16;

/// Used in [SetupAppPinScreen] and [EnterCurrentAppPinScreen].
const double spacingBetweenScreenTopAndLogo = 80;

const List<String> agentManagerPrivileges = [
  "HANDLE_AGENT_APPLICATION",
  "UNBLOCK_CUSTOMER_REFUND",
  "INITIATE_CUSTOMER_REFUND",
];

const defaultTextFieldMaxLength = 20;

const int defaultTextFieldMaxLines = 1;

const nationalIdMaxLength = 200;

const shopNameMaxLength = 200;

//Phone number field related.
const maxPhoneNumberFieldLength = 15;

// PrimaryTextField related.
const errorMaxLines = 2;

// Transaction details related.
const valueMaxLines = 1;
const dashWidth = 3.0;
const dashHeight = 1.0;
const labelMinimumWidth = 80;

//Card related.
const cardContentMaxLines = 1;
const cardSubtitleMaxLines = 2;

/// Used in [IconWithLabel] widget.
const int labelTextMaxLines = 2;

// OTP related.
const validOTPLength = 6;

// Auth Code related.
const authCodeLength = 4;

// Related to image upload.
const imageMaxSizeInBytes = 5000000;
const placeHolderMaxLines = 3;

// Session Pin related.
const pinLength = 8;
final sessionPinInputFormatter = FilteringTextInputFormatter.allow(
  RegExpHelper.alphaNumericPattern,
);
const int maximumIncorrectAttempts = 3;

final pageTransitionDuration = 300.milliseconds;
const pageTransitionCurve = Curves.linear;

/// Used in [InfoLabel] widget.
const int bodyTextMaxLines = 2;

// Landing Screen related.
const landingScreenSubtitleMaxLines = 2;

// UserDetailsCard related.
const nameMaxLines = 2;
const shopNameMaxLines = 2;

// Change Language related.
const supportChangeLanguageFeature = false;

// Manage wallet related.
const int walletDisplayNameMaxLines = 2;

// PhoneButtonWidget related.
// The string is written in sprintf format.
const String phoneNumberURL = "tel:%s";

// Address related.
const int maxAddressLength = 200;

// Name related.
const int maxNameLength = 200;

// Request ID related.
const requestIDMaxLength = 10;

// BottomSheet related.
const radius = Radius.circular(dimenFour);
const bottomSheetBorderRadius = BorderRadius.only(
  topLeft: radius,
  topRight: radius,
);

// Multiline Field related.
const minLines = 4;
const maxLines = 4;

// Comment related.
const maxCommentLength = 1000;

// Image caching related.
final stalePeriod = 10.minutes;
final cacheManager = CacheManager(
  Config("agency-banking-cache-key", stalePeriod: stalePeriod),
);

// Edit shop location related.
const allowedDistanceChangeInMeters = 200;

// Elevated Button related.
const elevatedButtonShadowOffset = Offset(0, 4);
const elevatedButtonShadowColor = Color(
  0x40000000,
); // 25% Alpha of white color.
const elevatedButtonShadowBlurRadius = 4.0;

// Retry location Radius check related.
final Duration locationRadiusCheckRetryDelay = 500.milliseconds;

// Initial zoom level of the Google Maps feature
const double defaultMapZoom = 18.0;

const String bulletPoint = '\u2022';
