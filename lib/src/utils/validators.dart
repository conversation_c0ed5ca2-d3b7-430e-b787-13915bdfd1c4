import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

class Validators {
  Validators._();

  /// Mobile number validator to be passed in [PrimaryTextField.validator].
  /// [label] defaults to [context.localizations.mobileNumber]
  static String? mobileNumberValidator(
    BuildContext context,
    IsoCode isoCode,
    String? number, {
    String? label,
  }) {
    if (number == null || number.trim().isEmpty) {
      return context.localizations.fieldIsRequired(
        label ?? context.localizations.mobileNumber,
      );
    }
    final String trimmedNumber = number.removeSpaces();
    final PhoneNumber phoneNumber = PhoneNumber(
      isoCode: isoCode,
      nsn: trimmedNumber,
    );

    if (!phoneNumber.isValid()) {
      return context.localizations.invalidMobileNumberError;
    }
    return null;
  }

  /// Amount Validator to be passed in [PrimaryTextField.validator].
  /// Unlike other validators this takes [Amount] for validating the
  /// inputs.
  static String? amountValidator(BuildContext context, String? amount) {
    // If amount is empty.
    if (amount == null) {
      return context.localizations.fieldIsRequired(
        context.localizations.amount,
      );
    }
    final filteredEnteredAmount = amount.replaceAll(RegExp(r'\D'), '');
    final double? value = double.tryParse(filteredEnteredAmount);
    if (value == null) {
      return context.localizations.fieldIsRequired(
        context.localizations.amount,
      );
    }
    // If amount is zero or invalid.
    if (value == 0) {
      return context.localizations.invalidAmountError;
    }
    return null;
  }

  /// This will validate the empty value.
  /// This can be used in National ID, Request ID
  /// and for all the other mandatory form fields.
  /// Pass [label] as the text you want to pass
  /// in [fieldName] of
  /// [context.localizations.fieldIsRequired].
  static String? emptyValidator(
    BuildContext context,
    String label,
    String? text,
  ) {
    if (text == null || text.trim().isEmpty) {
      return context.localizations.fieldIsRequired(label);
    }
    return null;
  }

  /// [fieldName] should be the name without the verb.
  /// For example, it should be 'OTP' and not 'Enter OTP'
  /// It first validates whether the OTP field is empty and
  /// then check whether the length of OTP equals to
  /// [validOtpLength].
  static String? otpValidator(
    BuildContext context,
    String fieldName,
    String? otp,
    int validOtpLength,
  ) {
    if (otp == null || otp.trim().isEmpty) {
      return context.localizations.fieldIsRequired(fieldName);
    }
    if (otp.trim().length != validOtpLength) {
      return context.localizations.invalidOTP;
    }
    return null;
  }
}
