import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    Key? key,
    required this.labelText,
    required this.onPressed,
    this.width = double.infinity,
  }) : super(key: key);
  final VoidCallback onPressed;
  final String labelText;
  final double width;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      decoration: const BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: elevatedButtonShadowColor,
            blurRadius: elevatedButtonShadowBlurRadius,
            offset: elevatedButtonShadowOffset,
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          dismissKeyboard();
          onPressed();
        },
        child: Text(
          labelText.toUpperCase(),
          style: context.appTextStyles.buttonText1.copyWith(
            color: context.appColors.genericWhiteColor,
          ),
        ),
      ),
    );
  }
}
