import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

/// [AgencyLocationErrorDialogRoute] that will pop based on the condition
/// provided in [shouldPop].
/// [shouldPop] is false by default.
/// This dialog is non-dismissible and overrides the
/// normal routing behaviour of [DialogRoute] under [Navigator].
/// This dialog can only be dismissed by [Navigator.removeRoute].
///
/// Although, this [DialogRoute] is a hack and is never recommended
/// to be used unless it is in specific use case like location check.
class AgencyLocationErrorDialogRoute extends DialogRoute {
  final BuildContext context;
  final WidgetBuilder builder;
  final bool shouldPop;

  final String dialogRouteName;

  AgencyLocationErrorDialogRoute({
    required this.context,
    required this.builder,
    this.shouldPop = false,
    required this.dialogRouteName,
  }) : super(
         context: context,
         builder: builder,
         settings: RouteSettings(name: dialogRouteName),
       );

  @override
  bool didPop(result) {
    if (shouldPop) {
      return super.didPop(result);
    } else {
      // Pop (remove) the route just below this route
      // to call the pop on the route the program is intended on.
      // Thanks: https://stackoverflow.com/a/55622474
      // This is done to prevent error in Navigation
      // of _debugLocked becoming true.
      // The simple way is to just use a delayed future with zero delay,
      // which will have dart schedule the call as soon as possible
      // once the current call stack returns to the event loop.
      // This is a hack and would not recommend to use this
      // for any purpose unless we have a specific need.
      Future.delayed(Duration.zero, () {
        // If there are minimum two routes in the Navigation Stack,
        // then only remove the route below this
        // so that this does not get call on first route and the user
        // is stranded on the black screen.
        if (NavigationHistoryObserver().history.length > 2 && context.mounted) {
          context.rootNavigator.removeRouteBelow(this);
        }
      });
      return false;
    }
  }

  @override
  Future<RoutePopDisposition> willPop() async {
    // Not using this variable
    // since, willPop will anyway be overridden.
    // Calling this to avoid analysis warning.
    final _ = super.popDisposition;
    return RoutePopDisposition.doNotPop;
  }

  // This dialog is never barrier dismissible.
  @override
  bool get barrierDismissible => false;

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return builder(context);
  }
}
