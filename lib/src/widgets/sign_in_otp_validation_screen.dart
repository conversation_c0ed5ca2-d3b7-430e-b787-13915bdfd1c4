import 'package:agency_banking_rpcs/types/otp_resend_details_type.dart';
import 'package:agency_banking_rpcs/types/otp_validity_details_type.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/phone_number_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/otp_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sub_screen_layout.dart';
import 'package:flutter/material.dart';

class SignInOTPValidationScreen extends StatelessWidget {
  final String phoneNumber;

  /// [OTPValidityDetails] object from RPC.
  final OTPValidityDetails otpValidityDetails;

  /// [onChanged] parameter when user edits the OTP text field.
  final void Function(String) onOTPChanged;

  /// [onResend] parameter should pass on the [Future] which will resolve
  /// with [OTPResendDetails] in case of successful RPC response.
  /// Resolve this [Future] with null, if there is any error in RPC call.
  final Future<OTPResendDetails?> Function() onOTPResend;
  final Widget validateOTPButton;

  final GlobalKey<FormState> formKey;
  final AutovalidateMode autoValidateMode;

  const SignInOTPValidationScreen({
    super.key,
    required this.phoneNumber,
    required this.otpValidityDetails,
    required this.onOTPChanged,
    required this.onOTPResend,
    required this.validateOTPButton,
    required this.formKey,
    required this.autoValidateMode,
  });

  @override
  Widget build(BuildContext context) {
    final formattedPhoneNumber = PhoneNumberFormatter.format(phoneNumber);
    return SubScreenLayout(
      title: context.localizations.enterTheOTP,
      primaryButton: validateOTPButton,
      child: Column(
        children: [
          verticalGapEight,
          Text(
            context.localizations.otpSentToNumber(formattedPhoneNumber),
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.neutralShadeDefaultColor,
            ),
            textAlign: TextAlign.center,
          ),
          verticalGapForty,
          Form(
            key: formKey,
            autovalidateMode: autoValidateMode,
            child: OTPWidget(
              otpValidityDetails: otpValidityDetails,
              onChanged: onOTPChanged,
              onResend: onOTPResend,
            ),
          ),
        ],
      ),
    );
  }
}
