import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';

import '../resources/dimensions.dart';
import 'dotted_divider.dart';
import 'title_widget.dart';
import 'transaction_details_dialog.dart';
import 'transaction_label_with_value.dart';

/// Views a Transaction details.
/// Shows transaction details in a following format:
/// {
/// Transaction Details:     (* This is dependent on whether the widget is in a dialog or not.)
/// [transactionAmountLabel]  -> formatted [transactionAmount]
/// Transaction Fee           -> formatted [transactionFee]
/// [additionalDetailLabel]   -> [additionalDetailValue]
/// (* The above one is optional layer)
/// [receivingAmountLabel]    -> formatted [receivingAmount]
/// }
///
///
/// [additionalDetailLabel] and [additionalDetailValue] both can either be null
/// or non-null.
///
///
/// [isShownInDialog] defaults to false.
/// It has a special case where it is [true]
/// in [TransactionDialog.show]
///
class TransactionDetailsWidget extends StatelessWidget {
  final String transactionAmountLabel;
  final Amount transactionAmount;

  final Amount transactionFee;

  final String receivingAmountLabel;
  final Amount receivingAmount;

  final String? additionalDetailLabel;
  final String? additionalDetailValue;

  final bool isShownInDialog;

  const TransactionDetailsWidget({
    Key? key,
    required this.transactionAmount,
    required this.transactionFee,
    required this.receivingAmount,
    required this.transactionAmountLabel,
    required this.receivingAmountLabel,
    this.additionalDetailLabel,
    this.additionalDetailValue,
    this.isShownInDialog = false,
  }) : assert(
         !(additionalDetailLabel != null) ^ (additionalDetailValue != null),
         "Please either provide both additionalDetailLabel and additionalDetailValue or provide none.",
       ),
       super(key: key);

  @override
  Widget build(BuildContext context) {
    return isShownInDialog
        ? PopScope(
          canPop: false,
          child: Dialog(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: dialogContentPadding,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.localizations.transactionDetails,
                        style: context.appTheme.appTextStyles.smallText1Bold
                            .copyWith(
                              color: context.appColors.neutralShade1Color,
                            ),
                      ),
                      verticalGapTwelve,
                      Flexible(child: _showTransactionDetails(context)),
                    ],
                  ),
                ),
                _buildButtons(context),
              ],
            ),
          ),
        )
        : Column(
          children: [
            TitleWidget(title: context.localizations.transactionDetailsString),
            Padding(
              padding: commonScreenPadding,
              child: _showTransactionDetails(context),
            ),
          ],
        );
  }

  Column _showTransactionDetails(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const DottedDivider(),
        _gapBetweenEachTransactionDetail,
        _buildTransactionAmount(context),
        _gapBetweenEachTransactionDetail,
        _buildTransactionFee(context),
        if (additionalDetailLabel != null && additionalDetailValue != null) ...{
          _gapBetweenEachTransactionDetail,
          _buildAdditionalDetails(context),
        },
        _gapBetweenEachTransactionDetail,
        const DottedDivider(),
        _gapBetweenEachTransactionDetail,
        _buildReceivableAmount(context),
        _gapBetweenEachTransactionDetail,
        const DottedDivider(),
      ],
    );
  }

  SizedBox get _gapBetweenEachTransactionDetail =>
      isShownInDialog ? verticalGapTwelve : verticalGapSixteen;

  Align _buildButtons(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: Padding(
        padding: actionContentPadding,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            PrimaryTextButton(
              text: context.localizations.decline.toUpperCase(),
              onTap: () => context.navigator.pop(false),
            ),
            horizontalGapEight,
            PrimaryTextButton(
              text: context.localizations.proceed.toUpperCase(),
              onTap: () => context.navigator.pop(true),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionAmount(BuildContext context) {
    return TransactionDetailsLabelWithValue(
      label: transactionAmountLabel,
      value: transactionAmount.localisedFormattedAmount,
      isShownInDialog: isShownInDialog,
    );
  }

  Widget _buildTransactionFee(BuildContext context) {
    return TransactionDetailsLabelWithValue(
      label: context.localizations.transactionFee,
      value: transactionFee.localisedFormattedAmount,
      isShownInDialog: isShownInDialog,
    );
  }

  Widget _buildAdditionalDetails(BuildContext context) {
    return TransactionDetailsLabelWithValue(
      label: additionalDetailLabel!,
      value: additionalDetailValue!,
      isShownInDialog: isShownInDialog,
    );
  }

  Widget _buildReceivableAmount(BuildContext context) {
    return TransactionDetailsLabelWithValue(
      label: receivingAmountLabel,
      value: receivingAmount.localisedFormattedAmount,
      isShownInDialog: isShownInDialog,
      isGrandTotal: true,
    );
  }
}
