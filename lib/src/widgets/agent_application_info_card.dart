import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/user_image.dart';
import 'package:flutter/material.dart';

/// Widget to display information of an agent application.
class AgentApplicationInfoCard extends StatelessWidget {
  final String? imageURL;
  final String name;
  final String phoneNumber;
  final String shopName;
  final DateTime createdAt;
  final VoidCallback onTap;

  const AgentApplicationInfoCard({
    Key? key,
    required this.name,
    required this.phoneNumber,
    required this.shopName,
    required this.createdAt,
    required this.onTap,
    this.imageURL,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Ink(
        padding: allPaddingSixteen,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            UserImage(
              imageUrl: imageURL,
              height: dimenForty,
              width: dimenForty,
            ),
            horizontalGapSixteen,
            _getUserDetails(context),
            horizontalGapFour,
            Text(
              DateFormatter.dobFormat().format(createdAt),
              style: context.appTheme.appTextStyles.smallText2.copyWith(
                color: context.appColors.neutralShade2Color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Expanded _getUserDetails(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            style: context.appTextStyles.smallText1Bold.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          verticalGapFour,
          Text(
            shopName,
            style: context.appTextStyles.smallText2.copyWith(
              color: context.appColors.neutralShade7Color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          verticalGapFour,
          Text(
            phoneNumber,
            style: context.appTextStyles.smallText2.copyWith(
              color: context.appColors.neutralShade7Color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
