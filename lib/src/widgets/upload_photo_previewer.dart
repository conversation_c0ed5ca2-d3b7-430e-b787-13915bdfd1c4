import 'dart:io';

import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/image_placeholder_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/photo_viewer.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:flutter/material.dart';
import 'package:leo_flutter_ui/leo_flutter_ui.dart';
import 'package:path/path.dart' as path_provider;

/// [UploadPhotoPreviewer] is a widget that enables the user to upload photos from the camera or gallery,
/// The user can view and delete the selected image.
/// Returns a Photo of [Photo] type and returns `null` if the user doesn't select a photo.
class UploadPhotoPreviewer extends StatefulWidget {
  const UploadPhotoPreviewer({
    Key? key,
    required this.onPhotoSelected,
    this.onPhotoDeleted,
    required this.placeholderText,
    this.initialPhoto,
    required this.title,
    this.initialServerPhoto,
  }) : super(key: key);

  final Function(Photo photo) onPhotoSelected;
  final void Function()? onPhotoDeleted;
  final String placeholderText;
  final File? initialPhoto;

  /// The initial network photo that was uploaded by the user.
  final ImageProvider? initialServerPhoto;
  final String title;

  @override
  UploadPhotoPreviewerState createState() {
    return UploadPhotoPreviewerState();
  }
}

class UploadPhotoPreviewerState extends State<UploadPhotoPreviewer> {
  late File? _selectedPhoto = widget.initialPhoto;
  late final ImageProvider? _initialServerPhoto = widget.initialServerPhoto;
  late bool _isImagePresent =
      widget.initialPhoto != null || widget.initialServerPhoto != null;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TitleWidget(title: widget.title),
        verticalGapSixteen,
        Padding(
          padding: horizontalPaddingSixteen,
          child: Column(
            children: [
              _getPhotoPreviewer(),
              verticalGapEight,
              _getActionButtons(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _getPhotoPreviewer() {
    return PhotoViewer(
      image:
          _selectedPhoto != null
              ? Image.file(_selectedPhoto!, fit: BoxFit.cover)
              : _initialServerPhoto != null && _isImagePresent
              ? Image(image: _initialServerPhoto, fit: BoxFit.cover)
              : ImagePlaceholderWidget(placeholderText: widget.placeholderText),
    );
  }

  Row _getActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        PrimaryTextButton(
          text: context.localizations.selectPhoto,
          onTap: _showPhotoSelectorDialog,
        ),
        if (_isImagePresent) ...[const Spacer(), _getDeletePhotoButton()],
      ],
    );
  }

  PrimaryTextButton _getDeletePhotoButton() {
    return PrimaryTextButton(
      onTap: () async {
        final bool shouldDeletePhoto =
            await AgencyAppDialog.showConfirmationDialog(
              context: context,
              contentText: context.localizations.deletePhotoConfirmationMessage,
            ) ??
            false;
        if (shouldDeletePhoto) {
          setState(() {
            _selectedPhoto = null;
            _isImagePresent = false;
          });
          widget.onPhotoDeleted?.call();
        }
      },
      color: context.appColors.errorColor,
      text: context.localizations.deletePhoto,
    );
  }

  Future<void> _showPhotoSelectorDialog() {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (fileSelectorDialogContext) => PopScope(
            canPop: false,
            child: FileSelectorDialog(
              maxImageSize: imageMaxSizeInBytes,
              cameraIcon: IconWidget(
                assetName: ABAssets.cameraIcon,
                iconColor:
                    fileSelectorDialogContext
                        .appColors
                        .neutralShadeDefaultColor,
              ),
              galleryIcon: IconWidget(
                assetName: ABAssets.imageIcon,
                iconColor:
                    fileSelectorDialogContext
                        .appColors
                        .neutralShadeDefaultColor,
              ),
              textStyle: FileSelectorStyle(
                fileSelectorDialogTitleStyle: fileSelectorDialogContext
                    .appTextStyles
                    .normalBold
                    .copyWith(
                      color:
                          fileSelectorDialogContext
                              .appColors
                              .neutralShade1Color,
                    ),
                selectionTileTextStyle: fileSelectorDialogContext
                    .appTextStyles
                    .smallText1
                    .copyWith(
                      color:
                          fileSelectorDialogContext
                              .appColors
                              .neutralShade1Color,
                    ),
                cancelTextStyle: fileSelectorDialogContext
                    .appTextStyles
                    .buttonText1
                    .copyWith(
                      color:
                          fileSelectorDialogContext.appColors.primaryLightColor,
                    ),
              ),
              onImageSelected: (String path) {
                final File selectedPhoto = File(path);
                final String selectedPhotoExtension = path_provider.extension(
                  path,
                );
                final int selectedPhotoSize = selectedPhoto.lengthSync();
                final Photo photo = Photo(
                  photo: selectedPhoto,
                  size: selectedPhotoSize,
                  fileExtension: selectedPhotoExtension,
                );
                widget.onPhotoSelected(photo);
                setState(() {
                  _selectedPhoto = selectedPhoto;
                  _isImagePresent = true;
                });
              },
              onCameraPermissionDenied:
                  () =>
                      AgencyAppDialog.showCameraPermissionDeniedError(context),
              onPhotoPermissionDenied:
                  () =>
                      AgencyAppDialog.showPhotosPermissionDeniedError(context),
              onImageGreaterThanAllowedMaxSize: () {
                AgencyAppDialog.showErrorDialog(
                  context: context,
                  contentText: context.localizations.selectSmallerImage,
                  buttonText: context.localizations.tryAgain,
                );
              },
            ),
          ),
    );
  }
}
