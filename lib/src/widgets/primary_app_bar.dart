import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:flutter/material.dart';

/// [AppBar] used across the whole app.
/// Upon clicking the cancel/back pop will be called from the root navigator.
/// [isCancellable] is true if the screen is inside a flow. If [isCancellable] is true,
/// it will show a close icon in the [AppBar.leading]. Clicking on the close icon will ask for confirmation
/// exit the [processName] flow.
/// if [isCancellable] is true, [processName] is mandatory.
/// if [isCancellable] is false, [processName] will be ignored.
class PrimaryAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? actionIcon;
  final String? processName;

  final bool isCancellable;
  final VoidCallback? onLeadingIconTapped;

  const PrimaryAppBar({
    super.key,
    this.isCancellable = false,
    this.title,
    this.actionIcon,
    this.onLeadingIconTapped,
    this.processName,
  }) : assert(
         isCancellable == true ? processName != null : true,
         "If isCancellable is true, processName should not be null.",
       );

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: IconButton(
        onPressed:
            onLeadingIconTapped ??
            () async {
              if (isCancellable) {
                final bool? shouldGoBack =
                    await AgencyAppDialog.showCancelConfirmation(
                      context,
                      processName!,
                    );
                if (context.mounted && shouldGoBack == true) {
                  context.rootNavigator.pop();
                }
              } else {
                context.rootNavigator.pop();
              }
            },
        icon: IconWidget(
          assetName:
              isCancellable ? ABAssets.closeIcon : ABAssets.arrowLeftIcon,
          iconColor: context.appColors.genericWhiteColor,
        ),
      ),
      centerTitle: false,
      title:
          title != null ? Text(title!, overflow: TextOverflow.ellipsis) : null,
      actions: actionIcon != null ? [actionIcon!] : null,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(dimenFiftySix);
}
