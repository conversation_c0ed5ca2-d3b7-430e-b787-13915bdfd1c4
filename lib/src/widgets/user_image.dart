import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../resources/ab_assets.dart';
import 'icon_widget.dart';

class UserImage extends StatelessWidget {
  final String? imageUrl;
  final double height;
  final double width;

  const UserImage({
    super.key,
    this.imageUrl,
    required this.height,
    required this.width,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: context.appColors.neutralShade4Color,
      ),
      child:
          imageUrl != null
              ? CachedNetworkImage(
                imageUrl: imageUrl!,
                imageBuilder:
                    (context, imageProvider) => Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                placeholder:
                    (_, __) => Center(
                      child: SizedBox(
                        height: height / 2,
                        width: width / 2,
                        child: const Spinner(strokeWidth: dimenTwo),
                      ),
                    ),
                errorWidget:
                    (_, __, ___) => IconWidget(
                      assetName: ABAssets.failedToFetchIcon,
                      height: height,
                      width: width,
                    ),
              )
              : _getPlaceholderWidget(context),
    );
  }

  Widget _getPlaceholderWidget(BuildContext context) {
    return Center(
      child: IconWidget(
        assetName: ABAssets.userIcon,
        height: height / 2,
        width: width / 2,
        iconColor: context.appColors.neutralShade5Color,
        fit: BoxFit.contain,
      ),
    );
  }
}
