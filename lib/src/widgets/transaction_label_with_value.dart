import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

import '../resources/dimensions.dart';
import '../utils/constants.dart';

/// [label] and [value] view to be shown for
/// Transaction details.
/// [value]s are usually formatted amount.
/// Conditional constraints is used for label in this widget.
/// The layout of label with value will be done in a following way:
/// The [value] will be wrapped in a single line. Based on that, the
/// [label] will get some maximum width constraints.
/// If maximum width constraints is less than [labelMinimumWidth] then,
/// the horizontal layout will change to vertical layout.
class TransactionDetailsLabelWithValue extends StatefulWidget {
  final String label;
  final String value;
  final bool isShownInDialog;

  /// Defaults to:
  /// 1. If [isShownInDialog] is true then:
  ///   if [isGrandTotal] is true then [context.appTextStyles.smallText1Bold]
  ///   else [context.appTextStyles.smallText2].
  /// 2. If [isShownInDialog] is false then:
  ///    if [isGrandTotal] is true then [context.appTextStyles.normalBold]
  ///   else [context.appTextStyles.smallText1].
  final TextStyle? labelStyle;

  /// Defaults to:
  /// 1. If [isShownInDialog] is true then:
  ///   if [isGrandTotal] is true then [context.appTextStyles.smallText1Bold]
  ///   else [context.appTextStyles.smallText2Bold].
  /// 2. If [isShownInDialog] is false then:
  ///    if [isGrandTotal] is true then [context.appTextStyles.normalBold]
  ///   else [context.appTextStyles.smallText1Bold].
  final TextStyle? valueStyle;

  /// Whether the label and value is shown as the grand total.
  /// Grand total label and value has bigger text size and is bolder from other
  /// label and value in transaction details dialog.
  /// Defaults to false.
  final bool isGrandTotal;

  const TransactionDetailsLabelWithValue({
    super.key,
    required this.label,
    required this.value,
    required this.isShownInDialog,
    this.isGrandTotal = false,
    this.labelStyle,
    this.valueStyle,
  });

  @override
  State<TransactionDetailsLabelWithValue> createState() =>
      _TransactionDetailsLabelWithValueState();
}

class _TransactionDetailsLabelWithValueState
    extends State<TransactionDetailsLabelWithValue> {
  Axis _direction = Axis.horizontal;
  MainAxisAlignment _mainAxisAlignment = MainAxisAlignment.start;
  CrossAxisAlignment _crossAxisAlignment = CrossAxisAlignment.start;
  late BoxConstraints _labelConstraints;
  TextStyle? _labelTextStyle;
  TextStyle? _valueTextStyle;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_labelConstraints.maxWidth < labelMinimumWidth) {
        setState(() {
          _setDirectionToVertical();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    _setTextStyles();
    return Flex(
      direction: _direction,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: _mainAxisAlignment,
      crossAxisAlignment: _crossAxisAlignment,
      children: [
        if (_direction == Axis.horizontal)
          Expanded(
            child: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints labelConstraints) {
                _labelConstraints = labelConstraints;
                return _buildLabelText();
              },
            ),
          )
        else
          _buildLabelText(),
        if (_direction == Axis.horizontal) horizontalGapEight,
        Text(widget.value, style: _valueTextStyle, maxLines: valueMaxLines),
      ],
    );
  }

  Text _buildLabelText() {
    return Text(
      widget.label,
      style: _labelTextStyle,
      textAlign: TextAlign.left,
    );
  }

  void _setDirectionToVertical() {
    _direction = Axis.vertical;
    _mainAxisAlignment = MainAxisAlignment.start;
    _crossAxisAlignment = CrossAxisAlignment.stretch;
  }

  void _setTextStyles() {
    // Set label text style.
    if (widget.labelStyle != null) {
      _labelTextStyle = widget.labelStyle;
    } else if (widget.isShownInDialog) {
      _labelTextStyle =
          widget.isGrandTotal
              ? context.appTextStyles.smallText1Bold.copyWith(
                color: context.appColors.neutralShade1Color,
              )
              : context.appTextStyles.smallText2.copyWith(
                color: context.appColors.neutralShade5Color,
              );
    } else {
      _labelTextStyle =
          widget.isGrandTotal
              ? context.appTextStyles.normalBold.copyWith(
                color: context.appColors.neutralShade1Color,
              )
              : context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShade5Color,
              );
    }
    // Set value text style.
    if (widget.valueStyle != null) {
      _valueTextStyle = widget.valueStyle;
    } else if (widget.isShownInDialog) {
      _valueTextStyle =
          widget.isGrandTotal
              ? context.appTextStyles.smallText1Bold.copyWith(
                color: context.appColors.neutralShade1Color,
              )
              : context.appTextStyles.smallText2Bold.copyWith(
                color: context.appColors.neutralShade1Color,
              );
    } else {
      _valueTextStyle =
          widget.isGrandTotal
              ? context.appTextStyles.normalBold.copyWith(
                color: context.appColors.neutralShade1Color,
              )
              : context.appTextStyles.smallText1Bold.copyWith(
                color: context.appColors.neutralShade1Color,
              );
    }
  }
}
