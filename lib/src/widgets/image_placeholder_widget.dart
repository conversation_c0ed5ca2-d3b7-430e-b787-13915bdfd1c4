import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class ImagePlaceholderWidget extends StatelessWidget {
  final String placeholderText;

  const ImagePlaceholderWidget({super.key, required this.placeholderText});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: allPaddingSixteen,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconWidget(
            assetName: ABAssets.imageIcon,
            iconColor: context.appColors.imagePreviewEmptyStateContentColor,
          ),
          verticalGapFour,
          Text(
            placeholderText,
            maxLines: placeHolderMaxLines,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            style: context.appTextStyles.smallText2.copyWith(
              color: context.appColors.imagePreviewEmptyStateContentColor,
            ),
          ),
        ],
      ),
    );
  }
}
