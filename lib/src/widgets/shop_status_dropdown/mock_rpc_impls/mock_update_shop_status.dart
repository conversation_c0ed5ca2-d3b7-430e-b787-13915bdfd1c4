import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

class MockShopStatusImpl extends UpdateShopStatusRPC {
  @override
  Future<LeoRPCResult<UpdateShopStatusResponse, Never>> execute(
    UpdateShopStatusRequest request,
  ) {
    final response = LeoRPCResult<UpdateShopStatusResponse, Never>.response(
      UpdateShopStatusResponse(),
    );

    return Future.delayed(2.seconds, () => response);
  }
}
