import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../core/service_locator.dart';
import '../../../errors/developer_error.dart';
import '../../../features/agent/home/<USER>/home_page_bloc.dart';
import '../../dialogs.dart';
import '../repository/shop_status_repository.dart';

part 'shop_status_bloc.freezed.dart';

class ShopStatusBloc extends Bloc<ShopStatusEvent, ShopStatusState>
    with RPCHandler {
  final Coordinate? shopCoordinate;
  final ShopStatusRepository _shopStatusRepository = ShopStatusRepository();
  ShopStatusEnum currentShopStatus;
  final ValueChanged<ShopStatusEnum?>? onShopStatusChanged;

  ShopStatusBloc({
    required this.shopCoordinate,
    required this.currentShopStatus,
    this.onShopStatusChanged,
  }) : super(const ShopStatusState.initial()) {
    on<ShopStatusEvent>((event, emit) async {
      switch (event) {
        case UpdateShopStatus(:final context, :final shopStatus):
          if (currentShopStatus == shopStatus) return;
          AgencyAppDialog.showSpinnerDialog(
            NavigationService.navigatorKey.currentContext!,
          );
          await _changeShopStatus(context, shopStatus, emit);
      }
    });
  }

  Future<void> _changeShopStatus(
    BuildContext context,
    ShopStatusEnum newShopStatus,
    Emitter<ShopStatusState> emit,
  ) async {
    final isNewShopStatusOpen = newShopStatus == ShopStatusEnum.SHOP_OPEN;
    // Agent should be within pre defined location radius
    // before setting the shop status as open
    if (isNewShopStatusOpen) {
      final isLocationValid = await _isAgentLocationValid(context);
      if (!isLocationValid) {
        emit(const ShopStatusState.prominentError());
        return;
      }
    }
    await rpcHandler(
      () async {
        final result = await _shopStatusRepository.updateShopStatus(
          newShopStatus,
        );
        result.when(
          response: (_) async {
            currentShopStatus = newShopStatus;
            await _waitForDataOnHomeScreen(context);
            // Pop the Spinner dialog.
            NavigationService.navigatorKey.currentContext!.navigator.pop();
          },
          error: (error) => throw DeveloperError("Something went wrong $error"),
        );
      },
      onTransientError: (_) {
        // pop the spinner dialog
        context.navigator.pop();
        emit(const ShopStatusState.transientError());
      },
      onServerError: () {
        // pop the spinner dialog
        context.navigator.pop();
        // Since, the app is currently on the home screen
        // which is a root screen.
        // Hence need to handle server error here.
        AgencyAppDialog.showErrorDialog(
          context: context,
          contentText: context.localizations.somethingWentWrong,
        );
      },
    );
  }

  Future<void> _waitForDataOnHomeScreen(BuildContext context) async {
    final HomePageBloc homePageBloc = BlocProvider.of<HomePageBloc>(context);
    homePageBloc.add(HomePageEvent.getHomeDataAfterShopStatusUpdate(context));
    await for (HomePageState state in homePageBloc.stream) {
      if (state is OnData || state is TransientError || state is ServerError) {
        return;
      }
    }
  }

  Future<bool> _isAgentLocationValid(BuildContext context) async {
    final locationService = locator<LocationService>();
    final locationDetails = shopCoordinate!.locationDetails;
    if (locationService.getCurrentLocation == null) {
      await locationService.updateCurrentLocation();
    }
    final isAgentLocationValid = locationService
        .checkCurrentLocationWithinAcceptableRange(location: locationDetails);
    if (!isAgentLocationValid && context.mounted) {
      // pop the spinner dialog.
      context.navigator.pop();
      // As here the ShopStatusDropDown will become defunct, we will take
      // context from navigator service.
      final currentContext = NavigationService.navigatorKey.currentContext!;
      AgencyAppDialog.showAgentLocationInvalidDialog(
        currentContext,
        locationDetails,
        contentText: currentContext.localizations.shopStatusChangeLocationError(
          locationService.agentMaxAllowedDistance,
        ),
        onCancel: currentContext.navigator.pop,
      );
    }
    return isAgentLocationValid;
  }
}

@freezed
sealed class ShopStatusState with _$ShopStatusState {
  const factory ShopStatusState.initial() = Initial;

  const factory ShopStatusState.loading() = Loading;

  const factory ShopStatusState.prominentError() = ShopStatusProminentError;

  const factory ShopStatusState.transientError() = ShopStatusTransientError;

  const factory ShopStatusState.shopStatusUpdated() = ShopStatusUpdated;
}

@freezed
sealed class ShopStatusEvent with _$ShopStatusEvent {
  const factory ShopStatusEvent.updateShopStatus(
    BuildContext context,
    ShopStatusEnum shopStatus,
  ) = UpdateShopStatus;
}
