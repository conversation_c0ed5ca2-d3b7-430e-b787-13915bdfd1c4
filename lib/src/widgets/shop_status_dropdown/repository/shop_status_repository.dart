import 'package:agency_banking_rpcs/agency/update_shop_status_rpc.dart';
import 'package:agency_banking_rpcs/types/shop_status_enum.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

import '../../../core/auth/rpc_auth_provider.dart';
import '../../../core/service_locator.dart';
import '../../../flavors/flavors.dart';
import '../mock_rpc_impls/mock_update_shop_status.dart';

class ShopStatusRepository {
  Future<LeoRPCResult<UpdateShopStatusResponse, Never>> updateShopStatus(
    ShopStatusEnum newShopStatus,
  ) async {
    final request = UpdateShopStatusRequest(newShopStatus: newShopStatus);
    final impl =
        currentFlavor.isMock
            ? MockShopStatusImpl()
            : UpdateShopStatusRPCImpl(
              client: apiClient,
              authenticationProvider: RPCAuthProvider.instance,
            );

    return await impl.execute(request);
  }
}
