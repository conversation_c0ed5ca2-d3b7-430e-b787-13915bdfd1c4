import 'package:agency_banking_rpcs/types/coordinate_type.dart';
import 'package:agency_banking_rpcs/types/shop_status_enum.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'blocs/shop_status_bloc.dart';

const _shopStatusMaxLines = 1;

class ShopStatusDropDown extends StatefulWidget {
  final ShopStatusEnum initialShopStatus;

  /// This callback will be called when the shop status has been changed successfully.
  final ValueChanged<ShopStatusEnum?>? onShopStatusChanged;
  final Coordinate? shopCoordinate;

  final bool isEnabled;

  const ShopStatusDropDown({
    Key? key,
    required this.initialShopStatus,
    required this.shopCoordinate,
    this.onShopStatusChanged,
    required this.isEnabled,
  }) : super(key: key);

  @override
  ShopStatusDropDownState createState() => ShopStatusDropDownState();
}

class ShopStatusDropDownState extends State<ShopStatusDropDown> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (blocContext) => ShopStatusBloc(
            shopCoordinate: widget.shopCoordinate,
            currentShopStatus: widget.initialShopStatus,
            onShopStatusChanged: widget.onShopStatusChanged,
          ),
      child: _ShopStatusDropdownMenu(isEnabled: widget.isEnabled),
    );
  }
}

class _ShopStatusDropdownMenu extends StatefulWidget {
  final bool isEnabled;

  const _ShopStatusDropdownMenu({Key? key, required this.isEnabled})
    : super(key: key);

  @override
  State<_ShopStatusDropdownMenu> createState() =>
      _ShopStatusDropdownMenuState();
}

class _ShopStatusDropdownMenuState extends State<_ShopStatusDropdownMenu> {
  late final _shopStatusBloc = BlocProvider.of<ShopStatusBloc>(context);
  final _subMenuFocusNode = FocusNode();
  late final _dropdownContentColor =
      widget.isEnabled
          ? context.appColors.genericWhiteColor
          : context.appColors.dropdownDisabledColor;

  @override
  Widget build(BuildContext context) {
    return _buildDropdownButton();
  }

  Widget _buildDropdownButton() {
    return BlocBuilder<ShopStatusBloc, ShopStatusState>(
      bloc: BlocProvider.of<ShopStatusBloc>(context),
      builder: (context, state) {
        return SizedBox(
          // SubmenuButton is not able to calculate its intrinsic width.
          // Hence wrapping this component with a fixed width container having
          // a width equal to the maximum possible width from the designs (i.e. 81px).
          // The width of 81px is hard-coded here, as it shouldn't be generically
          // available throughout the project code, and is very specific for this case here.
          width: 81,
          child: SubmenuButton(
            focusNode: _subMenuFocusNode,
            menuChildren:
                widget.isEnabled
                    ? _getShopStatusDropdownMenuEntry()
                    : List.empty(),
            menuStyle: MenuStyle(
              elevation: WidgetStateProperty.resolveWith((_) => dimenFour),
              backgroundColor: WidgetStateProperty.resolveWith(
                (states) => context.appColors.surfaceColor,
              ),
              shadowColor: WidgetStateProperty.resolveWith(
                (_) => Colors.black.withValues(alpha: dimenPointTwoFive),
              ),
            ),
            onClose: _subMenuFocusNode.unfocus,
            style: ButtonStyle(
              padding: WidgetStatePropertyAll(EdgeInsets.zero),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    _getLeadingIcon(_shopStatusBloc.currentShopStatus),
                    horizontalGapFour,
                    Text(
                      _getLocalizedShopStatusString(
                        _shopStatusBloc.currentShopStatus,
                      ).overflow,
                      style: context.appTextStyles.smallText1Bold.copyWith(
                        color: _dropdownContentColor,
                      ),
                      maxLines: _shopStatusMaxLines,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                horizontalGapFour,
                IconWidget(
                  assetName: ABAssets.arrowDropDownIcon,
                  iconColor: _dropdownContentColor,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Widget> _getShopStatusDropdownMenuEntry() {
    return List.generate(ShopStatusEnum.values.length, (index) {
      final shopStatusValue = ShopStatusEnum.values[index];
      return MenuItemButton(
        leadingIcon: const SizedBox(),
        trailingIcon: const SizedBox(),
        style: ElevatedButton.styleFrom(
          backgroundColor:
              _shopStatusBloc.currentShopStatus == shopStatusValue
                  ? context.appColors.neutralShade4Color
                  : null,
        ),
        onPressed: () {
          _subMenuFocusNode.unfocus();
          _shopStatusBloc.add(
            ShopStatusEvent.updateShopStatus(context, shopStatusValue),
          );
        },
        child: Row(
          children: [
            _getLeadingIcon(shopStatusValue),
            horizontalGapEight,
            Text(
              _getLocalizedShopStatusString(shopStatusValue).overflow,
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShade1Color,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: _shopStatusMaxLines,
            ),
            horizontalGapSixteen,
          ],
        ),
      );
    });
  }

  Widget _getLeadingIcon([ShopStatusEnum? value]) {
    final shopStatusBloc = BlocProvider.of<ShopStatusBloc>(context);
    return Container(
      height: dimenEight,
      width: dimenEight,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _getShopStatusColor(value ?? shopStatusBloc.currentShopStatus),
      ),
    );
  }

  String _getLocalizedShopStatusString(ShopStatusEnum shopStatus) {
    switch (shopStatus) {
      case ShopStatusEnum.SHOP_OPEN:
        return context.localizations.open;
      case ShopStatusEnum.SHOP_CLOSED:
        return context.localizations.closed;
    }
  }

  Color _getShopStatusColor(ShopStatusEnum shopStatus) {
    switch (shopStatus) {
      case ShopStatusEnum.SHOP_OPEN:
        return context.appColors.successShade2Color;
      case ShopStatusEnum.SHOP_CLOSED:
        return context.appColors.errorColor;
    }
  }
}
