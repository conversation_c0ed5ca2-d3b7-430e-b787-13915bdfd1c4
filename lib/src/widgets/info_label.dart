import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class InfoLabel extends StatelessWidget {
  const InfoLabel({
    Key? key,
    required this.title,
    this.bodyText,
    this.actionButton,
  }) : super(key: key);

  final String title;
  final String? bodyText;
  final Widget? actionButton;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: verticalPaddingEight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: context.appTextStyles.smallText2.copyWith(
                  color: context.appColors.neutralShade7Color,
                ),
              ),
              if (bodyText != null) ...{
                verticalGapFour,
                Text(
                  bodyText!,
                  style: context.appTextStyles.labelText2Bold.copyWith(
                    color: context.appColors.neutralShade1Color,
                  ),
                  maxLines: bodyTextMaxLines,
                  overflow: TextOverflow.ellipsis,
                ),
              },
            ],
          ),
          if (actionButton != null) actionButton!,
        ],
      ),
    );
  }
}
