import 'package:bcn_agency_banking_flutter/src/models/custom_bottom_navigation_bar_item.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

import '../resources/dimensions.dart';
import 'icon_widget.dart';

const _bottomNavigationBarHeight = dimenFortyEight;
const _bottomNavigationBarIconSize = dimenTwenty;

class CustomBottomNavigationBar extends StatelessWidget {
  const CustomBottomNavigationBar({
    Key? key,
    required this.currentIndex,
    required this.onItemTapped,
    required this.navBarItems,
  }) : super(key: key);
  final int currentIndex;
  final void Function(int) onItemTapped;
  final List<CustomBottomNavigationBarItem> navBarItems;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // Add bottom insets padding to the bottom navigation bar height
      // so that the content inside `_buildBottomNavigationBar` does not overlap
      // with the device's insets padding and falls in the safe area of the device.
      height: _bottomNavigationBarHeight + context.mq.padding.bottom,
      child: _buildBottomNavigationBar(context),
    );
  }

  Widget _buildBottomNavigationBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: context.appColors.neutralShade3Color),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: List.generate(navBarItems.length, (index) {
          return Expanded(
            child: _bottomNavigationBarItem(context, navBarItems[index], index),
          );
        }),
      ),
    );
  }

  Widget _bottomNavigationBarItem(
    BuildContext context,
    CustomBottomNavigationBarItem navBarItem,
    int index,
  ) {
    // Align the Bottom navigation bar item at the center
    // top of the over all height allotted.
    // This will ensure that the content inside the
    // navigation bar does not overlap with device's insets
    // padding.
    final itemColor =
        currentIndex == index
            ? context.appColors.primaryLightColor
            : context.appColors.neutralShade2Color;
    return Align(
      alignment: Alignment.topCenter,
      child: InkWell(
        borderRadius: BorderRadius.circular(dimenFortyEight),
        onTap: () => onItemTapped(index),
        child: Container(
          decoration: const BoxDecoration(shape: BoxShape.circle),
          height: _bottomNavigationBarHeight,
          alignment: Alignment.topCenter,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconWidget(
                assetName: navBarItem.iconLocation,
                iconColor: itemColor,
                height: _bottomNavigationBarIconSize,
                width: _bottomNavigationBarIconSize,
              ),
              verticalGapTwo,
              Text(
                navBarItem.label,
                style: context.appTextStyles.labelText4.copyWith(
                  color: itemColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
