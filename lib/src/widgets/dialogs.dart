import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';

import '../core/location_services/location_service.dart';
import '../core/logger.dart';
import '../models/location_details.dart';
import '../resources/dimensions.dart';
import '../utils/constants.dart';
import 'primary_text_button.dart';

class AgencyAppDialog {
  AgencyAppDialog._();

  /// Shows App Dialog with [actions].
  /// Returns an instance of Type [T].
  /// when dialog is closed with [Navigator.pop].
  static Future<T?> showAppDialog<T>({
    required BuildContext context,
    String? contentText,
    Widget Function(BuildContext)? contentWidget,
    required List<Widget> Function(BuildContext) actions,
  }) async {
    l.v("Showing App Dialog");
    if (!((contentText != null) ^ (contentWidget != null))) {
      throw DeveloperError(
        "Content text and content widget both cannot be null or not null.",
      );
    }
    return await showDialog<T>(
      context: context,
      builder: (dialogContext) {
        return alertDialog(
          dialogContext: dialogContext,
          actions: actions,
          contentWidget: contentWidget,
          contentText: contentText,
        );
      },
    );
  }

  static Widget alertDialog({
    String? contentText,
    required BuildContext dialogContext,
    Widget Function(BuildContext)? contentWidget,
    required List<Widget> Function(BuildContext) actions,
  }) {
    return PopScope(
      canPop: false,
      child: AlertDialog(
        content: Container(
          width: dialogWidth,
          padding: dialogContentPadding,
          child:
              contentText != null
                  ? Text(
                    contentText,
                    style: dialogContext.appTextStyles.smallText1.copyWith(
                      color: dialogContext.appColors.neutralShade1Color,
                    ),
                  )
                  : contentWidget!(dialogContext),
        ),
        actions: actions.call(dialogContext),
        contentPadding: EdgeInsets.zero,
        actionsPadding: actionContentPadding,
      ),
    );
  }

  /// Shows an error dialog with Okay button.
  /// [onOkay] parameter takes shown dialog's context.
  /// Dismissible behaviour can be changed using [isDismissible] boolean.
  /// [isDismissible] should be false for flow cancelling errors.
  static Future<T?> showErrorDialog<T>({
    required BuildContext context,
    required String contentText,
    bool isDismissible = false,
    void Function(BuildContext)? onOkay,
    String? buttonText,
  }) async {
    l.d('''Showing Error dialog.
    contentText: $contentText,
    ''');
    return await showAppDialog(
      context: context,
      contentText: contentText,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: buttonText ?? context.localizations.okay,
              onTap: () {
                dialogContext.navigator.pop();
                onOkay?.call(dialogContext);
              },
            ),
          ],
    );
  }

  /// Shows Non Dismissible Confirmation Dialog with Cancel and Confirm Button.
  /// Resolves with true, if confirm button is pressed.
  /// Resolves with false, if cancel button is pressed.
  static Future<bool?> showConfirmationDialog({
    required BuildContext context,
    required String contentText,
  }) async {
    l.d("Showing Confirmation dialog. contentText: $contentText");
    return await showAppDialog(
      context: context,
      contentText: contentText,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.cancel.toUpperCase(),
              onTap: () => dialogContext.navigator.pop(false),
            ),
            PrimaryTextButton(
              text: context.localizations.confirm.toUpperCase(),
              onTap: () => dialogContext.navigator.pop(true),
            ),
          ],
    );
  }

  /// Shows Spinner Dialog with optional [contentText].
  /// If [contentText] is null, then Spinner will be shown
  /// in the center of the dialog.
  static Future<void> showSpinnerDialog(
    BuildContext context, {
    String? contentText,
  }) async {
    l.d("Showing Spinner dialog");
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (buildContext) {
        return PopScope(
          canPop: false,
          child: Dialog(
            child: Container(
              width: dialogWidth,
              padding: dialogContentPadding,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  const Spinner(
                    height: dimenForty,
                    width: dimenForty,
                    strokeWidth: strokeWidth,
                  ),
                  horizontalGapTwelve,
                  Expanded(
                    child: Text(
                      contentText ?? context.localizations.loading,
                      style: buildContext.appTextStyles.smallText1.copyWith(
                        color: buildContext.appColors.neutralShade1Color,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Resolves with true if the user presses "Confirm" button.
  static Future<bool?> showCancelConfirmation(
    BuildContext context,
    String processName,
  ) async {
    l.d("Showing Cancel confirmation. processName: $processName");
    return await showConfirmationDialog(
      context: context,
      contentText: context.localizations.cancellationConfirmationMessage(
        processName,
      ),
    );
  }

  /// Resolves with true if the user presses "Confirm" button.
  static Future<bool?> showDiscardChangesConfirmation(
    BuildContext context,
  ) async {
    return await showConfirmationDialog(
      context: context,
      contentText: context.localizations.confirmDiscardChangesMessage,
    );
  }

  static void showDistanceLimitMessage(
    BuildContext context,
    int allowedDistance,
  ) {
    showErrorDialog(
      context: context,
      contentText: context.localizations.distanceLimitMessage(allowedDistance),
    );
  }

  static void showAgentLocationInvalidDialog(
    BuildContext context,
    LocationDetails shopDetails, {
    String? contentText,
    VoidCallback? onCancel,
  }) {
    final locationService = locator<LocationService>();
    l.d('''Show Agent Location Invalid dialog. 
    shopDetails: $shopDetails,
    contentText: $contentText''');
    showAppDialog(
      context: context,
      contentText:
          contentText ??
          context.localizations.locationRadiusCheck(
            locationService.agentMaxAllowedDistance,
          ),
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.cancel,
              onTap: onCancel ?? () => dialogContext.navigator.pop(),
            ),
            PrimaryTextButton(
              text: context.localizations.tryAgain,
              onTap: () async {
                // Pop the alert dialog.
                dialogContext.navigator.pop();

                AgencyAppDialog.showSpinnerDialog(context);
                await Future.delayed(locationRadiusCheckRetryDelay);
                final shouldAllow = locationService
                    .checkCurrentLocationWithinAcceptableRange(
                      location: shopDetails,
                    );
                // Pop the spinner dialog.
                if (context.mounted) {
                  context.rootNavigator.pop();
                }

                if (!shouldAllow && context.mounted) {
                  showAgentLocationInvalidDialog(context, shopDetails);
                }
              },
            ),
          ],
    );
  }

  static void showCameraPermissionDeniedError(BuildContext context) {
    AgencyAppDialog.showAppDialog(
      context: context,
      contentText: context.localizations.needCameraPermission,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.openSettings,
              onTap: () {
                dialogContext.rootNavigator.pop();

                // Open App Settings.
                Geolocator.openAppSettings();
              },
            ),
          ],
    );
  }

  static void showPhotosPermissionDeniedError(BuildContext context) {
    AgencyAppDialog.showAppDialog(
      context: context,
      contentText: context.localizations.needPhotosPermission,
      actions:
          (dialogContext) => [
            PrimaryTextButton(
              text: context.localizations.openSettings,
              onTap: () {
                dialogContext.rootNavigator.pop();

                // Open App Settings.
                Geolocator.openAppSettings();
              },
            ),
          ],
    );
  }
}
