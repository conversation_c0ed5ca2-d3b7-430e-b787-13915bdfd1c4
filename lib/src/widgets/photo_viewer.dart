import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

/// Widget used to display image or image empty state with background color and
/// border.
class PhotoViewer extends StatelessWidget {
  final Widget image;

  const PhotoViewer({super.key, required this.image});

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      height: dimenOneSixty,
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.appColors.neutralShade4Color,
        borderRadius: BorderRadius.circular(dimenFour),
      ),
      child: image,
    );
  }
}
