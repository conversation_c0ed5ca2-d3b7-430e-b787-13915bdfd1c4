import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Widget used to display the icon SVG image.
/// Pass the SVG asset image path as the [assetName] to render
/// the SVG image as an icon with respective colors on dark mode
/// and light mode.
class IconWidget extends StatelessWidget {
  /// The SVG asset image path of the icon.
  final String assetName;

  /// The color of the icon. This defaults to
  /// `context.updatedColors.neutralShade5Color`.
  final Color? iconColor;

  /// Height of the icon. Defaults to 24px.
  final double? height;

  /// Width of the icon. Defaults to 24px.
  final double? width;

  /// How to inscribe the icon into
  /// the space allocated by the parent.
  /// Defaults to [BoxFit.scaleDown].
  final BoxFit fit;

  const IconWidget({
    super.key,
    required this.assetName,
    this.iconColor,
    this.height = defaultIconSize,
    this.width = defaultIconSize,
    this.fit = BoxFit.scaleDown,
  });

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      assetName,
      height: height,
      width: width,
      colorFilter: ColorFilter.mode(
        iconColor ?? context.appColors.neutralShade5Color,
        // Displays the `src`, ie.. the SVG image,
        // only where the background and the SVG image overlap.
        BlendMode.srcIn,
      ),
      // SVGPicture widget likes to expand to size of
      // it's parent widget.
      // Defaulting to BoxFit.scaleDown, scales it down to intended size.
      fit: fit,
    );
  }
}
