import 'package:bcn_agency_banking_flutter/src/core/data_source/document_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/no_internet_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/server_error_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/spinner.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dedwig/dedwig.dart';
import 'package:flutter/material.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

/// Layout widget used for screens using cached network image.
/// Displays a full screen loader when the image is loading.
/// Displays a full screen error if an error occurs.
/// The network image is cached for a period of [stalePeriod] from when it is first
/// fetched. After this period the network call is made again to
/// fetch the image.
/// The [onImageLoaded] callback method is passed to
/// [CachedNetworkImage.imageBuilder]. Thus the widget returned from
/// the [onImageLoaded] method is displayed when the image is loaded after
/// caching is completed on the [CachedNetworkImage] widget.
class ImageDisplayLayout extends StatefulWidget {
  final LeoUUID? photoId;
  final LeoUUID requestId;
  final String? appBarTitle;
  final PrimaryAppBar? appBar;

  /// Callback function for when the image is loaded and ready to be displayed.
  final Widget Function(BuildContext, ImageProvider?) onImageLoaded;

  const ImageDisplayLayout({
    super.key,
    required this.photoId,
    required this.requestId,
    required this.onImageLoaded,
    this.appBarTitle,
    this.appBar,
  });

  @override
  State<ImageDisplayLayout> createState() => _ImageDisplayLayoutState();
}

class _ImageDisplayLayoutState extends State<ImageDisplayLayout> {
  final DocumentService _documentService = locator<DocumentService>();
  Future<FileInfo?>? _getCachedImage;
  bool _isReCachingRequired = true;

  @override
  void initState() {
    super.initState();
    _initializeCachedImageFuture();
  }

  @override
  Widget build(BuildContext context) {
    _initializeCachedImageFuture();
    return Scaffold(
      appBar:
          widget.appBar ??
          PrimaryAppBar(title: widget.appBarTitle, isCancellable: false),
      body: SafeArea(
        top: false,
        left: false,
        right: false,
        // If photoId is null, null is passed in the `onImageLoaded`
        // callback function. This implies that the image does not exist and
        // an empty state must be shown in place of the image,
        child:
            widget.photoId != null
                ? FutureBuilder<FileInfo?>(
                  future: _getCachedImage,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      if (snapshot.hasData) {
                        return widget.onImageLoaded(
                          context,
                          FileImage(snapshot.data!.file),
                        );
                      } else {
                        return _errorWidget(context, snapshot.error);
                      }
                    } else {
                      return const Spinner();
                    }
                  },
                )
                : widget.onImageLoaded(context, null),
      ),
    );
  }

  void _initializeCachedImageFuture() {
    if (widget.photoId != null && _isReCachingRequired) {
      _isReCachingRequired = false;
      // This forces the future-builder to build
      _getCachedImage = _updateCacheImage();
    }
  }

  Future<FileInfo> _updateCacheImage() async {
    FileInfo? cachedImage = await cacheManager.getFileFromCache(
      widget.photoId!.uuid,
    );
    // Image not found in cache memory.
    if (cachedImage == null) {
      Uri photoUrl = await _documentService.getPhotoUrl(
        widget.requestId,
        widget.photoId!,
      );
      cachedImage = await cacheManager.downloadFile(
        photoUrl.toString(),
        key: widget.photoId!.uuid,
      );
    }
    return cachedImage;
  }

  Widget _errorWidget(BuildContext context, Object? error) {
    if (error != null && error is NetworkException) {
      return NoInternetWidget(
        onRetryButtonClicked: () {
          setState(() {
            _isReCachingRequired = true;
          });
        },
      );
    }
    return ServerErrorWidget(
      onRetryButtonClicked: () {
        setState(() {
          _isReCachingRequired = true;
        });
      },
    );
  }
}
