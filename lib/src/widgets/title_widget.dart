import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class TitleWidget extends StatelessWidget {
  final String title;

  const TitleWidget({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      color: context.appColors.titleBackgroundColor,
      padding: const EdgeInsets.symmetric(
        horizontal: dimenSixteen,
        vertical: dimenEight,
      ),
      child: Text(
        title,
        style: context.appTextStyles.smallText2Bold.copyWith(
          color: context.appColors.neutralShadeDefaultColor,
        ),
      ),
    );
  }
}
