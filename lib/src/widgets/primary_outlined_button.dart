import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class PrimaryOutlinedButton extends StatelessWidget {
  const PrimaryOutlinedButton({
    Key? key,
    required this.onPressed,
    required this.labelText,
    this.isEnabled = true,
    this.width,
  }) : super(key: key);
  final VoidCallback onPressed;
  final String labelText;
  final bool isEnabled;
  final double? width;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      child: OutlinedButton(
        onPressed:
            isEnabled
                ? () {
                  dismissKeyboard();
                  onPressed();
                }
                : null,
        child: Text(
          labelText.toUpperCase(),
          style: context.appTextStyles.buttonText1.copyWith(
            color:
                isEnabled
                    ? context.appColors.primaryLightColor
                    : context.appColors.neutralShade8Color,
          ),
        ),
      ),
    );
  }
}
