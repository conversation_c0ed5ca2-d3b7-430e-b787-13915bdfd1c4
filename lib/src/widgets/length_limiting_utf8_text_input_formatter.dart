import 'dart:math';

import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LengthLimitingUtf8TextInputFormatter extends TextInputFormatter {
  final int? _maxLength;

  LengthLimitingUtf8TextInputFormatter(this._maxLength)
    : assert(_maxLength == null || _maxLength == -1 || _maxLength > 0);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (_maxLength != null &&
        _maxLength > 0 &&
        newValue.text.utf8Length > _maxLength) {
      // Enforced to return a truncated value.
      if (oldValue.text.utf8Length == _maxLength &&
          oldValue.selection.isCollapsed) {
        return oldValue;
      }
      return _truncate(newValue, _maxLength);
    }
    return newValue;
  }

  TextEditingValue _truncate(TextEditingValue value, int maxLength) {
    String newValue = '';
    if (value.text.utf8Length > maxLength) {
      int length = 0;
      value.text.characters.takeWhile((char) {
        int bytes = char.utf8Length;
        if (length + bytes <= maxLength) {
          newValue += char;
          length += bytes;
          return true;
        }
        return false;
      });
    }
    return TextEditingValue(
      text: newValue,
      selection: value.selection.copyWith(
        baseOffset: min(value.selection.start, newValue.length),
        extentOffset: min(value.selection.end, newValue.length),
      ),
      composing:
          !value.composing.isCollapsed &&
                  newValue.length > value.composing.start
              ? TextRange(
                start: value.composing.start,
                end: min(value.composing.end, newValue.length),
              )
              : TextRange.empty,
    );
  }
}
