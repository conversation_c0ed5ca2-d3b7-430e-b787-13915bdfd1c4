import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:flutter/material.dart';

class InfoLabelList extends StatelessWidget {
  const InfoLabelList({Key? key, required this.children}) : super(key: key);

  final List<InfoLabel> children;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) => children[index],
      itemCount: children.length,
    );
  }
}
