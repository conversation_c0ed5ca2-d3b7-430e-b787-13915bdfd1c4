import 'dart:io';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/custom_bottom_sheet.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/user_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:path_provider/path_provider.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart'
    as phone_number_parser;
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:sprintf/sprintf.dart';
import 'package:url_launcher/url_launcher.dart';

import '../helpers/helpers.dart';
import '../utils/formatters/phone_number_formatter.dart';

class TransactionSuccessScreen extends StatefulWidget {
  static const id = '/transaction-success-screen';

  const TransactionSuccessScreen({
    Key? key,
    required this.transactionStatusDetail,
    required this.succeededAt,
    required this.amount,
    required this.recordId,
    this.onDone,
  }) : super(key: key);

  final TransactionStatusDetail transactionStatusDetail;
  final DateTime succeededAt;
  final Amount amount;
  final String recordId;
  final VoidCallback? onDone;

  @override
  State<TransactionSuccessScreen> createState() =>
      _TransactionSuccessScreenState();
}

class _TransactionSuccessScreenState extends State<TransactionSuccessScreen> {
  final ScreenshotController _screenshotController = ScreenshotController();

  bool _isSharingUnderProcess = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }
        if (widget.onDone != null) {
          widget.onDone!();
        } else {
          navigateToHomeScreen(context);
        }
        return;
      },
      child: Scaffold(
        appBar: _buildAppBar(context),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Screenshot(
                  controller: _screenshotController,
                  // Screenshot widget applies the background color transparent
                  // as we are not enclosing scaffold in it. Adding this
                  // Material layer fixes that. This will provide the color to
                  // the background of the screenshot.
                  // Material is preferred over simple Container so as to avoid
                  // issues with the ripple effects on child widgets.
                  child: Material(
                    color: context.appColors.backgroundColor,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTransactionStatusSection(context),
                        _buildTransactionCard(context),
                        _buildDivider(),
                        ..._buildTransactionStatusDetailWidgets(context),
                        _buildContactSupportCard(context),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            _buildDoneButton(context),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      automaticallyImplyLeading: false,
      actions: <Widget>[_buildShareButton(context)],
      backgroundColor: context.appColors.successShade3Color,
    );
  }

  Widget _buildShareButton(BuildContext context) {
    return IconButton(
      onPressed: () async {
        if (_isSharingUnderProcess) return;
        setState(() => _isSharingUnderProcess = true);
        final screenshot = await _screenshotController.capture(
          pixelRatio: context.mq.devicePixelRatio,
        );
        await _saveAndShareScreenshot(screenshot!);
        if (mounted) setState(() => _isSharingUnderProcess = false);
      },
      icon: IconWidget(
        assetName: ABAssets.shareAltIcon,
        iconColor: context.appColors.genericWhiteColor,
      ),
    );
  }

  Container _buildTransactionStatusSection(BuildContext context) {
    return Container(
      width: double.infinity,
      color: context.appColors.successShade3Color,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          verticalGapSixteen,
          IconWidget(
            height: dimenFortyEight,
            width: dimenFortyEight,
            iconColor: context.appColors.genericWhiteColor,
            assetName: ABAssets.checkCircleIcon,
          ),
          verticalGapEight,
          _buildTransactionSuccessfulText(context),
          verticalGapFour,
          _buildTransactionTimestampString(context),
          verticalGapSixteen,
        ],
      ),
    );
  }

  Text _buildTransactionSuccessfulText(BuildContext context) {
    return Text(
      context.localizations.transactionSuccessful,
      style: context.appTextStyles.titleBold.copyWith(
        color: context.appColors.genericWhiteColor,
      ),
    );
  }

  Text _buildTransactionTimestampString(BuildContext context) {
    return Text(
      DateFormatter.transactionDateTimeFormat(
        context,
      ).format(widget.succeededAt.toLocal()),
      style: context.appTextStyles.smallText1.copyWith(
        color: context.appColors.genericWhiteColor,
      ),
    );
  }

  Widget _buildTransactionCard(BuildContext context) {
    return Padding(
      padding: commonScreenPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            context.localizations.transactionDetailsString,
            style: context.appTextStyles.smallText2Bold.copyWith(
              color: context.appColors.neutralShadeDefaultColor,
            ),
          ),
          verticalGapSixteen,
          Text(
            widget.amount.localisedFormattedAmount,
            style: context.appTextStyles.welcomeHeadingBold.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
          ),
          verticalGapSixteen,
          Text(
            context.localizations.transactionIdString,
            style: context.appTextStyles.smallText1.copyWith(
              color: context.appColors.neutralShade7Color,
            ),
          ),
          verticalGapFour,
          Text(
            widget.recordId,
            style: context.appTextStyles.smallText1Bold.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
          ),
          if (widget.transactionStatusDetail.description != null) ...{
            verticalGapSixteen,
            Text(
              BCNLocale.getLocalisedString(
                context,
                widget.transactionStatusDetail.description!,
              ),
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShade7Color,
              ),
            ),
          },
        ],
      ),
    );
  }

  Divider _buildDivider() => const Divider(thickness: 1);

  List<Widget> _buildTransactionStatusDetailWidgets(BuildContext context) {
    return widget.transactionStatusDetail.itemDetail.map((item) {
      return item.valueType.when(
        largeIconWithText:
            (largeIconWithTextItem) => _buildBeneficiaryCard(
              item.label,
              largeIconWithTextItem,
              context,
            ),
        smallIconWithText:
            (smallIconWithTextItem) =>
                throw DeveloperError(
                  'widget not implemented for rpc.smallIconWithText',
                ),
        text:
            (textItem) =>
                throw DeveloperError('widget not implemented for rpc.text'),
        copyableText:
            (copyableTextItem) =>
                _buildRequestIdCard(item.label, copyableTextItem, context),
      );
    }).toList();
  }

  Widget _buildBeneficiaryCard(
    LocalizedText label,
    TransactionStatusItemDetailValueTypeEnumLargeIconWithText
    itemWithLargeIconAndText,
    BuildContext context,
  ) {
    final titleString = _getItemString(itemWithLargeIconAndText.title);
    late final String? descriptionString;
    if (itemWithLargeIconAndText.description != null) {
      descriptionString = _getItemString(itemWithLargeIconAndText.description!);
    }
    return Column(
      children: [
        Padding(
          padding: commonScreenPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                BCNLocale.getLocalisedString(context, label),
                style: context.appTextStyles.smallText2Bold.copyWith(
                  color: context.appColors.neutralShadeDefaultColor,
                ),
              ),
              verticalGapSixteen,
              Padding(
                padding:
                    itemWithLargeIconAndText.description != null
                        ? verticalPaddingSixteen
                        : verticalPaddingEight,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    UserImage(
                      imageUrl: BCNLocale.getLocalisedImageUrl(
                        context,
                        itemWithLargeIconAndText.image,
                      ),
                      height: _imageHeightAndWidth,
                      width: _imageHeightAndWidth,
                    ),
                    horizontalGapSixteen,
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          titleString,
                          style: context.appTextStyles.labelText2.copyWith(
                            color: context.appColors.neutralShade1Color,
                          ),
                        ),
                        if (itemWithLargeIconAndText.description != null) ...{
                          verticalGapFour,
                          Text(
                            descriptionString!,
                            style: context.appTextStyles.labelText3.copyWith(
                              color: context.appColors.neutralShade7Color,
                            ),
                          ),
                        },
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        _buildDivider(),
      ],
    );
  }

  Column _buildRequestIdCard(
    LocalizedText label,
    TransactionStatusItemDetailValueTypeEnumCopyableText itemWithCopyableText,
    BuildContext context,
  ) {
    return Column(
      children: [
        Padding(
          padding: commonScreenPadding,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                BCNLocale.getLocalisedString(context, label),
                style: context.appTextStyles.smallText2Bold.copyWith(
                  color: context.appColors.neutralShadeDefaultColor,
                ),
              ),
              verticalGapSixteen,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    itemWithCopyableText.text,
                    style: context.appTextStyles.smallText1Bold.copyWith(
                      color: context.appColors.neutralShade1Color,
                    ),
                  ),
                  IconButton(
                    constraints: const BoxConstraints(
                      maxHeight: dimenTwenty,
                      maxWidth: dimenTwenty,
                    ),
                    padding: EdgeInsets.zero,
                    onPressed: () async {
                      await Clipboard.setData(
                        ClipboardData(text: itemWithCopyableText.text),
                      );
                      if (context.mounted) {
                        _showToastNotification(context);
                      }
                    },
                    icon:
                        _isSharingUnderProcess
                            ? const SizedBox(height: kMinInteractiveDimension)
                            : IconWidget(
                              iconColor: context.appColors.primaryLightColor,
                              assetName: ABAssets.copyIcon,
                            ),
                  ),
                ],
              ),
            ],
          ),
        ),
        _buildDivider(),
      ],
    );
  }

  Widget _buildContactSupportCard(BuildContext context) {
    return _isSharingUnderProcess
        ? verticalGapFiftySix
        : Padding(
          padding: commonScreenPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.localizations.needHelp,
                style: context.appTextStyles.smallText2Bold.copyWith(
                  color: context.appColors.neutralShadeDefaultColor,
                ),
              ),
              verticalGapSixteen,
              _buildContactSupportButton(context),
              verticalGapSixteen,
              Text(
                context.localizations.contactSupportHelperString,
                style: context.appTextStyles.smallText2.copyWith(
                  color: context.appColors.neutralShade7Color,
                ),
              ),
            ],
          ),
        );
  }

  CustomBottomSheet _buildCustomBottomSheet(BuildContext context) {
    return CustomBottomSheet(
      children: [
        Padding(
          padding: horizontalPaddingSixteen,
          child: Text(
            context.localizations.needHelp,
            style: context.appTextStyles.smallText1Bold.copyWith(
              color: context.appColors.neutralShade1Color,
            ),
          ),
        ),
        verticalGapSixteen,
        _buildBottomSheetIconWithLabelButtons(
          context,
          IconWidget(
            assetName: ABAssets.phoneIcon,
            iconColor: context.appColors.neutralShade1Color,
          ),
          context.localizations.callUs,
          onTap: () async {
            // Pop the bottom sheet as soon as user taps on an option.
            context.navigator.pop();
            final phoneNumberURLString = sprintf(phoneNumberURL, [
              supportPhoneNumber,
            ]);
            if (await canLaunchUrl(Uri.parse(phoneNumberURLString))) {
              await launchExternalUrl(phoneNumberURLString);
            } else {
              if (context.mounted) {
                AgencyAppDialog.showErrorDialog(
                  context: context,
                  contentText: context.localizations.noDialerAppFoundError,
                );
              }
            }
          },
        ),
        _buildBottomSheetIconWithLabelButtons(
          context,
          IconWidget(
            assetName: ABAssets.envelopeIcon,
            iconColor: context.appColors.neutralShade1Color,
          ),
          context.localizations.sendEmail,
          onTap: () async {
            // Pop the bottom sheet as soon as user taps on an option.
            context.navigator.pop();
            if (await canLaunchUrl(Uri.parse(_emailUriString))) {
              await launchExternalUrl(_emailUriString);
            } else {
              if (context.mounted) {
                AgencyAppDialog.showErrorDialog(
                  context: context,
                  contentText: context.localizations.noEmailAppFoundError(
                    supportEmailAddress,
                  ),
                );
              }
            }
          },
        ),
      ],
    );
  }

  Widget _buildBottomSheetIconWithLabelButtons(
    BuildContext context,
    IconWidget icon,
    String labelText, {
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: horizontalPaddingSixteen,
        child: SizedBox(
          height: dimenFiftySix,
          width: double.infinity,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              icon,
              horizontalGapSixteen,
              Text(
                labelText,
                style: context.appTextStyles.smallText1.copyWith(
                  color: context.appColors.neutralShade1Color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactSupportButton(BuildContext context) {
    return InkWell(
      onTap: () {
        showModalBottomSheet(
          shape: const RoundedRectangleBorder(
            borderRadius: bottomSheetBorderRadius,
          ),
          context: context,
          builder: (context) => _buildCustomBottomSheet(context),
        );
      },
      child: Row(
        children: [
          IconWidget(
            iconColor: context.appColors.primaryLightColor,
            assetName: ABAssets.questionCircleIcon,
          ),
          horizontalGapEight,
          Text(
            context.localizations.contactSupport,
            style: context.appTextStyles.smallText1Bold.copyWith(
              color: context.appColors.primaryLightColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDoneButton(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: commonScreenPadding,
        child: PrimaryButton(
          labelText: context.localizations.done,
          onPressed: widget.onDone ?? () => navigateToHomeScreen(context),
        ),
      ),
    );
  }

  void _showToastNotification(BuildContext context) {
    Fluttertoast.showToast(
      msg: context.localizations.copiedToClipboardToastMessage,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: context.appColors.neutralShade1Color,
      textColor: context.appColors.neutralShade4Color,
      // This method doesn't accept a TextStyle parameter but only font-size.
      // The figma-design says that the text should be in labelText2 (whose size
      // is 14px). Setting fontSize parameter as dimenFourteen is the closest we
      // can get to the design.
      fontSize: dimenFourteen,
    );
  }

  // Formats given string to phone number string.
  // If the string received for formatting isn't a phone number then it
  // returns the original string.
  String _getItemString(String itemString) {
    try {
      return PhoneNumberFormatter.format(itemString);
    } on phone_number_parser.PhoneNumberException {
      return itemString;
    }
  }

  Future<void> _saveAndShareScreenshot(Uint8List bytes) async {
    final directory = await getApplicationDocumentsDirectory();
    final image = File('${directory.path}/${widget.recordId}.png');

    if (mounted) {
      final box = context.findRenderObject() as RenderBox?;
      await image.writeAsBytes(bytes);
      if (mounted && box!.attached) {
        await Share.shareXFiles(
          [XFile(image.path)],
          // For iPad and iPhone 6 devices,
          // We need to set sharePositionOrigin.
          // See this: https://pub.dev/packages/share_plus#known-issues.
          sharePositionOrigin: box.localToGlobal(Offset.zero) & box.size,
        );
      }
    }
  }
}

const String _emailUriString = "mailto:$supportEmailAddress";
const double _imageHeightAndWidth = dimenForty;
