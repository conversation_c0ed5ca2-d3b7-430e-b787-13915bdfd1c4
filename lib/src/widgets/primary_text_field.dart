import 'dart:core';

import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'length_limiting_utf8_text_input_formatter.dart';

/// A TextField widget for general text input with app style guide applied.
class PrimaryTextField extends StatelessWidget {
  final String? labelText;
  final String? hintText;

  /// Controls the text being edited.
  /// If null, this widget will create its own TextEditingController.
  final TextEditingController? controller;

  /// [onChanged] parameter when user edits the text field.
  final void Function(String)? onChanged;

  /// Defaults to 20.
  final int maxLength;

  /// Optional input validation and formatting overrides.
  final List<TextInputFormatter>? inputFormatters;

  /// Whether the text can be changed.
  /// When this is set to true, the text cannot be modified by any shortcut or keyboard operation.
  /// The text is still selectable.
  final bool readOnly;

  final Widget? suffixIcon;

  final Color? suffixIconColor;

  final TextInputType? keyboardType;
  final int? minLines;
  final int? maxLines;
  final bool obscureText;
  final BoxConstraints? suffixIconConstraints;

  final FormFieldValidator<String?>? validator;

  /// Defaults to [textFieldContentPadding].
  final EdgeInsets contentPadding;

  /// Defaults to false.
  final bool autofocus;

  /// Defaults to false.
  /// Setting this to true
  /// will pass [maxLength] parameter to
  /// the [TextField.maxLength]
  /// that will make the [TextField] show
  /// the counter. Also make sure that [controller] is not null when
  /// [showCounter] is true.
  /// By default, [TextField.maxLength] will
  /// be null.
  final bool showCounter;
  final String? initialValue;

  const PrimaryTextField({
    Key? key,
    this.onChanged,
    this.labelText,
    this.hintText,
    this.obscureText = false,
    this.controller,
    this.maxLength = defaultTextFieldMaxLength,
    this.inputFormatters,
    this.readOnly = false,
    this.keyboardType,
    this.minLines,
    this.maxLines = defaultTextFieldMaxLines,
    this.suffixIcon,
    this.suffixIconConstraints,
    this.suffixIconColor,
    this.validator,
    this.contentPadding = textFieldContentPadding,
    this.autofocus = false,
    this.showCounter = false,
    this.initialValue,
  }) : assert(showCounter == false || controller != null),
       super(key: key);

  @override
  Widget build(BuildContext context) {
    late final List<TextInputFormatter> allInputFormatters;
    allInputFormatters = inputFormatters != null ? [...inputFormatters!] : [];
    allInputFormatters.add(LengthLimitingUtf8TextInputFormatter(maxLength));
    TextStyle counterTextStyle = context.appTextStyles.smallText1.copyWith(
      color: context.appColors.neutralShade2Color,
    );
    return TextFormField(
      initialValue: initialValue,
      obscureText: obscureText,
      obscuringCharacter: obscureCharacter,
      keyboardType: keyboardType,
      maxLength: showCounter ? maxLength : null,
      minLines: minLines,
      maxLines: maxLines,
      readOnly: readOnly,
      controller: controller,
      inputFormatters: allInputFormatters,
      style: context.appTextStyles.labelText1.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
      onTapOutside: (_) => dismissKeyboard(),
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        suffixIcon: suffixIcon,
        suffixIconConstraints: suffixIconConstraints,
        suffixIconColor: suffixIconColor,
        contentPadding: contentPadding,
        counterStyle: counterTextStyle,
        // If multi line field, then set label alignment to the top.
        alignLabelWithHint: (minLines ?? 0) > 1 ? true : false,
      ),
      onChanged: onChanged,
      validator: validator,
      autofocus: autofocus,
      buildCounter:
          showCounter
              // Custom counter for checking the length in UTF-8 format
              ? (
                context, {
                required currentLength,
                required isFocused,
                maxLength,
              }) {
                return Text(
                  '${controller!.text.utf8Length}/$maxLength',
                  style: counterTextStyle,
                );
              }
              : null,
    );
  }
}
