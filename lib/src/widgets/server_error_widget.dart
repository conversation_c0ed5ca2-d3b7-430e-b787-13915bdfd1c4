import 'package:bcn_agency_banking_flutter/src/features/agent/home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/generic_error_or_empty_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';

/// Widget that is to be displayed in case there server error occurs.
/// This screen is exclusive to [HomeScreen]
class ServerErrorWidget extends StatelessWidget {
  /// Callback function that will be called when `RETRY` button is pressed.
  final VoidCallback onRetryButtonClicked;

  const ServerErrorWidget({required this.onRetryButtonClicked, Key? key})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GenericErrorOrEmptyWidget(
      iconAssetPath: ABAssets.exclamationCircleIcon,
      labelText: context.localizations.somethingWentWrong,
      retryButton: PrimaryTextButton(
        text: context.localizations.retry,
        onTap: onRetryButtonClicked,
      ),
    );
  }
}
