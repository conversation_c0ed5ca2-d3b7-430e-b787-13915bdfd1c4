import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/material.dart';

import '../core/logger.dart';
import '../utils/formatters/amount_text_input_formatter.dart';
import '../utils/validators.dart';

class AmountTextField extends StatelessWidget {
  final String? labelText;

  /// This function is called everytime user initiates or deletes a number
  /// inside the text field.
  /// It is similar to [TextField.onChanged]
  /// except it has [Amount] as the input field.
  final ValueChanged<Amount?>? onChanged;

  final bool isEnabled;

  final AmountTextInputFormatter amountTextInputFormatter;

  const AmountTextField({
    Key? key,
    required this.amountTextInputFormatter,
    this.onChanged,
    this.labelText,
    this.isEnabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PrimaryTextField(
      inputFormatters: [amountTextInputFormatter],
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      onChanged: (_) {
        l.d("Entered Amount ${amountTextInputFormatter.amountObject}");
        onChanged?.call(amountTextInputFormatter.amountObject);
      },
      labelText: labelText ?? context.localizations.enterAmount,
      suffixIcon: Text(
        amountTextInputFormatter.currency.currencyCode,
        style: context.appTextStyles.labelText2.copyWith(
          color: context.appColors.neutralShade2Color,
        ),
      ),
      suffixIconConstraints: const BoxConstraints(minWidth: 0, minHeight: 0),
      validator: (amount) => Validators.amountValidator(context, amount),
    );
  }
}
