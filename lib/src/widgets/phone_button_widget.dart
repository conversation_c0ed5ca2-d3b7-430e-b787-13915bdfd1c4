import 'package:bcn_agency_banking_flutter/src/exceptions/url_destination_missing.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:flutter/material.dart';
import 'package:leo_dart_runtime/leo_phone_number.dart';
import 'package:sprintf/sprintf.dart';

import '../utils/constants.dart';
import 'info_label.dart';

/// Action button which can be used to call
/// the provided [phoneNumber].
/// This button is usually used in [InfoLabel.actionButton].
/// If the device does not have any dialer, a dialog
/// will be shown informing the same to the user.
class PhoneButtonWidget extends StatelessWidget {
  final LeoPhoneNumber phoneNumber;

  const PhoneButtonWidget({Key? key, required this.phoneNumber})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IconButton(
      constraints: const BoxConstraints(
        minHeight: dimenTwentyFour,
        minWidth: dimenTwentyFour,
      ),
      icon: IconWidget(
        iconColor: context.appColors.primaryLightColor,
        assetName: ABAssets.phoneIcon,
      ),
      onPressed: () => _openDiallerAppWithPhoneNumber(context),
    );
  }

  void _openDiallerAppWithPhoneNumber(BuildContext context) async {
    final phoneNumberURLString = sprintf(phoneNumberURL, [
      phoneNumber.formattedPhoneNumber,
    ]);

    try {
      await launchExternalUrl(phoneNumberURLString);
    } on UrlDestinationMissingException catch (_) {
      if (context.mounted) {
        AgencyAppDialog.showErrorDialog(
          context: context,
          contentText: context.localizations.noDialerAppFoundError,
        );
      }
    }
  }
}
