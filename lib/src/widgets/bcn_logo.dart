import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../resources/ab_assets.dart';
import '../resources/dimensions.dart';

/// App Logo widget.
/// By default, height and width is 64.
class BCNLogo extends StatelessWidget {
  final double height;
  final double width;

  const BCNLogo({
    super.key,
    this.height = dimenSixtyFour,
    this.width = dimenSixtyFour,
  });

  @override
  Widget build(BuildContext context) {
    // Using SVGPicture instead of IconWidget
    // since, BCN Logo is not an icon.
    // Additionally, IconWidget will provide default color
    // which will make BCN Logo SVG lose its own original
    // color.
    return SvgPicture.asset(ABAssets.bcnLogo, height: height, width: width);
  }
}
