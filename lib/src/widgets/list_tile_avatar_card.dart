import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/list_tile_avatar_content.dart';
import 'package:flutter/material.dart';

/// A tappable card that wraps the [ListTileAvatarContent] widget
/// with an [InkWell].
class ListTileAvatarCard extends StatelessWidget {
  /// The callback method for when the card is tapped.
  final VoidCallback onTap;

  /// The title of the widget.
  final String title;

  /// the asset path of the icon image.
  /// An SVG image is rendered using this path.
  final String iconPath;

  /// The subtitle of the widget.
  final String? subtitle;

  /// Defaults to true.
  /// [onTap] callback will be called even though
  /// [isEnabled] is false.
  /// The caller can pass null to [onTap] for the
  /// [onTap] not be called on disable state.
  final bool isEnabled;

  const ListTileAvatarCard({
    super.key,
    required this.onTap,
    required this.title,
    required this.iconPath,
    this.subtitle,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: cardBorderRadius,
      child: InkWell(
        borderRadius: cardBorderRadius,
        onTap: onTap,
        splashFactory:
            isEnabled ? InkSplash.splashFactory : NoSplash.splashFactory,
        child: Ink(
          decoration: BoxDecoration(
            color: context.appColors.backgroundColor,
            border: Border.all(color: context.appColors.neutralShade6Color),
            borderRadius: BorderRadius.circular(dimenEight),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: dimenSixteen,
            vertical: dimenEight,
          ),
          child: ListTileAvatarContent(
            title: title,
            subtitle: subtitle,
            iconPath: iconPath,
            isEnabled: isEnabled,
          ),
        ),
      ),
    );
  }
}
