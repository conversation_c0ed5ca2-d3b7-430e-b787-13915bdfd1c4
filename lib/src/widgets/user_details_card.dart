import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/user_image.dart';
import 'package:flutter/material.dart';

import '../resources/dimensions.dart';
import '../utils/constants.dart';

/// A Widget which will show User's photo with his name.
/// [subTitle] can be the phoneNumber.
class UserDetailsCard extends StatelessWidget {
  final MultiResolutionBitmapImage? image;
  final String name;

  /// [TextStyle] of [name].
  /// Defaults to [appTextStyles.labelText2].
  final TextStyle? nameStyle;

  /// [TextStyle] of [subTitle] and [thirdLineContent].
  /// Defaults to [appTextStyles.labelText3].
  final TextStyle? contentStyle;

  /// Content that will be shown just below [name].
  final String subTitle;

  /// Content that will be shown just below [subtitle].
  final String? thirdLineContent;

  /// Size of the image defaults to [dimenForty].
  final double userImageSize;

  const UserDetailsCard({
    Key? key,
    required this.name,
    required this.subTitle,
    this.thirdLineContent,
    this.image,
    this.userImageSize = dimenForty,
    this.nameStyle,
    this.contentStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: allPaddingSixteen,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          UserImage(
            height: userImageSize,
            width: userImageSize,
            imageUrl: image?.getNetworkPhotoURL(context),
          ),
          horizontalGapSixteen,
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: (nameStyle ?? context.appTextStyles.labelText2)
                      .copyWith(color: context.appColors.neutralShade1Color),
                  maxLines: nameMaxLines,
                  overflow: TextOverflow.ellipsis,
                ),
                verticalGapFour,
                Text(
                  subTitle,
                  style: context.appTextStyles.labelText3.copyWith(
                    color: context.appColors.neutralShade7Color,
                  ),
                  maxLines: shopNameMaxLines,
                  overflow: TextOverflow.ellipsis,
                ),
                if (thirdLineContent != null) ...{
                  verticalGapFour,
                  Text(
                    thirdLineContent!,
                    style: context.appTextStyles.labelText3.copyWith(
                      color: context.appColors.neutralShade7Color,
                    ),
                  ),
                },
              ],
            ),
          ),
        ],
      ),
    );
  }
}
