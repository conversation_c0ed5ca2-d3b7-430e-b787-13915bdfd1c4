import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:leo_flutter_ui/leo_flutter_ui.dart';

import '../helpers/helpers.dart';
import '../resources/dimensions.dart';
import '../utils/constants.dart';

/// A reusable [AuthPinField] that can be used
/// in all the session pin flows.
class AgencyAuthPinField extends StatelessWidget {
  /// Callback which returns the entered pin string
  /// and also provides the ability to clear the pin field.
  final void Function(String, void Function()) onPinComplete;

  /// FocusNode of the [AuthPinField].
  /// If this is null,
  /// [AuthPinField] will create its own [FocusNode].
  final FocusNode? focusNode;
  final bool autofocus;

  const AgencyAuthPinField({
    super.key,
    required this.onPinComplete,
    this.autofocus = true,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return AuthPinField(
      focusNode: focusNode,
      autofocus: autofocus,
      separator: horizontalGapSixteen,
      pinLength: pinLength,
      onPinComplete: onPinComplete,
      filledPinColor: context.appColors.primaryLightColor,
      unfilledPinColor: context.appColors.neutralShade2Color,
      onTapOutside: (_) => dismissKeyboard(),
      inputType: TextInputType.text,
      inputFormatters: [sessionPinInputFormatter],
    );
  }
}
