import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class PullToRefreshWidget extends StatelessWidget {
  final void Function() onRefresh;
  final RefreshController controller;
  final Widget child;

  const PullToRefreshWidget({
    super.key,
    required this.onRefresh,
    required this.controller,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      physics: const BouncingScrollPhysics(),
      onRefresh: onRefresh,
      header: MaterialClassicHeader(
        color: context.appColors.primaryLightColor,
        backgroundColor: context.appColors.surfaceColor,
      ),
      controller: controller,
      child: child,
    );
  }
}
