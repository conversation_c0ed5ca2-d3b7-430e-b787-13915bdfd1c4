import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/sub_screen_layout.dart';
import 'package:flutter/material.dart';

import 'dialogs.dart';

/// This widget is to be used for creating screen layouts for different
/// flows present in the app.
/// Not to be confused with [SubScreenLayout] as this widget is
/// used in transaction or similar flows and not in Sign In flows.
/// if [isCancellable] is false, [processName] will be ignored.
class CommonScreenLayout extends StatelessWidget {
  /// [appBarTitle] is used to set the app bar title.
  final String appBarTitle;

  /// [isCancellable] is used for determining if we want the cancellation
  /// AppBar which shows a dialog asking user for confirmation.
  /// Typically used in flows with [NestedNavigator] involved.
  final bool isCancellable;

  final Widget child;

  final Widget ctaWidget;

  final EdgeInsets? padding;

  /// [disableBackGesture] should be set to true to avoid back gesture in iOS.
  final bool disableBackGesture;

  /// [actionIcon] is used to provide actionIcons to the AppBar.
  final IconButton? actionIcon;
  final VoidCallback? onAppBarLeadingIconTapped;

  /// [processName] when the screen [isCancellable].
  /// if null, processName will be taken as [context.localizations.transaction].
  final String? processName;

  const CommonScreenLayout({
    Key? key,
    required this.appBarTitle,
    required this.child,
    required this.ctaWidget,
    this.isCancellable = false,
    this.disableBackGesture = true,
    this.padding,
    this.actionIcon,
    this.onAppBarLeadingIconTapped,
    this.processName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return disableBackGesture
        // This will pop will have no effect on the Android provided
        // this widget is under nested navigator that already has a `WillPopScope`
        // as the parent but this will be used by iOS
        // to check whether the app can go back from the current route.
        // In conclusion, going back using back gesture on iOS is disabled.
        ? PopScope(
          canPop: false,
          onPopInvokedWithResult: (bool didPop, _) async {
            if (didPop) {
              return;
            }
            final bool? shouldGoBack =
                await AgencyAppDialog.showCancelConfirmation(
                  context,
                  processName ?? context.localizations.transaction,
                );
            if (context.mounted && shouldGoBack == true) {
              context.rootNavigator.pop();
            }
            return;
          },
          child: _buildScaffold(context),
        )
        : _buildScaffold(context);
  }

  Scaffold _buildScaffold(BuildContext context) {
    return Scaffold(
      appBar: PrimaryAppBar(
        title: appBarTitle,
        isCancellable: isCancellable,
        actionIcon: actionIcon,
        onLeadingIconTapped: onAppBarLeadingIconTapped,
        processName: processName ?? context.localizations.transaction,
      ),
      body: SafeArea(
        top: false,
        left: false,
        right: false,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: padding ?? commonScreenPadding,
                  child: child,
                ),
              ),
            ),
            Padding(padding: commonScreenPadding, child: ctaWidget),
          ],
        ),
      ),
    );
  }
}
