import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

import 'icon_widget.dart';

class FeatureCard extends StatelessWidget {
  final String title;
  final VoidCallback onTap;

  const FeatureCard({Key? key, required this.title, required this.onTap})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Ink(
        height: dimenFortyEight,
        child: Padding(
          padding: horizontalPaddingSixteen,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: context.appTextStyles.labelText2.copyWith(
                    color: context.appColors.neutralShade1Color,
                  ),
                ),
              ),
              IconWidget(
                assetName: ABAssets.arrowRightIcon,
                iconColor: context.appColors.neutralShadeDefaultColor,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
