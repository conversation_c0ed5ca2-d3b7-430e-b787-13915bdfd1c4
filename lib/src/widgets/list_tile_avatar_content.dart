import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

/// Displays a Row widget with a vertical padding of [dimenEight].
/// This Row widget consists of a SVG icon image given by the [iconPath],
/// [title] and an optional [subtitle].
/// This widget can be wrapped with an [InkWell] to make it a tappable card.
class ListTileAvatarContent extends StatelessWidget {
  /// The title to be displayed in the Row widget.
  final String title;

  /// The color of the title to be displayed.
  final Color? titleColor;

  /// The asset path of the icon image.
  /// An SVG image is rendered using this path.
  final String iconPath;

  /// The subtitle to be displayed in the Row widget.
  final String? subtitle;

  /// If this card is disabled,
  /// it will have a disabled state of
  /// avatar's opacity changed to [dimenPointFive],
  /// title and subtitle color changed to show disabled state.
  /// Defaults to true.
  final bool isEnabled;

  /// The trailing icon to be displayed on the row.
  /// If null, the default [ABAssets.arrowRightIcon] is displayed.
  final Widget? trailingIcon;

  const ListTileAvatarContent({
    super.key,
    required this.title,
    this.subtitle,
    required this.iconPath,
    this.isEnabled = true,
    this.trailingIcon,
    this.titleColor,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: verticalPaddingEight,
      child: Row(
        children: [
          Opacity(
            opacity: isEnabled ? dimenOne : dimenPointFive,
            child: SvgPicture.asset(
              iconPath,
              height: dimenFiftySix,
              width: dimenFiftySix,
            ),
          ),
          horizontalGapSixteen,
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: context.appTextStyles.labelText1Bold.copyWith(
                    color:
                        titleColor ??
                        (isEnabled
                            ? context.appColors.neutralShade1Color
                            : context.appColors.neutralShade2Color),
                  ),
                  maxLines: cardContentMaxLines,
                  overflow: TextOverflow.ellipsis,
                ),
                verticalGapFour,
                if (subtitle != null)
                  Text(
                    subtitle!,
                    maxLines: cardSubtitleMaxLines,
                    overflow: TextOverflow.ellipsis,
                    style: context.appTextStyles.labelText2.copyWith(
                      color: context.appColors.neutralShade2Color,
                    ),
                  ),
              ],
            ),
          ),
          horizontalGapSixteen,
          trailingIcon ??
              IconWidget(
                assetName: ABAssets.arrowRightIcon,
                iconColor:
                    isEnabled
                        ? context.appColors.neutralShadeDefaultColor
                        : context.appColors.neutralShade2Color,
              ),
        ],
      ),
    );
  }
}
