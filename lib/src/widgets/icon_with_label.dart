import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

class IconWithLabel extends StatelessWidget {
  const IconWithLabel({
    Key? key,
    required this.icon,
    required this.label,
    this.labelTextStyle,
    this.spacing = dimenThirtyTwo,
    this.maxLines = labelTextMaxLines,
    this.onTap,
    this.padding,
    this.height,
    this.width,
    this.backgroundColor,
    this.trailing,
  }) : super(key: key);

  /// Leading Icon to be displayed in [IconWithLabel].
  final Widget icon;

  final String label;

  /// Defaults to [labelText2].
  final TextStyle? labelTextStyle;

  /// Space between [icon] and [label].
  final double spacing;

  /// Max Lines for [label].
  /// Defaults to [labelTextMaxLines].
  final int maxLines;

  final VoidCallback? onTap;

  final EdgeInsets? padding;

  /// Height of [IconWithLabel].
  /// Defaults to wrap content.
  final double? height;

  /// Width of [IconWithLabel].
  /// Defaults to wrap content.
  final double? width;

  /// Defaults to [scaffoldBackgroundColor]
  /// of the [Theme].
  final Color? backgroundColor;

  final Widget? trailing;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Ink(
        color: backgroundColor ?? context.theme.scaffoldBackgroundColor,
        height: height,
        width: width,
        padding: padding ?? const EdgeInsets.all(dimenThirtyTwo),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            icon,
            SizedBox(width: spacing),
            Expanded(
              child: Text(
                label,
                style:
                    labelTextStyle ??
                    context.appTextStyles.labelText2.copyWith(
                      color: context.appColors.neutralShade1Color,
                    ),
                maxLines: maxLines,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (trailing != null) trailing!,
          ],
        ),
      ),
    );
  }
}
