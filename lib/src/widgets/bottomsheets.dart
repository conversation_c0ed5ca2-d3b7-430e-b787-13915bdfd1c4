import 'package:agency_banking_rpcs/agency/ab_comment_type.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/comment_bottomsheet_widget.dart';
import 'package:flutter/material.dart';

import '../utils/constants.dart';

class BottomSheets {
  BottomSheets._();

  /// Comment bottom sheet that will show a text field with
  /// [commentReason] and the submit [ctaButtonText].
  /// This bottom sheet will return [ABComment]
  /// if the user enters a valid comment and
  /// taps on the submit CTA.
  /// It will return null if the user cancels the bottom sheet.
  static Future<ABComment?> showCommentBottomSheet({
    required BuildContext context,
    required String commentReason,
    required String ctaButtonText,
  }) async {
    return await showModalBottomSheet<ABComment?>(
      context: context,
      isDismissible: false,
      shape: const RoundedRectangleBorder(
        borderRadius: bottomSheetBorderRadius,
      ),
      isScrollControlled: true,
      builder: (_) {
        return CommentBottomSheet(
          commentReason: commentReason,
          ctaButtonText: ctaButtonText,
        );
      },
    );
  }
}
