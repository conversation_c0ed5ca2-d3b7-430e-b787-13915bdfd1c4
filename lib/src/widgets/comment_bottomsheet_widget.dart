import 'package:agency_banking_rpcs/agency/ab_comment_type.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/reg_exp_helper.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_outlined_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/validators.dart';
import 'bottomsheets.dart';
import 'primary_button.dart';
import 'primary_text_field.dart';

/// Widget which should be used in [BottomSheets.showCommentBottomSheet].
class CommentBottomSheet extends StatefulWidget {
  final String commentReason;
  final String ctaButtonText;

  const CommentBottomSheet({
    Key? key,
    required this.commentReason,
    required this.ctaButtonText,
  }) : super(key: key);

  @override
  State<CommentBottomSheet> createState() => _CommentBottomSheetState();
}

class _CommentBottomSheetState extends State<CommentBottomSheet> {
  final _commentController = TextEditingController();
  final _commentFormKey = GlobalKey<FormState>();
  AutovalidateMode _shouldValidateComment = AutovalidateMode.disabled;

  @override
  Widget build(BuildContext context) {
    return _buildCommentBody(context);
  }

  Container _buildCommentBody(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: bottomSheetBorderRadius,
        color: context.appColors.surfaceColor,
      ),
      child: Padding(
        // Added a padding so that text field comes up
        // when the text field is focused.
        padding: context.mq.viewInsets,
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: commonScreenPadding,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    widget.commentReason,
                    style: context.appTextStyles.smallText1.copyWith(
                      color: context.appColors.neutralShadeDefaultColor,
                    ),
                  ),
                  verticalGapSixteen,
                  Form(
                    key: _commentFormKey,
                    autovalidateMode: _shouldValidateComment,
                    child: PrimaryTextField(
                      labelText: context.localizations.enterComment,
                      autofocus: true,
                      showCounter: true,
                      minLines: minLines,
                      maxLines: maxLines,
                      controller: _commentController,
                      validator:
                          (comment) => Validators.emptyValidator(
                            context,
                            context.localizations.comment,
                            comment,
                          ),
                      inputFormatters: [
                        FilteringTextInputFormatter.deny(
                          RegExpHelper.emojiPattern,
                        ),
                      ],
                      maxLength: maxCommentLength,
                      keyboardType: TextInputType.multiline,
                    ),
                  ),
                  verticalGapSixteen,
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: PrimaryOutlinedButton(
            onPressed: context.rootNavigator.pop,
            labelText: context.localizations.cancel,
          ),
        ),
        horizontalGapSixteen,
        Flexible(
          child: PrimaryButton(
            onPressed: () {
              final isFormValidated =
                  _commentFormKey.currentState?.validate() ?? false;
              if (!isFormValidated) {
                setState(() {
                  _shouldValidateComment = AutovalidateMode.onUserInteraction;
                });
                return;
              }
              context.rootNavigator.pop(
                ABComment(text: _commentController.text.trim()),
              );
            },
            labelText: widget.ctaButtonText,
          ),
        ),
      ],
    );
  }
}
