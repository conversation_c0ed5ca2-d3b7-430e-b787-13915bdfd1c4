import 'package:flutter/cupertino.dart';

class NestedNavigatorObserver extends NavigatorObserver {
  final List<Route> _history = [];

  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    // Update the history with the Navigation history.
    _history.add(route);
  }

  /// Get the top most route in the [Navigator].
  Route? get top {
    try {
      return _history.last;
    } catch (_) {
      return null;
    }
  }
}
