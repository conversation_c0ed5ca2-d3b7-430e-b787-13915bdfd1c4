import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_app_bar.dart';
import 'package:flutter/material.dart';

/// The widget used for the layout of the screens on sign in flows.
class SubScreenLayout extends StatelessWidget {
  final Widget child;
  final Widget primaryButton;

  /// Title displayed on the [PrimaryAppBar].
  final String? title;

  final EdgeInsets? padding;

  const SubScreenLayout({
    super.key,
    required this.child,
    required this.primaryButton,
    this.title,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }
        _askForConfirmationAndNavigate(context);
      },
      child: Scaffold(
        appBar: PrimaryAppBar(
          isCancellable: true,
          title: title,
          processName: context.localizations.signIn,
          onLeadingIconTapped: () => _askForConfirmationAndNavigate(context),
        ),
        body: SafeArea(
          top: false,
          left: false,
          right: false,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: padding ?? commonScreenPadding,
                    child: child,
                  ),
                ),
              ),
              Padding(padding: commonScreenPadding, child: primaryButton),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> _askForConfirmationAndNavigate(BuildContext context) async {
    final bool? shouldGoBack = await AgencyAppDialog.showCancelConfirmation(
      context,
      context.localizations.signIn,
    );
    if (context.mounted && shouldGoBack == true) {
      navigateToLandingScreen(context);
    }
    return false;
  }
}
