import 'package:bcn_agency_banking_flutter/src/helpers/reg_exp_helper.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/validators.dart';

class PasswordField extends StatefulWidget {
  const PasswordField({
    Key? key,
    this.labelText,
    required this.onPasswordChanged,
    this.enableVisibility = false,
  }) : super(key: key);

  final String? labelText;
  final void Function(String) onPasswordChanged;
  final bool enableVisibility;

  @override
  State<PasswordField> createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordField> {
  bool _isPasswordVisible = false;

  CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center;

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // Center align the phone icon with the text field
          // without error state height.
          SizedBox(
            height: dimenFortyEight,
            child: Center(
              child: IconWidget(
                assetName: ABAssets.lockIcon,
                iconColor: context.appColors.neutralShadeDefaultColor,
              ),
            ),
          ),
          horizontalGapSixteen,
          Expanded(
            child: PrimaryTextField(
              inputFormatters: [
                FilteringTextInputFormatter.deny(RegExpHelper.emojiPattern),
              ],
              keyboardType: TextInputType.visiblePassword,
              labelText: widget.labelText ?? context.localizations.password,
              obscureText: !_isPasswordVisible,
              maxLength: maxPasswordLength,
              maxLines: passwordMaxLines,
              suffixIcon:
                  widget.enableVisibility ? _buildVisibilityButton() : null,
              onChanged: widget.onPasswordChanged,
              suffixIconConstraints: const BoxConstraints(
                minHeight: dimenZero,
                minWidth: dimenZero,
              ),
              validator:
                  (password) => Validators.emptyValidator(
                    context,
                    context.localizations.password,
                    password,
                  ),
              contentPadding: textFieldContentPadding.copyWith(
                right: dimenZero,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVisibilityButton() {
    return InkWell(
      borderRadius: BorderRadius.circular(dimenFortyEight),
      onTap: () {
        setState(() => _isPasswordVisible = !_isPasswordVisible);
      },
      child: Ink(
        height: dimenThirtyTwo,
        width: dimenThirtyTwo,
        child: IconWidget(
          assetName:
              _isPasswordVisible
                  ? ABAssets.visibilityOffIcon
                  : ABAssets.visibilityIcon,
          iconColor: context.appColors.neutralShadeDefaultColor,
        ),
      ),
    );
  }
}
