import 'package:flutter/material.dart';

import '../resources/dimensions.dart';

class Spinner extends StatelessWidget {
  final double? height;
  final double? width;
  final double? strokeWidth;
  final Color? color;

  const Spinner({
    super.key,
    this.height,
    this.width,
    this.strokeWidth,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: strokeWidth ?? dimenFour,
          color: color,
        ),
      ),
    );
  }
}
