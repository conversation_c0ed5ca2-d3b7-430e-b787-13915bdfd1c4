import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:flutter/material.dart';

import 'transaction_details_widget.dart';

class TransactionDialog {
  TransactionDialog._();

  /// Shows TransactionDetailDialog.
  /// Resolves with true, when user clicks on "Proceed" button.
  /// Resolves with false, when user clicks on "Decline" button.
  /// Shows transaction details dialog in a following format:
  /// {
  /// Transaction Details:
  /// [transactionAmountLabel]  -> formatted [transactionAmount]
  /// Transaction Fee           -> formatted [transactionFee]
  /// [additionalDetailLabel]   -> [additionalDetailValue]
  /// (* The above one is optional layer)
  /// [receivingAmountLabel]    -> formatted [receivingAmount]
  /// }
  ///
  ///
  /// [additionalDetailLabel] and [additionalDetailValue] both can either be null
  /// or non-null.
  static Future<bool?> show({
    required BuildContext context,
    required String transactionAmountLabel,
    required Amount transactionAmount,
    required String receivingAmountLabel,
    required Amount transactionFee,
    required Amount receivingAmount,
    String? additionalDetailLabel,
    String? additionalDetailValue,
  }) async {
    assert(
      !((additionalDetailLabel != null) ^ (additionalDetailValue != null)),
      "Please either provide both additionalDetailLabel and additionalDetailValue or provide none.",
    );
    return await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return TransactionDetailsWidget(
          transactionAmountLabel: transactionAmountLabel,
          transactionAmount: transactionAmount,
          transactionFee: transactionFee,
          receivingAmount: receivingAmount,
          receivingAmountLabel: receivingAmountLabel,
          additionalDetailLabel: additionalDetailLabel,
          additionalDetailValue: additionalDetailValue,
          isShownInDialog: true,
        );
      },
    );
  }
}
