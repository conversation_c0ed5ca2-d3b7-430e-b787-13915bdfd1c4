import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/info_label_list.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/title_widget.dart';
import 'package:flutter/material.dart';

/// This widget is to be used in different flows where information is
/// to be displayed in a listed manner inside a section.
class SectionWidget extends StatelessWidget {
  const SectionWidget({
    Key? key,
    required this.title,
    required this.children,
    this.padding,
  }) : super(key: key);

  /// A string to be displayed as a header of the section giving
  /// an idea to the user as to what the section is about.
  final String title;

  /// The data to be displayed to user in the section in
  /// listed manner.
  final List<InfoLabel> children;

  /// Padding across [InfoLabelList].
  /// Defaults to vertical padding of [dimenEight]
  /// and horizontal padding of [dimenSixteen].
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TitleWidget(title: title),
        Padding(
          padding:
              padding ??
              const EdgeInsets.symmetric(
                vertical: dimenEight,
                horizontal: dimenSixteen,
              ),
          child: InfoLabelList(children: children),
        ),
      ],
    );
  }
}
