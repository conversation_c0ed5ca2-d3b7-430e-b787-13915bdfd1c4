import 'package:flutter/material.dart';

class PrimaryTextButton extends StatelessWidget {
  final VoidCallback? onTap;
  final String? text;
  final Widget? child;
  final Color? color;

  /// Fixed size of the button.
  /// If null, the button size will
  /// adjust to the content.
  final Size? fixedSize;

  const PrimaryTextButton({
    Key? key,
    this.text,
    this.onTap,
    this.child,
    this.color,
    this.fixedSize,
  }) : assert(
         text != null || child != null,
         'Please provide either text or child.',
       ),
       super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onTap,
      style: TextButton.styleFrom(foregroundColor: color, fixedSize: fixedSize),
      child: text != null ? Text(text!.toUpperCase()) : child!,
    );
  }
}
