import 'package:agency_banking_rpcs/agency/blocked_money_transfer_request_type.dart';
import 'package:agency_banking_rpcs/agency/money_transfer_active_request_info_type.dart';
import 'package:agency_banking_rpcs/agency/money_transfer_refund_request_type.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../resources/ab_assets.dart';
import '../resources/dimensions.dart';
import '../utils/formatters/date_formatter.dart';
import '../widgets/icon_widget.dart';

part 'request_card.freezed.dart';

/// This is currently used in Money Transfer: Active Request and Customer Refunds,
/// Agent Manager: Unblock Refunds.
/// [requestCardModel] is a freezed model and
/// can take object for each request for their respective feature.
class RequestCard extends StatelessWidget {
  final RequestCardModel requestCardModel;
  final VoidCallback onTap;

  const RequestCard({
    Key? key,
    required this.requestCardModel,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: cardBorderRadius,
      child: Ink(
        decoration: BoxDecoration(
          borderRadius: cardBorderRadius,
          color: context.theme.scaffoldBackgroundColor,
          border: Border.all(color: context.appColors.neutralShade6Color),
        ),
        padding: allPaddingSixteen,
        child: Column(
          children: [
            _buildHeadingTile(context),
            verticalGapTwelve,
            _buildDetail(
              context: context,
              title: context.localizations.senderDetails,
              leadingIcon: IconWidget(
                assetName: ABAssets.arrowUpRightIcon,
                iconColor: context.appColors.neutralShadeDefaultColor,
              ),
              subtitle: switch (requestCardModel) {
                CustomerRefundRequest(
                  moneyTransferRefundRequest: final customerRefundRequest,
                ) =>
                  (() {
                    final formattedPhoneNumber =
                        customerRefundRequest
                            .senderPhoneNumber
                            .formattedPhoneNumber;
                    final senderName = customerRefundRequest.senderName.text;
                    return "$senderName, $formattedPhoneNumber";
                  })(),
                ActiveRequest(activeRequestInfo: final activeRequest) =>
                  (() {
                    final formattedPhoneNumber =
                        activeRequest.senderPhoneNumber.formattedPhoneNumber;
                    final senderName = activeRequest.senderName.text;
                    return "$senderName, $formattedPhoneNumber";
                  })(),
                UnBlockRefunds(
                  blockedMoneyTransferRequest: final unblockRefunds,
                ) =>
                  (() {
                    final formattedPhoneNumber =
                        unblockRefunds
                            .requestDetails
                            .senderDetail
                            .phoneNumber
                            .formattedPhoneNumber;
                    final senderName =
                        unblockRefunds
                            .requestDetails
                            .senderDetail
                            .userName
                            .text;
                    return "$senderName, $formattedPhoneNumber";
                  })(),
              },
            ),
            verticalGapEight,
            _buildDetail(
              context: context,
              title:
                  requestCardModel is UnBlockRefunds
                      ? context.localizations.agentDetails
                      : context.localizations.recipientDetails,
              leadingIcon: IconWidget(
                assetName:
                    requestCardModel is UnBlockRefunds
                        ? ABAssets.userLocationIcon
                        : ABAssets.arrowDownLeftIcon,
                iconColor: context.appColors.neutralShadeDefaultColor,
              ),
              subtitle: switch (requestCardModel) {
                CustomerRefundRequest(
                  moneyTransferRefundRequest: final customerRefundRequest,
                ) =>
                  (() {
                    final formattedPhoneNumber =
                        customerRefundRequest
                            .recipientPhoneNumber
                            .formattedPhoneNumber;
                    final recipientNationalId =
                        customerRefundRequest.recipientNationalId.id;
                    return "$recipientNationalId, $formattedPhoneNumber";
                  })(),
                ActiveRequest(activeRequestInfo: final activeRequest) =>
                  (() {
                    final formattedPhoneNumber =
                        activeRequest.recipientPhoneNumber.formattedPhoneNumber;
                    final recipientNationalId =
                        activeRequest.recipientNationalId.id;
                    return "$recipientNationalId, $formattedPhoneNumber";
                  })(),
                UnBlockRefunds(
                  blockedMoneyTransferRequest: final unblockRefunds,
                ) =>
                  (() {
                    final formattedPhoneNumber =
                        unblockRefunds
                            .requestDetails
                            .agentDetail
                            .phoneNumber
                            .formattedPhoneNumber;
                    final agentName =
                        unblockRefunds.requestDetails.agentDetail.userName.text;
                    return "$agentName, $formattedPhoneNumber";
                  })(),
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeadingTile(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(child: _buildMainTitle(context)),
            horizontalGapEight,
            IconWidget(
              assetName: ABAssets.arrowRightIcon,
              iconColor: context.appColors.neutralShadeDefaultColor,
            ),
          ],
        ),
        Text(
          switch (requestCardModel) {
            CustomerRefundRequest(
              moneyTransferRefundRequest: final customerRefundRequest,
            ) =>
              (() {
                return context.localizations.createdOnTimestamp(
                  _formatDate(
                    context,
                    customerRefundRequest.requestCreatedAt.toLocal(),
                  ),
                );
              })(),
            ActiveRequest(activeRequestInfo: final activeRequest) =>
              (() {
                return context.localizations.createdOnTimestamp(
                  _formatDate(
                    context,
                    activeRequest.requestCreatedAt.toLocal(),
                  ),
                );
              })(),
            UnBlockRefunds(blockedMoneyTransferRequest: final unblockRefunds) =>
              (() {
                return context.localizations.blockedOn(
                  _formatDate(context, unblockRefunds.blockedAt.toLocal()),
                );
              })(),
          },
          style: context.appTextStyles.smallText2.copyWith(
            color: context.appColors.neutralShade7Color,
          ),
        ),
      ],
    );
  }

  String _formatDate(BuildContext context, DateTime dateTime) =>
  //TODO: https://bcnsurya.atlassian.net/browse/AGB-694
  DateFormatter.transactionDateTimeFormat(context).format(dateTime);

  Widget _buildMainTitle(BuildContext context) {
    return Text(
      switch (requestCardModel) {
        CustomerRefundRequest(
          moneyTransferRefundRequest: final customerRefundRequest,
        ) =>
          (() {
            return customerRefundRequest.amount.localisedFormattedAmount;
          })(),
        ActiveRequest(activeRequestInfo: final activeRequest) =>
          (() {
            return activeRequest.amount.localisedFormattedAmount;
          })(),
        UnBlockRefunds(blockedMoneyTransferRequest: final unblockRefunds) =>
          (() {
            return context.localizations.requestId(
              unblockRefunds.requestDetails.requestId.id,
            );
          })(),
      },
      style: context.appTextStyles.titleBold.copyWith(
        color: context.appColors.neutralShade1Color,
      ),
    );
  }

  Widget _buildDetail({
    required BuildContext context,
    required Widget leadingIcon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        leadingIcon,
        horizontalGapSixteen,
        Expanded(
          child: Padding(
            padding: verticalPaddingEight,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: context.appTextStyles.smallText2.copyWith(
                    color: context.appColors.neutralShade7Color,
                  ),
                  textAlign: TextAlign.start,
                ),
                verticalGapFour,
                Text(
                  subtitle,
                  style: context.appTextStyles.labelText2Bold.copyWith(
                    color: context.appColors.neutralShade1Color,
                  ),
                  textAlign: TextAlign.start,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

@freezed
sealed class RequestCardModel with _$RequestCardModel {
  const factory RequestCardModel.customerRefundRequest(
    MoneyTransferRefundRequest moneyTransferRefundRequest,
  ) = CustomerRefundRequest;

  const factory RequestCardModel.activeRequest(
    MoneyTransferActiveRequestInfo activeRequestInfo,
  ) = ActiveRequest;

  const factory RequestCardModel.unblockRefunds(
    BlockedMoneyTransferRequest blockedMoneyTransferRequest,
  ) = UnBlockRefunds;
}
