import 'dart:async';

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/formatted_duration_left_helper.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/formatters/date_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/validators.dart';

/// A widget with both OTP text field and resend button.
/// This widget is responsible for UI based changes in the [OTPWidget].
class OTPWidget extends StatefulWidget {
  final bool isEnabled;

  /// [OTPValidityDetails] object from RPC.
  final OTPValidityDetails otpValidityDetails;

  /// [onChanged] parameter when user edits the OTP text field.
  final void Function(String)? onChanged;

  /// [onResend] parameter should pass on the [Future] which will resolve
  /// with [OTPResendDetails] in case of successful RPC response.
  /// Resolve this [Future] with null, if there is any error in RPC call.
  final Future<OTPResendDetails?> Function() onResend;

  /// Defaults to [otpLength].
  final int otpLength;

  /// [labelText] defaults to 'Enter OTP'.
  final String? labelText;

  /// Name of the field without verb.
  /// For example, "OTP", "Sender's OTP" and "Recipient's OTP", etc.
  /// Defaults to "OTP".
  final String? fieldName;

  const OTPWidget({
    Key? key,
    this.isEnabled = true,
    this.otpLength = validOTPLength,
    this.onChanged,
    this.labelText,
    required this.otpValidityDetails,
    required this.onResend,
    this.fieldName,
  }) : super(key: key);

  @override
  State<OTPWidget> createState() => _OTPWidgetState();
}

class _OTPWidgetState extends State<OTPWidget> {
  late DateTime _otpExpiration = widget.otpValidityDetails.expiresAt;

  String get _formattedOTPExpiration =>
      DateFormatter.otpValidityFormat(context).format(_otpExpiration.toLocal());

  int? _numberOfResendLeft;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PrimaryTextField(
          labelText: widget.labelText ?? context.localizations.enterOTP,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(widget.otpLength),
          ],
          keyboardType: TextInputType.number,
          onChanged: (value) {
            widget.onChanged?.call(value);
          },
          validator:
              (otp) => Validators.otpValidator(
                context,
                widget.fieldName ?? context.localizations.otp,
                otp,
                widget.otpLength,
              ),
        ),
        verticalGapTwelve,
        _ResendButton(
          nextResendAt: widget.otpValidityDetails.nextResendAt.toLocal(),
          onResend: () async {
            final otpResendDetails = await widget.onResend();
            if (otpResendDetails != null) {
              setState(() {
                _otpExpiration = otpResendDetails.validityDetails.expiresAt;
                _numberOfResendLeft = otpResendDetails.numberOfResendsLeft;
              });
            }
            return otpResendDetails;
          },
        ),
        verticalGapTwelve,
        Text(
          context.localizations.otpValidTill(_formattedOTPExpiration),
          style: context.appTextStyles.smallText2.copyWith(
            color: context.appColors.neutralShadeDefaultColor,
          ),
        ),
        if (_numberOfResendLeft != null)
          _numberOfResendLeft! > 1
              ? Text(
                context.localizations.numberOfOTPResendAttemptsLeft(
                  _numberOfResendLeft!,
                ),
                style: context.appTextStyles.smallText2.copyWith(
                  color: context.appColors.neutralShadeDefaultColor,
                ),
              )
              : Text(
                context.localizations.numberOfOTPResendAttemptLeft(
                  _numberOfResendLeft!,
                ),
                style: context.appTextStyles.smallText2.copyWith(
                  color: context.appColors.neutralShadeDefaultColor,
                ),
              ),
      ],
    );
  }
}

class _ResendButton extends StatefulWidget {
  final DateTime nextResendAt;
  final Future<OTPResendDetails?> Function() onResend;

  const _ResendButton({
    Key? key,
    required this.nextResendAt,
    required this.onResend,
  }) : super(key: key);

  @override
  State<_ResendButton> createState() => _ResendButtonState();
}

class _ResendButtonState extends State<_ResendButton> {
  bool _isLoading = false;
  late DateTime _nextResendAt = widget.nextResendAt;
  int? _retriesLeft;

  String get _formattedDurationLeftForNextResend =>
      FormattedDurationLeftHelper.getFormattedDurationLeftString(_nextResendAt);

  late final Timer timer;

  bool get _shouldRestrictResend => _nextResendAt.isAfter(DateTime.now());

  bool get _isRetryAttemptsExhausted =>
      _retriesLeft != null && _retriesLeft! < 1;

  bool get _isButtonEnabled =>
      !_isLoading && !_shouldRestrictResend && !_isRetryAttemptsExhausted;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      timer = Timer.periodic(1.seconds, (_) {
        final now = DateTime.now();
        // Don't setState when _nextResendAt is before now. This will save unwanted build.
        if (_nextResendAt.isAfter(now) ||
            _nextResendAt.difference(now).inSeconds < 2) {
          setState(() {});
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return PrimaryTextButton(
      onTap: _isButtonEnabled ? _resendOTP : null,
      text: _isButtonEnabled ? context.localizations.resendOTP : null,
      child: _isButtonEnabled ? null : _buildResendButtonChild(),
    );
  }

  Widget? _buildResendButtonChild() {
    if (_isLoading) {
      // `Spinner` widget is not used here as this is currently the only case
      // where the alignment for the `CircularProgressIndicator` is not center.
      // In case more instances like this arise in the future, a common widget
      // must be used.
      return const SizedBox(
        height: dimenTwenty,
        width: dimenTwenty,
        child: CircularProgressIndicator(strokeWidth: dimenOnePointSixSeven),
      );
    } else if (_isRetryAttemptsExhausted) {
      return Text(
        context.localizations.resendOTP.toUpperCase(),
        style: context.appTextStyles.buttonText1.copyWith(
          color: context.appColors.neutralShade2Color,
        ),
      );
    } else if (_shouldRestrictResend) {
      return Text(
        "${context.localizations.resendOTP} ($_formattedDurationLeftForNextResend)"
            .toUpperCase(),
        style: context.appTextStyles.buttonText1.copyWith(
          color: context.appColors.neutralShade2Color,
        ),
      );
    }
    return null;
  }

  Future<void> _resendOTP() async {
    setState(() => _isLoading = true);
    final otpResendDetails = await widget.onResend();
    _isLoading = false;
    if (otpResendDetails != null) {
      _retriesLeft = otpResendDetails.numberOfResendsLeft;
      _nextResendAt = otpResendDetails.validityDetails.nextResendAt;
    }
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    timer.cancel();
  }
}
