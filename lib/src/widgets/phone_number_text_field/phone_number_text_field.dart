import 'package:agency_banking_rpcs/types/country_type.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_number_text_field/phone_number_entry_widget.dart';
import 'package:flutter/material.dart';

import '../../core/logger.dart';
import '../../utils/supported_countries.dart';

class PhoneNumberTextField extends StatefulWidget {
  final String? labelText;

  /// This callback will be called only when the entered phone number is valid.
  final ValueChanged<String?>? onPhoneNumberValidated;
  final TextEditingController? controller;

  const PhoneNumberTextField({
    Key? key,
    this.labelText,
    this.onPhoneNumberValidated,
    this.controller,
  }) : super(key: key);

  @override
  State<PhoneNumberTextField> createState() => _PhoneNumberTextFieldState();
}

class _PhoneNumberTextFieldState extends State<PhoneNumberTextField> {
  Future<List<Country>?>? _cachedCountriesFuture;

  @override
  void initState() {
    super.initState();
    _cachedCountriesFuture = CachedSupportedCountries.getSupportedCountries();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Country>?>(
      future: _cachedCountriesFuture,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Placeholder();
        }
        if (snapshot.data?.isEmpty ?? true) {
          throw DeveloperError("There are no cached countries");
        }
        final List<Country> countries = snapshot.data!;
        return PhoneNumberEntryWidget(
          labelText: widget.labelText,
          supportedCountries: countries,
          onValueChanged: (String? newNumber) {
            l.d("Entered Phone Number $newNumber");
            widget.onPhoneNumberValidated?.call(newNumber);
          },
        );
      },
    );
  }
}
