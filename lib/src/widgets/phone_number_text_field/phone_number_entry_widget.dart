import 'package:agency_banking_rpcs/types/country_type.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_number_text_field/phone_number_formatter.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

import '../../resources/dimensions.dart';
import '../../utils/validators.dart';
import 'country_iso_dropdown.dart';

class PhoneNumberEntryWidget extends StatefulWidget {
  final String? labelText;
  final List<Country> supportedCountries;
  final ValueChanged<String?> onValueChanged;

  const PhoneNumberEntryWidget({
    Key? key,
    this.labelText,
    required this.supportedCountries,
    required this.onValueChanged,
  }) : super(key: key);

  @override
  State<PhoneNumberEntryWidget> createState() => _PhoneNumberEntryWidgetState();
}

class _PhoneNumberEntryWidgetState extends State<PhoneNumberEntryWidget> {
  late Country _currentCountry = widget.supportedCountries.first;

  String get _currentCountryCode => _currentCountry.code.code;

  IsoCode get _currentCountryIsoCode =>
      _getIsoCodeFromCountryCode(_currentCountryCode);

  final TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CountryISOCodeDropdown(
          supportedCountries: widget.supportedCountries,
          onCountryChanged: (Country newCountry) {
            if (newCountry != _currentCountry) {
              setState(() {
                _currentCountry = newCountry;
              });

              // Format for the newly selected country code.
              final String getTrimmedTextFieldContent =
                  _controller.text.removeSpaces();
              final String getFormattedNumber = PhoneNumberFormatter.formatNsn(
                getTrimmedTextFieldContent,
                _currentCountryIsoCode,
              );
              _controller.text = getFormattedNumber;
              _evaluatePhoneNumber(_controller.text);
            }
          },
        ),
        horizontalGapSixteen,
        Expanded(
          child: PrimaryTextField(
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              PhoneInputFormatter(_currentCountryIsoCode),
            ],
            controller: _controller,
            keyboardType: TextInputType.number,
            onChanged: _evaluatePhoneNumber,
            labelText:
                widget.labelText ?? context.localizations.enterMobileNumber,
            validator:
                (number) => Validators.mobileNumberValidator(
                  context,
                  _currentCountryIsoCode,
                  number,
                  label: widget.labelText,
                ),
          ),
        ),
      ],
    );
  }

  void _evaluatePhoneNumber(String newNumber) {
    final String trimmedNumber = newNumber.removeSpaces();
    final PhoneNumber phoneNumber = PhoneNumber(
      isoCode: _currentCountryIsoCode,
      nsn: trimmedNumber,
    );
    if (phoneNumber.isValid()) {
      final String phoneNumberWithIsoCode =
          _currentCountry.phoneCode + trimmedNumber;
      widget.onValueChanged(phoneNumberWithIsoCode);
    } else {
      widget.onValueChanged(null);
    }
  }

  IsoCode _getIsoCodeFromCountryCode(String countryCode) {
    return IsoCode.values.firstWhere((country) {
      return country.name.toLowerCase() ==
          _currentCountry.code.code.toLowerCase();
    });
  }
}
