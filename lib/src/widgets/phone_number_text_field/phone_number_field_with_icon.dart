import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/phone_number_text_field/phone_number_text_field.dart';
import 'package:flutter/material.dart';

import '../icon_widget.dart';

class PhoneNumberFieldWithIcon extends StatefulWidget {
  final void Function(String?) onPhoneNumberValidated;

  const PhoneNumberFieldWithIcon({
    super.key,
    required this.onPhoneNumberValidated,
  });

  @override
  State<PhoneNumberFieldWithIcon> createState() =>
      _PhoneNumberFieldWithIconState();
}

class _PhoneNumberFieldWithIconState extends State<PhoneNumberFieldWithIcon> {
  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Center align the phone icon with the text field
          // without error state height.
          SizedBox(
            height: dimenFortyEight,
            child: Center(
              child: IconWidget(
                assetName: ABAssets.phoneIcon,
                iconColor: context.appColors.neutralShadeDefaultColor,
              ),
            ),
          ),
          horizontalGapSixteen,
          Expanded(
            child: PhoneNumberTextField(
              labelText: context.localizations.mobileNumber,
              onPhoneNumberValidated: widget.onPhoneNumberValidated,
            ),
          ),
        ],
      ),
    );
  }
}
