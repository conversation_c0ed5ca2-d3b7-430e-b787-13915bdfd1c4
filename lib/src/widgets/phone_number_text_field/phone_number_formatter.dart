import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/services.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

class PhoneInputFormatter extends TextInputFormatter {
  final IsoCode countryIsoCode;

  PhoneInputFormatter(this.countryIsoCode);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.length > maxPhoneNumberFieldLength) return oldValue;

    final formattedValue = PhoneNumberFormatter.formatNsn(
      newValue.text,
      countryIsoCode,
    );

    return TextEditingValue(
      text: formattedValue,
      selection: TextSelection.collapsed(
        offset: _getSelectionOffset(newValue, formattedValue),
        affinity: newValue.selection.affinity,
      ),
      composing: TextRange.empty,
    );
  }

  int _getSelectionOffset(TextEditingValue newValue, String formattedValue) {
    int leadingCount =
        newValue.text
            .substring(0, newValue.selection.start)
            .removeSpaces()
            .length;
    int position = 0;
    while (leadingCount > 0 && position < formattedValue.length) {
      if (formattedValue[position] != " ") leadingCount -= 1;
      position += 1;
    }
    return position;
  }
}
