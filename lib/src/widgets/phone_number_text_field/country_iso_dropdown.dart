import 'package:agency_banking_rpcs/types/country_type.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:country_flags/country_flags.dart';
import 'package:flutter/material.dart';

import '../../core/locale_service/bcn_locale.dart';
import '../icon_widget.dart';

class CountryISOCodeDropdown extends StatefulWidget {
  final List<Country> supportedCountries;
  final ValueChanged<Country> onCountryChanged;

  const CountryISOCodeDropdown({
    Key? key,
    required this.supportedCountries,
    required this.onCountryChanged,
  }) : super(key: key);

  @override
  State<CountryISOCodeDropdown> createState() => _CountryISOCodeDropdownState();
}

class _CountryISOCodeDropdownState extends State<CountryISOCodeDropdown> {
  late Country _currentCountry = widget.supportedCountries.first;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        if (widget.supportedCountries.length > 1) {
          final Country? selectedCountry = await _selectCountry(
            context,
            widget.supportedCountries,
          );
          // Don't rebuild the view if the selected country is
          // the same as the _currentCountry.
          if (selectedCountry != null && selectedCountry != _currentCountry) {
            setState(() {
              _currentCountry = selectedCountry;
            });
            widget.onCountryChanged(_currentCountry);
          }
        }
      },
      child: Ink(
        height: dimenFortyEight,
        width: dimenNinetyTwo,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CountryFlag.fromCountryCode(
                    _currentCountry.code.code,
                    height: _getFlagHeight(dimenTwenty),
                    width: dimenTwenty,
                    shape: RoundedRectangle(dimenTwo),
                  ),
                  horizontalGapFour,
                  Flexible(
                    child: Text(
                      _currentCountry.phoneCode,
                      style: context.appTextStyles.labelText1.copyWith(
                        color: context.appColors.neutralShade1Color,
                        // This height will center align the text with
                        // the flag.
                        height: dimenOnePointThree,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  if (widget.supportedCountries.length > 1) ...{
                    horizontalGapFour,
                    IconWidget(
                      assetName: ABAssets.arrowDropDownIcon,
                      iconColor: context.appColors.neutralShadeDefaultColor,
                      height: dimenTwenty,
                      width: dimenTwenty,
                    ),
                  },
                ],
              ),
            ),
            Container(
              alignment: Alignment.bottomCenter,
              width: dimenNinetyTwo,
              decoration: BoxDecoration(
                border: Border.fromBorderSide(
                  BorderSide(
                    // Start the border stroke outside of the border stroke
                    // so the border alignment will match
                    // with that of Text field's border.
                    strokeAlign: dimenOne,
                    width: dimenPointFive,
                    color: context.appColors.neutralShade2Color,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Future<Country?> _selectCountry(
  BuildContext context,
  List<Country> countries,
) async {
  return await showDialog<Country>(
    context: context,
    barrierDismissible: false,
    builder: (dialogContext) {
      return Dialog(
        child: PopScope(
          canPop: false,
          child: _buildCountries(context, dialogContext, countries),
        ),
      );
    },
  );
}

Widget _buildCountries(
  BuildContext context,
  BuildContext dialogContext,
  List<Country> countries,
) {
  return SizedBox(
    width: dialogWidth,
    child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: dialogContentPadding.copyWith(bottom: dimenTwelve),
          child: Text(
            context.localizations.selectCountryCode,
            style: dialogContext.appTextStyles.normalBold.copyWith(
              color: dialogContext.appColors.neutralShade1Color,
            ),
          ),
        ),
        Flexible(
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: countries.length,
            itemBuilder: (listViewContext, index) {
              return _CountryPhoneNumberCard(country: countries[index]);
            },
          ),
        ),
        Align(
          alignment: Alignment.centerRight,
          child: Padding(
            padding: actionContentPadding,
            child: PrimaryTextButton(
              text: context.localizations.cancel,
              onTap: context.rootNavigator.pop,
            ),
          ),
        ),
      ],
    ),
  );
}

class _CountryPhoneNumberCard extends StatelessWidget {
  final Country country;

  const _CountryPhoneNumberCard({Key? key, required this.country})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.rootNavigator.pop(country);
      },
      child: Ink(
        height: dimenFortyEight,
        padding: dialogContentPadding.copyWith(
          top: dimenZero,
          bottom: dimenZero,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CountryFlag.fromCountryCode(
              country.code.code,
              height: _getFlagHeight(dimenTwentyFour),
              width: dimenTwentyFour,
              shape: RoundedRectangle(dimenTwo),
            ),
            Expanded(
              child: Padding(
                padding: horizontalPaddingSixteen,
                child: Text(
                  BCNLocale.getLocalisedString(context, country.displayName),
                  style: context.appTextStyles.smallText1.copyWith(
                    color: context.appColors.neutralShade1Color,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
            ),
            Text(
              country.phoneCode,
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShade2Color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Country flag aspect ratio is always 4:3.
/// We have the width to be used but height depends
/// on this calculation.
/// To get height, we simply use aspect ratio formula.
/// Aspect ratio = width / height.
/// Hence, Height = width / Aspect Ratio .
double _getFlagHeight(double flagWidth) {
  return flagWidth / countryFlagAspectRatio;
}
