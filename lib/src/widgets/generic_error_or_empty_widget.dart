import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:flutter/material.dart';

/// Widget  when a full error screen or an empty state screen
/// is to be displayed.
class GenericErrorOrEmptyWidget extends StatelessWidget {
  /// Path to icon asset to be displayed on the screen.
  final String iconAssetPath;

  /// Label to be displayed to give user more info on what went wrong.
  final String labelText;

  /// Button to be displayed below [labelText].
  /// It will have a specific action to re-fetch data from server.
  final Widget? retryButton;

  const GenericErrorOrEmptyWidget({
    required this.iconAssetPath,
    required this.labelText,
    this.retryButton,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox.expand(
      child: Padding(
        padding: commonScreenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: dimenOneTwenty,
              width: dimenOneTwenty,
              decoration: _createCircularBoxDecoration(context),
              child: IconWidget(
                height: dimenFiftyTwo,
                width: dimenFiftyTwo,
                iconColor: context.appColors.neutralShade2Color,
                assetName: iconAssetPath,
              ),
            ),
            verticalGapSixteen,
            Text(
              labelText,
              textAlign: TextAlign.center,
              style: context.appTextStyles.smallText1.copyWith(
                color: context.appColors.neutralShade2Color,
              ),
            ),
            if (retryButton != null) ...{verticalGapSixteen, retryButton!},
          ],
        ),
      ),
    );
  }

  BoxDecoration _createCircularBoxDecoration(BuildContext context) {
    return BoxDecoration(
      shape: BoxShape.circle,
      color: context.appColors.neutralShade6Color,
    );
  }
}
