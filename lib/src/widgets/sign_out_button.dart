import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/rpc_handler.dart';
import 'package:bcn_agency_banking_flutter/src/resources/app_colors.dart';
import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/icon_widget.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/primary_text_button.dart';
import 'package:flutter/material.dart';

class SignOutButton extends StatelessWidget with RPCHandler {
  final Alignment alignment;

  /// The foreground color of the  button when it is enabled.
  /// Defaults to [AppColors.primaryLightColor].
  final Color? buttonForegroundColor;

  final bool isEnabled;

  const SignOutButton({
    super.key,
    this.alignment = Alignment.centerRight,
    this.buttonForegroundColor,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final foregroundColor =
        isEnabled
            ? (buttonForegroundColor ?? context.appColors.primaryLightColor)
            : context.appColors.neutralShade2Color;
    return Align(
      alignment: alignment,
      child: PrimaryTextButton(
        onTap:
            isEnabled
                ? () async {
                  final isSignOutUser =
                      await AgencyAppDialog.showConfirmationDialog(
                        context: context,
                        contentText: context.localizations.confirmSignOut,
                      );
                  if ((isSignOutUser ?? false) && context.mounted) {
                    AgencyAppDialog.showSpinnerDialog(context);
                    await rpcHandler(
                      () async {
                        await signOutUser(context);
                      },
                      onTransientError: (_) {
                        // Close the spinner dialog.
                        context.rootNavigator.pop();
                      },
                    );
                  }
                }
                : null,
        color: buttonForegroundColor,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconWidget(
              assetName: ABAssets.signOutIcon,
              iconColor: foregroundColor,
            ),
            horizontalGapEight,
            Text(
              context.localizations.signOut,
              style: context.appTheme.appTextStyles.smallText1Bold,
            ),
          ],
        ),
      ),
    );
  }
}
