import 'dart:io';

import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/flavors/flavors.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

import 'firebase_options_dev.dart' as dev_firebase;
import 'firebase_options_prod.dart' as prod_firebase;
import 'firebase_options_uat.dart' as uat_firebase;

Future<void> setupFirebase() async {
  // Firebase or Crashlytics is not required for Mock flavor.
  if (currentFlavor.isMock) return;
  late final FirebaseOptions firebaseOptions;
  if (currentFlavor.isDev) {
    firebaseOptions = dev_firebase.DefaultFirebaseOptions.currentPlatform;
  } else if (currentFlavor.isUAT) {
    firebaseOptions = uat_firebase.DefaultFirebaseOptions.currentPlatform;
  } else if (currentFlavor.isProd) {
    firebaseOptions = prod_firebase.DefaultFirebaseOptions.currentPlatform;
  } else {
    throw DeveloperError(
      "Firebase configurable file for $currentFlavor is not setup yet",
    );
  }

  await Firebase.initializeApp(options: firebaseOptions);

  await setupCrashlytics();
}

Future<void> setupCrashlytics() async {
  // Pass all uncaught errors from the framework to Crashlytics.
  FlutterError.onError = (errorDetails) async {
    await FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    // Crash the app only for DeveloperError.
    if (errorDetails.exception is DeveloperError) {
      exit(0);
    }
  };

  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    exit(0);
  };
}
