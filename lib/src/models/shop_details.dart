import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';
import 'package:bcn_agency_banking_flutter/src/models/photo.dart';
import 'package:flutter/material.dart';

class ShopDetails {
  final String name;
  final double latitude;
  final double longitude;
  final FormattedAddress? address;
  final Photo? photo;
  final ImageProvider? initialServerPhoto;
  final LocationDetails initialShopLocation;
  final bool isImageDeleted;

  ShopDetails({
    required this.name,
    required this.latitude,
    required this.longitude,
    this.address,
    this.photo,
    this.initialServerPhoto,
    required this.isImageDeleted,
    required this.initialShopLocation,
  });

  @override
  String toString() =>
      'ShopDetails(name: $name, latitude: $latitude, longitude: $longitude)'
      'address: $address, photo: $photo, initialServerPhoto: $initialServerPhoto, '
      'initialShopLocation: $initialShopLocation, isImageDeleted: $isImageDeleted';
}
