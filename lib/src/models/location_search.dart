import 'package:json_annotation/json_annotation.dart';

part 'location_search.g.dart';

@JsonSerializable()
class PlacesTextSearchResponse extends GoogleResponseStatus {
  @JsonKey(defaultValue: <Prediction>[])
  final List<Prediction> results;

  PlacesTextSearchResponse({
    required String status,
    String? errorMessage,
    required this.results,
  }) : super(status: status, errorMessage: errorMessage);

  factory PlacesTextSearchResponse.fromJson(Map<String, dynamic> json) =>
      _$PlacesTextSearchResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PlacesTextSearchResponseToJson(this);
}

@JsonSerializable()
class Prediction {
  final String? name;

  @JsonKey(readValue: _readAddress)
  final String? formattedAddress;
  final String? id;

  @JsonKey(readValue: _readPlaceId)
  final String? placeId;

  Prediction({this.name, this.formattedAddress, this.id, this.placeId});

  static _readAddress(Map map, String key) => map["formatted_address"];

  static _readPlaceId(Map map, String key) => map["place_id"];

  factory Prediction.fromJson(Map<String, dynamic> json) =>
      _$PredictionFromJson(json);

  Map<String, dynamic> toJson() => _$PredictionToJson(this);
}

@JsonSerializable()
class PlacesDetailsResponse extends GoogleResponseStatus {
  final PlaceDetails result;

  PlacesDetailsResponse({
    required String status,
    String? errorMessage,
    required this.result,
  }) : super(status: status, errorMessage: errorMessage);

  factory PlacesDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$PlacesDetailsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PlacesDetailsResponseToJson(this);
}

@JsonSerializable()
class PlaceDetails {
  @JsonKey(readValue: _readAddress)
  final String? formattedAddress;
  final String? name;
  final String? id;
  final Geometry? geometry;

  static _readAddress(Map map, String key) => map["formatted_address"];

  PlaceDetails({this.id, this.name, this.formattedAddress, this.geometry});

  factory PlaceDetails.fromJson(Map<String, dynamic> json) =>
      _$PlaceDetailsFromJson(json);

  Map<String, dynamic> toJson() => _$PlaceDetailsToJson(this);
}

@JsonSerializable()
class Geometry {
  final Location location;

  Geometry({required this.location});

  factory Geometry.fromJson(Map<String, dynamic> json) =>
      _$GeometryFromJson(json);

  Map<String, dynamic> toJson() => _$GeometryToJson(this);
}

@JsonSerializable()
class Location {
  final double lat;
  final double lng;

  Location({required this.lat, required this.lng});

  factory Location.fromJson(Map<String, dynamic> json) =>
      _$LocationFromJson(json);

  Map<String, dynamic> toJson() => _$LocationToJson(this);

  @override
  String toString() => '$lat,$lng';
}

abstract class GoogleResponseStatus {
  static const okay = 'OK';
  static const zeroResults = 'ZERO_RESULTS';
  static const overQueryLimit = 'OVER_QUERY_LIMIT';
  static const requestDenied = 'REQUEST_DENIED';
  static const invalidRequest = 'INVALID_REQUEST';
  static const unknownErrorStatus = 'UNKNOWN_ERROR';
  static const notFound = 'NOT_FOUND';
  static const maxWaypointsExceeded = 'MAX_WAYPOINTS_EXCEEDED';
  static const maxRouteLengthExceeded = 'MAX_ROUTE_LENGTH_EXCEEDED';

  final String status;
  final String? errorMessage;

  bool get isOkay => status == okay;

  bool get hasNoResults => status == zeroResults;

  bool get isOverQueryLimit => status == overQueryLimit;

  bool get isDenied => status == requestDenied;

  bool get isInvalid => status == invalidRequest;

  bool get unknownError => status == unknownErrorStatus;

  bool get isNotFound => status == notFound;

  GoogleResponseStatus({required this.status, this.errorMessage});
}
