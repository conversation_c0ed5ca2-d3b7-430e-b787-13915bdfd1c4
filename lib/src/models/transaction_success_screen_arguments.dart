import 'package:agency_banking_rpcs/types/amount_type.dart';
import 'package:agency_banking_rpcs/types/transaction_status_detail_type.dart';
import 'package:flutter/material.dart';

class TransactionSuccessScreenArguments {
  final TransactionStatusDetail transactionStatusDetail;
  final DateTime succeededAt;
  final Amount amount;
  final String recordId;
  final VoidCallback? onDone;

  TransactionSuccessScreenArguments({
    required this.transactionStatusDetail,
    required this.succeededAt,
    required this.amount,
    required this.recordId,
    this.onDone,
  });
}
