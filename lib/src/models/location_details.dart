// Bad name is chosen because of a namespace clash which will repeatedly
// cause problems for developers on the project.
class LocationDetails {
  final double latitude;
  final double longitude;
  final double accuracy;
  final FormattedAddress? address;

  LocationDetails({
    required this.latitude,
    required this.longitude,
    this.accuracy = 0.0,
    this.address,
  });

  @override
  String toString() {
    return 'LocationDetails(latitude: $latitude, longitude: $longitude, '
        'accuracy: $accuracy, formattedAddress $address)';
  }
}

class FormattedAddress {
  final String addressLabel;
  final String addressText;

  FormattedAddress({required this.addressLabel, required this.addressText});

  @override
  String toString() =>
      "FormattedAddress(addressLabel: $addressLabel, addressText: $addressText)";
}
