import 'package:bcn_agency_banking_flutter/src/widgets/transaction_success_screen.dart';
import 'package:flutter/material.dart';

abstract class AppColors {
  /// Primary Color for the application.
  Color get primaryDefaultColor;

  /// Primary Light Color for the application.
  Color get primaryLightColor;

  /// Color used on the titles.
  Color get titleColor;

  /// Color used for subtitles and enabled icons.
  Color get neutralShadeDefaultColor;

  /// Color used for list tile title and text field content.
  Color get neutralShade1Color;

  /// Color used for helper text and suffix text of text field
  /// and content of empty widget
  Color get neutralShade2Color;

  /// Color used for disabled state of primary button and divider.
  Color get neutralShade3Color;

  /// Color user for background of avatar icon, background of empty state of photo preview widget
  /// and active selection color for shop drop down.
  Color get neutralShade4Color;

  /// Color user for avatar icon.
  Color get neutralShade5Color;

  /// Color user for border of the card and background of empty state widget.
  Color get neutralShade6Color;

  /// Color user for info label color, card content color,
  /// supporting text color [TransactionSuccessScreen].
  Color get neutralShade7Color;

  /// Color user for dotted divider, outlined button border color.
  Color get neutralShade8Color;

  /// Color used for icons on search bar.
  Color get neutralShade9Color;

  Color get imagePreviewEmptyStateContentColor;

  Color get titleBackgroundColor;

  Color get genericWhiteColor;

  Color get errorColor;

  Color get errorStateBackgroundColor;

  Color get errorStateTextColor;

  Color get errorTextFieldColor;

  Color get successShade1Color;

  Color get successShade2Color;

  /// Color used for [TransactionSuccessScreen].
  Color get successShade3Color;

  /// Color used for dialog background and shop dropdown.
  Color get surfaceColor;

  Color get backgroundColor;

  Color get warningColor;

  Color get switchActiveThumbColor;

  Color get switchInactiveThumbColor;

  Color get switchActiveTrackColor;

  Color get switchInactiveTrackColor;

  Color get snackBarActionTextColor;

  Color get dropdownDisabledColor;

  Color get tabBarUnselectedLabelColor;
}

class LightThemeColors extends AppColors {
  @override
  Color get backgroundColor => _white;

  @override
  Color get errorColor => _semanticErrorDark;

  @override
  Color get errorStateBackgroundColor => _semanticErrorLighter;

  @override
  Color get errorStateTextColor => _semanticErrorDarker;

  @override
  Color get errorTextFieldColor => _semanticErrorDarker;

  @override
  Color get genericWhiteColor => _white;

  @override
  Color get imagePreviewEmptyStateContentColor => _neutralDarkLight;

  @override
  Color get neutralShade1Color => _neutralDarkDark;

  @override
  Color get neutralShade2Color => _neutralDarkLight;

  @override
  Color get neutralShade3Color => _neutralLightDarker;

  @override
  Color get neutralShade4Color => _neutralLightDefault;

  @override
  Color get neutralShade5Color => _neutralDarkDefault;

  @override
  Color get neutralShade6Color => _neutralLightDarker;

  @override
  Color get neutralShade7Color => _neutralDarkDefault;

  @override
  Color get neutralShade8Color => neutralDarkLighter;

  @override
  Color get neutralShade9Color => _neutralDarkDark;

  @override
  Color get neutralShadeDefaultColor => _neutralDarkDefault;

  @override
  Color get primaryDefaultColor => _basicPrimaryDefault;

  @override
  Color get primaryLightColor => _basicPrimaryDefault;

  @override
  Color get successShade1Color => _semanticSuccessDarker;

  @override
  Color get successShade2Color => _semanticSuccessDefault;

  @override
  Color get successShade3Color => _semanticSuccessDark;

  @override
  Color get surfaceColor => _white;

  @override
  Color get titleBackgroundColor => _neutralLightDefault;

  @override
  Color get titleColor => _neutralDarkDarker;

  @override
  Color get warningColor => _semanticWarningDarker;

  @override
  Color get switchActiveThumbColor => _basicPrimaryDefault;

  @override
  Color get switchActiveTrackColor => _basicPrimaryLight;

  @override
  Color get switchInactiveThumbColor => _white;

  @override
  Color get switchInactiveTrackColor => neutralDarkLighter;

  @override
  Color get snackBarActionTextColor => _basicPrimaryLight;

  @override
  Color get dropdownDisabledColor => neutralDarkLighter;

  @override
  Color get tabBarUnselectedLabelColor => _basicPrimaryLight;
}

class DarkThemeColors extends AppColors {
  @override
  Color get backgroundColor => _black;

  @override
  Color get errorColor => _semanticErrorDark;

  @override
  Color get errorStateBackgroundColor => _semanticErrorDarker;

  @override
  Color get errorStateTextColor => _semanticErrorLighter;

  @override
  Color get errorTextFieldColor => _semanticErrorDefault;

  @override
  Color get genericWhiteColor => _white;

  @override
  Color get imagePreviewEmptyStateContentColor => neutralDarkLighter;

  @override
  Color get neutralShade1Color => _neutralLightLight;

  @override
  Color get neutralShade2Color => _neutralDarkLight;

  @override
  Color get neutralShade3Color => _neutralDarkDark;

  @override
  Color get neutralShade4Color => _neutralDarkDefault;

  @override
  Color get neutralShade5Color => neutralDarkLighter;

  @override
  Color get neutralShade6Color => _neutralDarkDefault;

  @override
  Color get neutralShade7Color => _neutralDarkLight;

  @override
  Color get neutralShade8Color => _neutralDarkDefault;

  @override
  Color get neutralShade9Color => _neutralLightDefault;

  @override
  Color get neutralShadeDefaultColor => _neutralLightDefault;

  @override
  Color get primaryDefaultColor => _basicPrimaryDefault;

  @override
  Color get primaryLightColor => _basicPrimaryLight;

  @override
  Color get successShade1Color => _semanticSuccessDark;

  @override
  Color get successShade2Color => _semanticSuccessDefault;

  @override
  Color get successShade3Color => _semanticSuccessDark;

  @override
  Color get surfaceColor => _neutralDarkDark;

  @override
  Color get titleBackgroundColor => _neutralDarkDark;

  @override
  Color get titleColor => _white;

  @override
  Color get warningColor => _semanticWarningDefault;

  @override
  Color get switchActiveThumbColor => _basicPrimaryDefault;

  @override
  Color get switchActiveTrackColor => _basicPrimaryLight;

  @override
  Color get switchInactiveThumbColor => _neutralLightDarker;

  @override
  Color get switchInactiveTrackColor => _neutralDarkLight;

  @override
  Color get snackBarActionTextColor => _basicPrimaryDefault;

  @override
  Color get dropdownDisabledColor => neutralDarkLighter;

  @override
  Color get tabBarUnselectedLabelColor => _basicPrimaryLight;
}

// The following color names correspond to the naming methodology used in the
// design system here
// https://www.figma.com/file/3PxMC7Mp5tkKH2AaHYY13T/Agency-Banking?node-id=14859-28125&t=BfIJfIgAwkFIgOH5-0.
const Color _white = Color(0xFFFFFFFF);
const Color _black = Color(0xFF1C1C1E);
const Color _neutralLightDefault = Color(0xFFF2F2F5);
const Color _neutralLightLight = Color(0xFFFAFAFC);
const Color _neutralLightDarker = Color(0xFFE3E4EB);
const Color _neutralDarkDefault = Color(0xFF555770);
const Color neutralDarkLighter = Color(0xFFC7C8D9);
const Color _neutralDarkLight = Color(0xFF8E90A6);
const Color _neutralDarkDark = Color(0xFF28293D);
const Color _neutralDarkDarker = Color(0xFF1C1C28);
const Color _basicPrimaryDefault = Color(0xFF6A39F1);
const Color _basicPrimaryLight = Color(0xFFB49CF8);
const Color _semanticErrorDefault = Color(0xFFFF5C5C);
const Color _semanticErrorLighter = Color(0xFFFFE5E5);
const Color _semanticErrorDark = Color(0xFFFF3B3B);
const Color _semanticErrorDarker = Color(0xFFC60000);
const Color _semanticSuccessDefault = Color(0xFF39D98A);
const Color _semanticSuccessDark = Color(0xFF06C270);
const Color _semanticSuccessDarker = Color(0xFF1C9559);
const Color _semanticWarningDarker = Color(0xFFB66802);
const Color _semanticWarningDefault = Color(0xFFFDAC42);
