abstract class AppIcons {
  String get emptyStateImage;

  String get cupertinoArrowRight;

  String get close;

  String get profile;

  String get contact;

  String get location;

  String get mapMarker;

  String get agent;

  String get agentManager;

  String get applyToBeAnAgentIcon;

  String get signOut;

  String get checkCircle;

  String get backArrow;

  String get personPlaceholder;

  String get currentLocation;

  String get success;

  String get failed;

  String get lock;

  String get infoCircle;

  String get pencil;

  String get calendar;

  String get downArrow;

  String get cashIn;

  String get cashOut;

  String get appBarBackArrow;

  String get home;

  String get homeActive;

  String get commissionEarnedIcon;

  String get commissionEarnedIconActive;

  String get profileIconActive;

  String get commissionCardBackground;

  String get walletCardBackground;

  String get scanQr;

  String get greenCircle;

  String get redCircle;

  String get dropDownArrow;

  String get secondaryRightArrow;

  String get dropdownIcon;

  String get greenIcon;

  String get redIcon;

  String get manageAccount;

  String get appPin;

  String get changeLanguage;

  String get privacyPolicy;

  String get signOutProfile;

  String get resignAgent;

  String get profileInfo;

  String get errorIcon;

  String get exchange;

  String get noCommissionTransactionIcon;

  String get noAccruedTransactionIcon;

  String get scanQrFaded;

  String get cupertinoArrowRightFaded;

  String get secondaryInfoCircle;

  String get noRequests;

  String get agencyBankingLogo;

  String get landingScreenAppIcon;

  String get walletIcon;
}

class LightThemeIcons extends AppIcons {
  String _getIcon(String fileName) {
    return 'assets/light/$fileName.png';
  }

  @override
  String get cupertinoArrowRight => _getIcon('cupertino_arrow_right_light');

  @override
  String get close => _getIcon('close');

  @override
  String get contact => _getIcon('contact');

  @override
  String get location => _getIcon('location');

  @override
  String get profile => _getIcon('profile');

  @override
  String get mapMarker => _getIcon('location_marker');

  @override
  String get agent => _getIcon('agent_icon_light');

  @override
  String get agentManager => _getIcon('agent_manager_icon_light');

  @override
  String get applyToBeAnAgentIcon => _getIcon('library_books_icon_light');

  @override
  String get signOut => _getIcon('signOut_icon_dark');

  @override
  String get checkCircle => _getIcon('check_circle');

  @override
  String get backArrow => _getIcon('back_arrow_light');

  @override
  String get currentLocation => _getIcon('current_location');

  @override
  String get personPlaceholder => _getIcon('person');

  @override
  String get success => _getIcon('success');

  @override
  String get failed => _getIcon('failed');

  @override
  String get lock => _getIcon('lock_icon_light');

  @override
  String get infoCircle => _getIcon('info_circle');

  @override
  String get pencil => _getIcon('pencil');

  @override
  String get calendar => _getIcon('calendar');

  @override
  String get downArrow => _getIcon('down_arrow');

  @override
  String get cashIn => _getIcon('cash_in');

  @override
  String get cashOut => _getIcon('cash_out');

  @override
  String get appBarBackArrow => _getIcon('app_bar_back_arrow');

  @override
  String get home => _getIcon("home");

  @override
  String get homeActive => _getIcon("home_active");

  @override
  String get commissionEarnedIcon => _getIcon("commissions_earned");

  @override
  String get commissionEarnedIconActive =>
      _getIcon("commissions_earned_active");

  @override
  String get profileIconActive => _getIcon("profile_active");

  @override
  String get commissionCardBackground => _getIcon("commission_card_background");

  @override
  String get walletCardBackground => _getIcon("wallet_card_background");

  @override
  String get scanQr => _getIcon("scan_qr");

  @override
  String get greenCircle => _getIcon('green_circle');

  @override
  String get redCircle => _getIcon('red_circle');

  @override
  String get dropDownArrow => _getIcon('drop_down_arrow');

  @override
  String get exchange => _getIcon('exchange');

  @override
  String get secondaryRightArrow => _getIcon('secondary_right_arrow');

  @override
  String get errorIcon => _getIcon('error');

  @override
  String get noAccruedTransactionIcon =>
      _getIcon('transaction_empty_state_icon_light');

  @override
  String get scanQrFaded => _getIcon('scan_qr_faded');

  @override
  String get cupertinoArrowRightFaded =>
      _getIcon('cupertino_arrow_right_faded');

  @override
  String get secondaryInfoCircle => _getIcon('secondary_info_circle');

  @override
  String get noCommissionTransactionIcon =>
      _getIcon('commission_empty_state_icon_light');

  @override
  String get dropdownIcon => _getIcon('dropdown_icon');

  @override
  String get greenIcon => _getIcon('green_icon');

  @override
  String get redIcon => _getIcon('red_icon');

  @override
  String get appPin => _getIcon('change_app_pin');

  @override
  String get changeLanguage => _getIcon('language');

  @override
  String get manageAccount => _getIcon('settings');

  @override
  String get privacyPolicy => _getIcon('privacy_policy');

  @override
  String get signOutProfile => _getIcon('signout');

  @override
  String get profileInfo => _getIcon('user_icon');

  @override
  String get noRequests => _getIcon('requests');

  @override
  String get resignAgent => _getIcon("resign_agent");

  @override
  String get emptyStateImage => _getIcon("empty_state_image_icon");

  @override
  String get agencyBankingLogo => _getIcon("agency_banking_logo_light");

  @override
  String get landingScreenAppIcon => _getIcon("agency_banking_logo_white");

  @override
  String get walletIcon => _getIcon("wallet_icon");
}

class DarkThemeIcons extends AppIcons {
  String _getIcon(String fileName) {
    return 'assets/dark/$fileName.png';
  }

  @override
  String get cupertinoArrowRight => _getIcon('cupertino_arrow_right_dark');

  @override
  String get close => _getIcon('close');

  @override
  String get contact => _getIcon('contact');

  @override
  String get location => _getIcon('location');

  @override
  String get profile => _getIcon('profile');

  @override
  String get mapMarker => _getIcon('location_marker');

  @override
  String get agent => _getIcon('agent_icon_dark');

  @override
  String get agentManager => _getIcon('agent_manager_icon_dark');

  @override
  String get applyToBeAnAgentIcon => _getIcon('library_books_icon_dark');

  @override
  String get signOut => _getIcon('signOut_icon_dark');

  @override
  String get checkCircle => _getIcon('check_circle');

  @override
  String get backArrow => _getIcon('back_arrow_dark');

  @override
  String get personPlaceholder => _getIcon('person');

  @override
  String get currentLocation => _getIcon('current_location');

  @override
  String get success => _getIcon('success');

  @override
  String get failed => _getIcon('failed');

  @override
  String get lock => _getIcon('lock_icon_dark');

  @override
  String get infoCircle => _getIcon('info_circle');

  @override
  String get pencil => _getIcon('pencil');

  @override
  String get calendar => _getIcon('calendar');

  @override
  String get downArrow => _getIcon('down_arrow');

  @override
  String get cashIn => _getIcon('cash_in');

  @override
  String get cashOut => _getIcon('cash_out');

  @override
  String get appBarBackArrow => _getIcon('app_bar_back_arrow');

  @override
  String get dropdownIcon => _getIcon('dropdown_icon');

  @override
  String get home => _getIcon("home");

  @override
  String get homeActive => _getIcon("home_active");

  @override
  String get commissionEarnedIcon => _getIcon("commissions_earned");

  @override
  String get commissionEarnedIconActive =>
      _getIcon("commissions_earned_active");

  @override
  String get profileIconActive => _getIcon("profile_active");

  @override
  String get commissionCardBackground => _getIcon("commission_card_background");

  @override
  String get walletCardBackground => _getIcon("wallet_card_background");

  @override
  String get scanQr => _getIcon("scan_qr");

  @override
  String get greenCircle => _getIcon('green_circle');

  @override
  String get redCircle => _getIcon('red_circle');

  @override
  String get dropDownArrow => _getIcon('drop_down_arrow');

  @override
  String get secondaryRightArrow => _getIcon('secondary_right_arrow');

  @override
  String get errorIcon => _getIcon('error');

  @override
  String get exchange => _getIcon('exchange');

  @override
  String get scanQrFaded => _getIcon('scan_qr_faded');

  @override
  String get cupertinoArrowRightFaded =>
      _getIcon('cupertino_arrow_right_faded');

  @override
  String get greenIcon => _getIcon('green_icon');

  @override
  String get redIcon => _getIcon('red_icon');

  @override
  String get appPin => _getIcon('change_app_pin');

  @override
  String get changeLanguage => _getIcon('language');

  @override
  String get manageAccount => _getIcon('settings');

  @override
  String get privacyPolicy => _getIcon('privacy_policy');

  @override
  String get signOutProfile => _getIcon('signout');

  @override
  String get profileInfo => _getIcon('user_icon');

  @override
  String get secondaryInfoCircle => _getIcon('secondary_info_circle');

  @override
  String get noAccruedTransactionIcon =>
      _getIcon('transaction_empty_state_icon_dark');

  @override
  String get noCommissionTransactionIcon =>
      _getIcon('commission_empty_state_icon_dark');

  @override
  String get noRequests => _getIcon('requests');

  @override
  String get resignAgent => _getIcon("resign_agent");

  @override
  String get emptyStateImage => _getIcon("empty_state_image_icon");

  @override
  String get agencyBankingLogo => _getIcon("agency_banking_logo_dark");

  @override
  String get landingScreenAppIcon => _getIcon("agency_banking_logo_dark");

  @override
  String get walletIcon => _getIcon("wallet_icon");
}
