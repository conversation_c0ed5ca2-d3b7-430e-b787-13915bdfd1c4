class ABAssets {
  ABAssets._();

  // All Images will come here.
  static final bcnLogo = _getImagePath("bcn_logo.svg");
  static final applicationScreenImageDark = _getImagePath(
    "application_screen_image_dark.svg",
  );
  static final applicationScreenImageLight = _getImagePath(
    "application_screen_image_light.svg",
  );
  static final landingScreenImageLight = _getImagePath(
    "landing_screen_image_light.svg",
  );
  static final landingScreenImageDark = _getImagePath(
    "landing_screen_image_dark.svg",
  );
  static final setupSessionPinScreenImageLight = _getImagePath(
    "setup_session_pin_screen_image_light.svg",
  );
  static final setupSessionPinScreenImageDark = _getImagePath(
    "setup_session_pin_screen_image_dark.svg",
  );

  // All Icons will come here.
  static final usersIcon = _getIconPath("users.svg");
  static final userCircleIcon = _getIconPath("user_circle.svg");
  static final arrowRightIcon = _getIconPath("arrow_right.svg");
  static final locationPointIcon = _getIconPath("location_point.svg");
  static final cashInHomeIcon = _getIconPath("cash_in_home.svg");
  static final cashOutHomeIcon = _getIconPath("cash_out_home.svg");
  static final exchangeAltCircleIcon = _getIconPath("exchange_alt_circle.svg");
  static final cameraIcon = _getIconPath("camera.svg");
  static final imageIcon = _getIconPath("gallery.svg");
  static final cloudSlashIcon = _getIconPath("cloud_slash.svg");
  static final exchangeAltIcon = _getIconPath("exchange_alt.svg");
  static final exclamationCircleIcon = _getIconPath("exclamation_circle.svg");
  static final fileSlashIcon = _getIconPath("file_slash.svg");
  static final fileTimesAltIcon = _getIconPath("file_times_alt.svg");
  static final stopwatchSlashIcon = _getIconPath("stopwatch_slash.svg");
  static final lockIcon = _getIconPath("lock.svg");
  static final phoneIcon = _getIconPath("phone.svg");
  static final visibilityIcon = _getIconPath("visibility.svg");
  static final visibilityOffIcon = _getIconPath("visibility_off.svg");
  static final homeAltIcon = _getIconPath("home_alt.svg");
  static final moneyBillIcon = _getIconPath("money_bill.svg");
  static final userIcon = _getIconPath("user.svg");
  static final arrowUpRightIcon = _getIconPath("arrow_up_right.svg");
  static final arrowDownLeftIcon = _getIconPath("arrow_down_left.svg");
  static final userLocationIcon = _getIconPath("user_location.svg");
  static final checkCircleIcon = _getIconPath("check_circle.svg");
  static final copyIcon = _getIconPath("copy.svg");
  static final questionCircleIcon = _getIconPath("question_circle.svg");
  static final shareAltIcon = _getIconPath("share_alt.svg");
  static final envelopeIcon = _getIconPath("envelope.svg");
  static final closeIcon = _getIconPath("close.svg");
  static final arrowDropDownIcon = _getIconPath("arrow_drop_down.svg");
  static final arrowLeftIcon = _getIconPath("arrow_left.svg");
  static final searchIcon = _getIconPath("search.svg");
  static final locationIcon = _getIconPath("location_icon.svg");
  static final refundAmountIcon = _getIconPath("refund_amount_icon.svg");
  static final userPlusIcon = _getIconPath("user_plus.svg");
  static final walletIcon = _getIconPath("wallet.svg");
  static final shieldCheckIcon = _getIconPath("shield_check.svg");
  static final languageIcon = _getIconPath("language_icon.svg");
  static final fileIcon = _getIconPath("file.svg");
  static final arrowDownIcon = _getIconPath("arrow_down.svg");
  static final checkIcon = _getIconPath("check.svg");
  static final signOutIcon = _getIconPath("sign_out.svg");
  static final arrowIcon = _getIconPath("arrow.svg");
  static final failedToFetchIcon = _getIconPath("failed_to_fetch.svg");
  static final editIcon = _getIconPath("edit.svg");
  static final directionsIcon = _getIconPath("directions.svg");
  static final infoCircleIcon = _getIconPath("info_circle.svg");

  // Maps styles will come here.
  static final String darkMapStylePath = _getMapStylePath(isDark: true);
  static final String lightMapStylePath = _getMapStylePath(isDark: false);

  static String _getImagePath(String assetName) => "assets/images/$assetName";

  static String _getIconPath(String assetName) => "assets/ab_icons/$assetName";

  static String _getMapStylePath({required bool isDark}) =>
      "assets/maps/${isDark ? "dark" : "light"}.json";
}
