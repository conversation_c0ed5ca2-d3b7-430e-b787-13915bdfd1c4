import 'package:bcn_agency_banking_flutter/src/resources/ab_assets.dart';
import 'package:flutter/services.dart';

class MapStyles {
  MapStyles._();

  static bool _isInitialized = false;
  static late final String darkMapStyle;
  static late final String lightMapStyle;

  static Future<void> loadMapStyles() async {
    if (!_isInitialized) {
      _isInitialized = true;
      darkMapStyle = await _getMapStyle(isDark: true);
      lightMapStyle = await _getMapStyle(isDark: false);
    }
  }

  static Future<String> _getMapStyle({required bool isDark}) async {
    return await rootBundle.loadString(
      isDark ? ABAssets.darkMapStylePath : ABAssets.lightMapStylePath,
    );
  }
}
