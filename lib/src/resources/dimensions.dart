import 'package:flutter/material.dart';

// Dimensions related to dialogs.
const dialogWidth = 280.0;
const dialogContentPadding = EdgeInsets.all(dimenTwentyFour);
const actionContentPadding = EdgeInsets.all(dimenEight);

const dimenZero = 0.0;
const dimenPointTwoFive = 0.25;
const dimenPointFive = 0.5;
const dimenOne = 1.0;
const dimenOnePointThree = 1.3;
const dimenOnePointSixSeven = 1.67;
const dimenTwo = 2.0;
const dimenFour = 4.0;
const dimenSix = 6.0;
const dimenEight = 8.0;
const dimenTwelve = 12.0;
const dimenFourteen = 14.0;
const dimenSixteen = 16.0;
const dimenTwenty = 20.0;
const dimenTwentyFour = 24.0;
const dimenThirty = 30.0;
const dimenThirtyTwo = 32.0;
const dimenThirtySix = 36.0;
const dimenForty = 40.0;
const dimenFortyEight = 48.0;
const dimenFiftyTwo = 52.0;
const dimenFiftySix = 56.0;
const dimenSixtyFour = 64.0;
const dimenSeventyTwo = 72.0;
const dimenEighty = 80.0;
const dimenEightyEight = 88.0;
const dimenNinetyTwo = 92.0;
const dimenOneTwenty = 120.0;
const dimenOneSixty = 160.0;

const verticalGapTwo = SizedBox(height: dimenTwo);
const verticalGapFour = SizedBox(height: dimenFour);
const verticalGapEight = SizedBox(height: dimenEight);
const verticalGapTwelve = SizedBox(height: dimenTwelve);
const verticalGapSixteen = SizedBox(height: dimenSixteen);
const verticalGapTwenty = SizedBox(height: dimenTwenty);
const verticalGapTwentyFour = SizedBox(height: dimenTwentyFour);
const verticalGapThirtyTwo = SizedBox(height: dimenThirtyTwo);
const verticalGapThirtySix = SizedBox(height: dimenThirtySix);
const verticalGapForty = SizedBox(height: dimenForty);
const verticalGapFiftySix = SizedBox(height: dimenFiftySix);
const verticalGapEighty = SizedBox(height: dimenEighty);
const horizontalGapFour = SizedBox(width: dimenFour);
const horizontalGapEight = SizedBox(width: dimenEight);
const horizontalGapTwelve = SizedBox(width: dimenTwelve);
const horizontalGapSixteen = SizedBox(width: dimenSixteen);
const horizontalGapThirtyTwo = SizedBox(width: dimenThirtyTwo);

const commonScreenPadding = EdgeInsets.all(dimenSixteen);
const allPaddingFour = EdgeInsets.all(dimenFour);
const allPaddingEight = EdgeInsets.all(dimenEight);
const allPaddingSixteen = EdgeInsets.all(dimenSixteen);
const allPaddingTwentyFour = EdgeInsets.all(dimenTwentyFour);
const horizontalPaddingFour = EdgeInsets.symmetric(horizontal: dimenFour);
const verticalPaddingFour = EdgeInsets.symmetric(vertical: dimenFour);
const horizontalPaddingEight = EdgeInsets.symmetric(horizontal: dimenEight);
const verticalPaddingEight = EdgeInsets.symmetric(vertical: dimenEight);
const horizontalPaddingSixteen = EdgeInsets.symmetric(horizontal: dimenSixteen);
const verticalPaddingSixteen = EdgeInsets.symmetric(vertical: dimenSixteen);

final cardBorderRadius = BorderRadius.circular(dimenEight);

const buttonHeight = Size.fromHeight(dimenForty);

const textFieldContentPadding = allPaddingFour;

// Country Flag related.
const countryFlagAspectRatio = 4 / 3;

// Icon size related.
const defaultIconSize = dimenTwentyFour;

// AppBar related
const appBarElevation = dimenZero;

// Spinner dialog related
const strokeWidth = 3.33;

// Location search bar related.
const appBarBorderWidth = 1.0;

// Divider related.
const dividerThickness = 1.0;
