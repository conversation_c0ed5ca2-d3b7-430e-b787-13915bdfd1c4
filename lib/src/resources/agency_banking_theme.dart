import 'package:bcn_agency_banking_flutter/src/resources/app_colors.dart';
import 'package:bcn_agency_banking_flutter/src/resources/app_text_styles.dart';
import 'package:bcn_agency_banking_flutter/src/resources/dimensions.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

import '../utils/constants.dart';
import 'app_icons.dart';

class AgencyBankingTheme {
  AgencyBankingTheme._();

  static final _lightThemeColors = LightThemeColors();
  static final _darkThemeColors = DarkThemeColors();
  static final _appTextStyles = AppTextStyles();

  static final lightTheme = ThemeData(
    appBarTheme: AppBarTheme(
      color: _lightThemeColors.primaryDefaultColor,
      actionsIconTheme: IconThemeData(
        color: _lightThemeColors.genericWhiteColor,
      ),
      iconTheme: IconThemeData(color: _lightThemeColors.genericWhiteColor),
      titleTextStyle: _appTextStyles.titleBold.copyWith(
        color: _lightThemeColors.genericWhiteColor,
      ),
      elevation: appBarElevation,
    ),
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: _lightThemeColors.surfaceColor,
    ),
    tabBarTheme: TabBarTheme(
      labelStyle: _appTextStyles.buttonText2.copyWith(
        color: _lightThemeColors.genericWhiteColor,
      ),
      unselectedLabelStyle: _appTextStyles.buttonText2.copyWith(
        color: _lightThemeColors.genericWhiteColor,
      ),
      indicatorSize: TabBarIndicatorSize.tab,
    ),
    colorScheme: ColorScheme.light(
      primary: _lightThemeColors.primaryDefaultColor,
      secondary: _lightThemeColors.primaryDefaultColor,
      surface: _lightThemeColors.backgroundColor,
    ),
    primaryColor: _lightThemeColors.primaryDefaultColor,
    scaffoldBackgroundColor: _lightThemeColors.backgroundColor,
    dividerTheme: DividerThemeData(color: _lightThemeColors.neutralShade3Color),
    cardColor: _lightThemeColors.backgroundColor,
    dialogTheme: DialogThemeData(
      backgroundColor: _lightThemeColors.surfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(dimenFour),
      ),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      selectedLabelStyle: _appTextStyles.labelText4.copyWith(
        color: _lightThemeColors.primaryDefaultColor,
      ),
      unselectedLabelStyle: _appTextStyles.labelText4.copyWith(
        color: _lightThemeColors.neutralShade2Color,
      ),
      selectedItemColor: _lightThemeColors.primaryDefaultColor,
      backgroundColor: _lightThemeColors.backgroundColor,
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: ButtonStyle(
        fixedSize: WidgetStatePropertyAll(buttonHeight),
        textStyle: WidgetStatePropertyAll(
          _appTextStyles.buttonText1.copyWith(
            color: _lightThemeColors.primaryLightColor,
          ),
        ),
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(
            side: BorderSide(color: _lightThemeColors.neutralShade8Color),
            borderRadius: BorderRadius.circular(dimenFour),
          ),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        textStyle: _appTextStyles.buttonText1,
        foregroundColor: _lightThemeColors.primaryLightColor,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(dimenFour),
        ),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        fixedSize: WidgetStatePropertyAll(buttonHeight),
        elevation: WidgetStatePropertyAll(dimenZero),
        textStyle: WidgetStatePropertyAll(
          _appTextStyles.buttonText1.copyWith(
            color: _lightThemeColors.genericWhiteColor,
          ),
        ),
        backgroundColor: WidgetStatePropertyAll(
          _lightThemeColors.primaryDefaultColor,
        ),
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(dimenFour),
          ),
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      contentPadding: textFieldContentPadding,
      labelStyle: _appTextStyles.labelText1.copyWith(
        color: _lightThemeColors.neutralShade2Color,
      ),
      floatingLabelStyle: WidgetStateTextStyle.resolveWith((
        Set<WidgetState> states,
      ) {
        late final Color color;
        // Change text color based on the material state of the
        // text field.
        if (states.contains(WidgetState.error)) {
          color = _lightThemeColors.errorTextFieldColor;
        } else if (states.contains(WidgetState.focused)) {
          color = _lightThemeColors.primaryLightColor;
        } else {
          color = _lightThemeColors.neutralShade2Color;
        }
        // Keep this as labelText1 as TextField will
        // decrease the size of the floatingLabelStyle out of the box
        // when focused in proportionate with the
        // unfocused label style.
        // https://m3.material.io/components/text-fields/specs.
        return _appTextStyles.labelText1.copyWith(color: color);
      }),
      border: UnderlineInputBorder(
        borderSide: BorderSide(color: _lightThemeColors.neutralShade2Color),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: _lightThemeColors.primaryLightColor,
          width: dimenTwo,
        ),
      ),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: _lightThemeColors.neutralShade2Color),
      ),
      errorBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: _lightThemeColors.errorTextFieldColor),
      ),
      focusedErrorBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: _lightThemeColors.errorTextFieldColor,
          width: dimenTwo,
        ),
      ),
      errorStyle: _appTextStyles.labelText3.copyWith(
        color: _lightThemeColors.errorTextFieldColor,
      ),
      errorMaxLines: errorMaxLines,
    ),
    textTheme: ThemeData.light().textTheme
        .apply(
          displayColor: _lightThemeColors.neutralShadeDefaultColor,
          bodyColor: _lightThemeColors.neutralShadeDefaultColor,
        )
        .copyWith(
          // This is defined for the text style of the `SnackBarAction`.
          labelLarge: _appTextStyles.buttonText2,
        ),
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: _lightThemeColors.primaryDefaultColor,
    ),
    switchTheme: SwitchThemeData(
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return _lightThemeColors.switchActiveTrackColor;
        }
        return _lightThemeColors.switchInactiveTrackColor;
      }),
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return _lightThemeColors.switchActiveThumbColor;
        }
        return _lightThemeColors.switchInactiveThumbColor;
      }),
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return _lightThemeColors.primaryLightColor;
        }
        return _lightThemeColors.neutralShade2Color;
      }),
    ),
  );

  static final darkTheme = ThemeData(
    appBarTheme: AppBarTheme(
      color: _darkThemeColors.primaryDefaultColor,
      actionsIconTheme: IconThemeData(
        color: _darkThemeColors.genericWhiteColor,
      ),
      iconTheme: IconThemeData(color: _darkThemeColors.genericWhiteColor),
      titleTextStyle: _appTextStyles.titleBold.copyWith(
        color: _darkThemeColors.genericWhiteColor,
      ),
      elevation: appBarElevation,
    ),
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: _darkThemeColors.surfaceColor,
    ),
    tabBarTheme: TabBarTheme(
      labelStyle: _appTextStyles.buttonText2.copyWith(
        color: _darkThemeColors.genericWhiteColor,
      ),
      unselectedLabelStyle: _appTextStyles.buttonText2.copyWith(
        color: _darkThemeColors.genericWhiteColor,
      ),
      indicatorSize: TabBarIndicatorSize.tab,
    ),
    colorScheme: ColorScheme.dark(
      primary: _darkThemeColors.primaryDefaultColor,
      secondary: _darkThemeColors.primaryLightColor,
      surface: _darkThemeColors.backgroundColor,
    ),
    primaryColor: _darkThemeColors.primaryDefaultColor,
    scaffoldBackgroundColor: _darkThemeColors.backgroundColor,
    dividerTheme: DividerThemeData(color: _darkThemeColors.neutralShade3Color),
    cardColor: _darkThemeColors.backgroundColor,
    dialogTheme: DialogThemeData(
      backgroundColor: _darkThemeColors.surfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(dimenFour),
      ),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      selectedLabelStyle: _appTextStyles.labelText4.copyWith(
        color: _darkThemeColors.primaryDefaultColor,
      ),
      unselectedLabelStyle: _appTextStyles.labelText4.copyWith(
        color: _darkThemeColors.neutralShade2Color,
      ),
      selectedItemColor: _darkThemeColors.primaryDefaultColor,
      backgroundColor: _darkThemeColors.backgroundColor,
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: ButtonStyle(
        minimumSize: WidgetStatePropertyAll(buttonHeight),
        textStyle: WidgetStatePropertyAll(
          _appTextStyles.buttonText1.copyWith(
            color: _darkThemeColors.primaryLightColor,
          ),
        ),
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(
            side: BorderSide(color: _darkThemeColors.neutralShade8Color),
            borderRadius: BorderRadius.circular(dimenFour),
          ),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        textStyle: _appTextStyles.buttonText1,
        foregroundColor: _darkThemeColors.primaryLightColor,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(dimenFour),
        ),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        minimumSize: WidgetStatePropertyAll(buttonHeight),
        elevation: WidgetStatePropertyAll(dimenZero),
        textStyle: WidgetStatePropertyAll(
          _appTextStyles.buttonText1.copyWith(
            color: _darkThemeColors.genericWhiteColor,
          ),
        ),
        backgroundColor: WidgetStatePropertyAll(
          _darkThemeColors.primaryDefaultColor,
        ),
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(dimenFour),
          ),
        ),
      ),
    ),
    textTheme: ThemeData.dark().textTheme
        .apply(
          displayColor: _darkThemeColors.neutralShadeDefaultColor,
          bodyColor: _darkThemeColors.neutralShadeDefaultColor,
        )
        .copyWith(
          // This is defined for the text style of the `SnackBarAction`.
          labelLarge: _appTextStyles.buttonText2,
        ),
    inputDecorationTheme: InputDecorationTheme(
      contentPadding: textFieldContentPadding,
      labelStyle: _appTextStyles.labelText1.copyWith(
        color: _darkThemeColors.neutralShade2Color,
      ),
      floatingLabelStyle: WidgetStateTextStyle.resolveWith((
        Set<WidgetState> states,
      ) {
        late final Color color;
        // Change text color based on the material state of the
        // text field.
        if (states.contains(WidgetState.error)) {
          color = _darkThemeColors.errorTextFieldColor;
        } else if (states.contains(WidgetState.focused)) {
          color = _darkThemeColors.primaryLightColor;
        } else {
          color = _darkThemeColors.neutralShade2Color;
        }
        // Keep this as labelText1 as TextField will
        // decrease the size of the floatingLabelStyle out of the box
        // when focused in proportionate with the
        // unfocused label style.
        // See this on how material scales text based on the labelStyle
        // https://m3.material.io/components/text-fields/specs.
        return _appTextStyles.labelText1.copyWith(color: color);
      }),
      border: UnderlineInputBorder(
        borderSide: BorderSide(color: _darkThemeColors.neutralShade2Color),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: _darkThemeColors.primaryLightColor,
          width: dimenTwo,
        ),
      ),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: _darkThemeColors.neutralShade2Color),
      ),
      errorBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: _darkThemeColors.errorTextFieldColor),
      ),
      focusedErrorBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: _darkThemeColors.errorTextFieldColor,
          width: dimenTwo,
        ),
      ),
      errorStyle: _appTextStyles.labelText3.copyWith(
        color: _darkThemeColors.errorTextFieldColor,
      ),
      errorMaxLines: errorMaxLines,
    ),
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: _darkThemeColors.primaryLightColor,
    ),
    switchTheme: SwitchThemeData(
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return _darkThemeColors.switchActiveTrackColor;
        }
        return _darkThemeColors.switchInactiveTrackColor;
      }),
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return _darkThemeColors.switchActiveThumbColor;
        }
        return _darkThemeColors.switchInactiveThumbColor;
      }),
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return _darkThemeColors.primaryLightColor;
        }
        return _darkThemeColors.neutralShade2Color;
      }),
    ),
  );

  static ABTheme of(BuildContext context) {
    if (context.isDarkMode) {
      return ABDarkTheme();
    } else {
      return ABLightTheme();
    }
  }
}

abstract class ABTheme {
  AppIcons get icons;

  AppTextStyles get appTextStyles => AppTextStyles();

  AppColors get appColors;
}

class ABLightTheme extends ABTheme {
  @override
  LightThemeIcons get icons => LightThemeIcons();

  @override
  AppColors get appColors => LightThemeColors();
}

class ABDarkTheme extends ABTheme {
  @override
  DarkThemeIcons get icons => DarkThemeIcons();

  @override
  AppColors get appColors => DarkThemeColors();
}
