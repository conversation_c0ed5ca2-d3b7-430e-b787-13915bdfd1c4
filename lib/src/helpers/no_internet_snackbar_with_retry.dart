import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/material.dart';

import '../core/logger.dart';

void showNoInternetSnackBarWithRetry(
  BuildContext context,
  VoidCallback onRetry,
) {
  l.d("Showing No Internet Snackbar with retry");
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      // Adding duration for an year here since there is no
      // other way to re-fetch data on the landing screen from the server if the snack-bar
      // is dismissed.
      duration: 365.days,
      backgroundColor: context.appColors.neutralShade9Color,
      content: Text(
        context.localizations.noInternetConnection,
        style: context.appTextStyles.labelText2.copyWith(
          color: context.appColors.neutralShade4Color,
        ),
      ),
      action: SnackBarAction(
        label: context.localizations.retry.toUpperCase(),
        textColor: context.appColors.snackBarActionTextColor,
        onPressed: onRetry,
      ),
      dismissDirection: DismissDirection.none,
    ),
  );
}
