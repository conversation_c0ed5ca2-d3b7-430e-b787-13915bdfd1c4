import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:flutter/cupertino.dart';

class GenderHelper {
  GenderHelper._();

  static String getGenderString(GenderEnum gender, BuildContext context) {
    switch (gender) {
      case GenderEnum.MALE:
        return context.localizations.male;
      case GenderEnum.FEMALE:
        return context.localizations.female;
    }
  }
}
