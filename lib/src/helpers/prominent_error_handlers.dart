import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:flutter/material.dart';

import '../errors/exchange_rate_error.dart';
import 'helpers.dart';

class ProminentErrorHandler {
  ProminentErrorHandler._();

  static BuildContext get _currentContext =>
      NavigationService.navigatorKey.currentContext!;

  static void amountTooHigh(Amount? amount) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText:
          amount != null
              ? _currentContext.localizations.amountTooHighErrorWithAmount(
                amount.localisedFormattedAmount,
              )
              : _currentContext.localizations.amountTooHighError,
    );
  }

  static void amountTooLess(Amount? amount) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText:
          amount != null
              ? _currentContext.localizations.amountTooLessErrorWithAmount(
                amount.localisedFormattedAmount,
              )
              : _currentContext.localizations.amountTooLessError,
    );
  }

  static void periodicRequestTransactionLimitExceeded() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText:
          _currentContext.localizations.periodicRequestTransactionLimitExceeded,
      onOkay: navigateToHomeScreen,
      isDismissible: false,
    );
  }

  static void monetaryTransactionLimitExceededErrorMessage() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText:
          _currentContext.localizations.monetaryTransactionLimitExceeded,
      onOkay: navigateToHomeScreen,
      isDismissible: false,
    );
  }

  static Future<void> agentMonetaryTransactionLimitExceeded() async {
    final currentUserType = await AuthProvider.instance.getSignedInUserType();
    if (_currentContext.mounted) {
      AgencyAppDialog.showErrorDialog(
        context: _currentContext,
        contentText:
            _currentContext.localizations.agentMonetaryTransactionLimitExceeded,
        onOkay:
            currentUserType == UserType.agentManager
                ? navigateToAgentManagerHomeScreen
                : navigateToHomeScreen,
        isDismissible: false,
      );
    }
  }

  static Future<void> agentPeriodicTransactionLimitExceeded() async {
    final currentUserType = await AuthProvider.instance.getSignedInUserType();
    if (_currentContext.mounted) {
      AgencyAppDialog.showErrorDialog(
        context: _currentContext,
        contentText:
            _currentContext.localizations.agentPeriodicTransactionLimitExceeded,
        onOkay:
            currentUserType == UserType.agentManager
                ? navigateToAgentManagerHomeScreen
                : navigateToHomeScreen,
        isDismissible: false,
      );
    }
  }

  static Future<void> receivingAccountWouldCrossLimit() async {
    final currentUserType = await AuthProvider.instance.getSignedInUserType();
    if (_currentContext.mounted) {
      AgencyAppDialog.showErrorDialog(
        context: _currentContext,
        contentText:
            _currentContext.localizations.receivingAccountWouldCrossLimit,
        onOkay:
            currentUserType == UserType.agentManager
                ? navigateToAgentManagerHomeScreen
                : navigateToHomeScreen,
        isDismissible: false,
      );
    }
  }

  static void agentDisabledForSpecificInterval({
    void Function(BuildContext)? onOkay,
  }) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText:
          _currentContext.localizations.agentDisabledForSpecificInterval,
      isDismissible: false,
      onOkay: onOkay ?? navigateToHomeScreen,
    );
  }

  static void unableToPerformExchange() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.unableToPerformExchange,
      isDismissible: false,
      onOkay: navigateToHomeScreen,
    );
  }

  static void exchangeRateNotSupported() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.exchangeRateError,
      isDismissible: false,
      onOkay: (_) => throw ExchangeRateError(),
    );
  }

  static void confirmationTimeout(String featureName) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.confirmationTimeout(
        featureName,
      ),
      isDismissible: false,
      onOkay: navigateToHomeScreen,
    );
  }

  static void couldNotSendOTP() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.couldNotSendOTPError,
      buttonText: _currentContext.localizations.tryAgain,
    );
  }

  static void tooManyOTPRequests({void Function(BuildContext)? onOkay}) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.tooManyOTPRequests,
      isDismissible: false,
      onOkay: onOkay ?? navigateToHomeScreen,
    );
  }

  static void tooManyResendRequests() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.tooManyResendRequests,
      isDismissible: false,
      onOkay: navigateToHomeScreen,
    );
  }

  /// By default tapping [onOkay] will pop the dialog.
  static void inSufficientBalance({
    void Function(BuildContext)? onOkay,
    String? buttonText,
    String? contentText,
    Amount? transactionFee,
  }) {
    assert(
      contentText != null || transactionFee != null,
      "contentText and transactionFee both can't be null",
    );
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText:
          contentText ??
          _currentContext.localizations.inSufficientBalance(
            transactionFee!.localisedFormattedAmount,
          ),
      isDismissible: false,
      buttonText: buttonText ?? _currentContext.localizations.tryAgain,
      onOkay: onOkay,
    );
  }

  static void inactiveSender() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.inactiveSender,
      isDismissible: false,
      onOkay: navigateToHomeScreen,
    );
  }

  static void inactiveRecipient() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.inactiveRecipient,
      isDismissible: false,
      onOkay: navigateToHomeScreen,
    );
  }

  static void otpExpired() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.otpExpired,
      buttonText: _currentContext.localizations.tryAgain,
    );
  }

  /// If [onOkay] is not provided, user is navigated to [HomeScreen] on tap of
  /// the action button when the [numberOfValidationsAttemptsLeft] is 0.
  /// Thus Developer needs to make sure that this dialog is used only on the
  /// flows where we have an authenticated agent.
  static void incorrectOTP({
    required int numberOfValidationsAttemptsLeft,
    required String featureName,
    void Function(BuildContext)? onOkay,
  }) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText:
          numberOfValidationsAttemptsLeft != 0
              ? _currentContext.localizations.incorrectOTP
              : _currentContext.localizations.tooManyOTPSubmission(featureName),
      buttonText:
          numberOfValidationsAttemptsLeft != 0
              ? _currentContext.localizations.tryAgain
              : _currentContext.localizations.okay,
      onOkay:
          numberOfValidationsAttemptsLeft != 0
              ? null
              : onOkay ?? navigateToHomeScreen,
    );
  }

  static void genericError({void Function(BuildContext)? onOkay}) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.somethingWentWrong,
      isDismissible: false,
      onOkay: onOkay,
    );
  }

  static void waitForOTPResend() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.waitForResendOTPErrorMessage,
      isDismissible: true,
    );
  }

  static void userDisabled() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.userDisabled,
      isDismissible: false,
      onOkay: navigateToLandingScreen,
    );
  }

  static void signInTemporarilyBlocked() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.signInTemporarilyBlocked,
      isDismissible: false,
      onOkay: navigateToLandingScreen,
    );
  }

  static void tooManyRequests() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText:
          _currentContext.localizations.tooManyRequestAgentManagerSignInRequest,
      isDismissible: false,
      onOkay: navigateToLandingScreen,
    );
  }

  static void tooManyIncorrectAuthCodeAttempts() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.tooManyIncorrectAttempts,
      isDismissible: false,
      onOkay: navigateToLandingScreen,
    );
  }

  static void requestAmountRefunded(
    void Function(BuildContext context) onOkay,
  ) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.requestAmountAlreadyRefunded,
      onOkay: onOkay,
      isDismissible: false,
    );
  }

  static void phoneNumberUnknown(String phoneNumber) {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.phoneNumberUnknownError(
        phoneNumber,
      ),
    );
  }

  static void invalidShopImage() {
    AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      contentText: _currentContext.localizations.invalidShopImage,
      buttonText: _currentContext.localizations.tryAgain,
    );
  }

  static void userNotAgentManager() async {
    await AgencyAppDialog.showErrorDialog(
      context: _currentContext,
      isDismissible: false,
      contentText: _currentContext.localizations.userNotAgentManager,
    );
  }
}
