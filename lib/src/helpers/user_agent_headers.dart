import 'dart:developer';
import 'dart:io';

import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/utils/constants.dart';
import 'package:dedwig/dedwig.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

Headers? userAgentHeaders(
  Request request,
  PackageInfo packageInfo,
  BaseDeviceInfo deviceInfo,
) {
  final String platform = Platform.operatingSystem.toUpperCase();
  const userAgentHeaderKey = 'user-agent';
  if (Platform.isAndroid) {
    final androidDeviceInfo = deviceInfo as AndroidDeviceInfo;
    final osVersion = androidDeviceInfo.version.release;
    final manufacturer = androidDeviceInfo.manufacturer;
    final model = androidDeviceInfo.model;
    final heightPx =
        WidgetsBinding
            .instance
            .platformDispatcher
            .views
            .first
            .physicalSize
            .height
            .toInt();
    final widthPx =
        WidgetsBinding
            .instance
            .platformDispatcher
            .views
            .first
            .physicalSize
            .width
            .toInt();
    final screenResolution = "$widthPx,$heightPx";
    return Headers(
      headers: [
        Header(
          name: userAgentHeaderKey,
          value: _getFormattedUserAgent(
            platform,
            osVersion,
            manufacturer,
            model,
            screenResolution,
            packageInfo,
          ),
        ),
      ],
    );
  } else if (Platform.isIOS) {
    final iosDeviceInfo = deviceInfo as IosDeviceInfo;
    log("iOS Device Info $iosDeviceInfo");
    final osVersion = iosDeviceInfo.systemVersion;
    const manufacturer = "apple";
    final model = iosDeviceInfo.utsname.machine;
    // Since Device info for iOS does not have physical size,
    // using dart UI to get the physical size.
    final heightPx =
        WidgetsBinding
            .instance
            .platformDispatcher
            .views
            .first
            .physicalSize
            .height
            .toInt();
    final widthPx =
        WidgetsBinding
            .instance
            .platformDispatcher
            .views
            .first
            .physicalSize
            .width
            .toInt();
    final screenResolution = "$widthPx,$heightPx";
    return Headers(
      headers: [
        Header(
          name: userAgentHeaderKey,
          value: _getFormattedUserAgent(
            platform,
            osVersion,
            manufacturer,
            model,
            screenResolution,
            packageInfo,
          ),
        ),
      ],
    );
  } else {
    throw DeveloperError("$platform is not supported");
  }
}

String _getFormattedUserAgent(
  String platform,
  String osVersion,
  String manufacturer,
  String model,
  String screenResolution,
  PackageInfo packageInfo,
) {
  final appVersion = packageInfo.version;
  final appVersionCode = packageInfo.buildNumber;
  final environment =
      const String.fromEnvironment("flavor", defaultValue: "dev").toLowerCase();
  late final String buildType;
  if (kReleaseMode) {
    buildType = "release";
  } else if (kDebugMode) {
    buildType = "debug";
  } else if (kProfileMode) {
    buildType = "profile";
  } else {
    throw DeveloperError("Current Build Type not supported");
  }

  final osInformation = "$platform/$osVersion";
  final deviceInformation = "$manufacturer/$model/$screenResolution";
  final appInformation =
      "$appName/$appVersion($appVersionCode)/$environment/$buildType";
  return "$osInformation $deviceInformation $appInformation";
}
