class FormattedDurationLeftHelper {
  FormattedDurationLeftHelper._();

  static String getFormattedDurationLeftString(DateTime nextResendAt) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    final Duration difference = nextResendAt.difference(DateTime.now());
    final int days = difference.inDays;
    final int hours = difference.inHours;
    final String twoDigitMinutes = twoDigits(
      difference.inMinutes.remainder(60),
    );
    final String twoDigitSeconds = twoDigits(
      difference.inSeconds.remainder(60),
    );

    if (days > 0) {
      return "$days days";
    }

    if (hours > 0) {
      final twoDigitHours = twoDigits(hours.remainder(24));
      return "$twoDigitHours:$twoDigitMinutes:$twoDigitSeconds";
    }
    return "$twoDigitMinutes:$twoDigitSeconds";
  }
}
