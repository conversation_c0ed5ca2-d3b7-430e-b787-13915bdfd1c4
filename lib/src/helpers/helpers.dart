import 'package:bcn_agency_banking_flutter/src/core/auth/app_pin_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/auth/auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/errors/developer_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_manager_home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/sign_out/sign_out_repository.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geocoding/geocoding.dart';
import 'package:url_launcher/url_launcher.dart';

import '../core/logger.dart';
import '../exceptions/url_destination_missing.dart';
import '../features/agent/home_screen.dart';
import '../features/landing_screen/landing_screen.dart';
import '../models/location_details.dart';
import '../widgets/nested_navigator_observer.dart';

void navigateToHomeScreen(BuildContext context) {
  l.d("Navigating to Home Screen");
  context.rootNavigator.pushNamedAndRemoveUntil(
    HomeScreen.id,
    (route) => false,
  );
}

void navigateToLandingScreen(BuildContext context) {
  l.d("Navigating to Landing Screen");
  context.rootNavigator.pushNamedAndRemoveUntil(
    LandingScreen.id,
    (route) => false,
  );
}

void navigateToAgentManagerHomeScreen(BuildContext context) => context
    .rootNavigator
    .pushNamedAndRemoveUntil(AgentManagerHomeScreen.id, (route) => false);

void dismissKeyboard() => FocusManager.instance.primaryFocus?.unfocus();

/// For Nested Navigation Routes.
/// This will return boolean whether the current route is the initialRoute in the
/// Navigator.
bool isCurrentRouteInitialRoute(
  NestedNavigatorObserver nestedNavigatorObserver,
  String initialRoute,
) {
  return nestedNavigatorObserver.top?.settings.name == initialRoute;
}

Future<void> signOutUser(BuildContext context) async {
  l.d("Sign out user");
  final UserType? currentUserType =
      await AuthProvider.instance.getSignedInUserType();
  if (currentUserType == null) {
    // CurrentUserType should not be null when the  user is logged in.
    throw DeveloperError("User type cannot be null.");
  } else {
    final llt = await AuthProvider.instance.getLLT();
    final rpcResult = await SignOutRepository(llt: llt).signOutUser();
    l.d("SignOut RPC result: $rpcResult");
    rpcResult.when(
      response: (_) async {
        await clearOfflineData();
        if (context.mounted) navigateToLandingScreen(context);
      },
      error: (_) {
        // This cannot happen as the error type for this RPC is Never.
        throw DeveloperError("Something went wrong.");
      },
    );
  }
}

Future<void> forceSignOutUser(
  BuildContext context, {
  bool showSpinnerDialog = true,
}) async {
  l.d("Force Signing out user");
  final UserType? currentUserType =
      await AuthProvider.instance.getSignedInUserType();
  if (currentUserType == null) {
    // CurrentUserType should not be null when the  user is logged in.
    l.e("Current User type is null");
    throw DeveloperError("User type cannot be null.");
  } else {
    try {
      if (context.mounted && showSpinnerDialog) {
        AgencyAppDialog.showSpinnerDialog(context);
      }
      await SignOutRepository(
        llt: await AuthProvider.instance.getLLT(),
      ).signOutUser();
      await clearOfflineData();
      l.d("Signed Out");
      if (context.mounted) navigateToLandingScreen(context);
    } catch (_) {
      await clearOfflineData();
      if (context.mounted) navigateToLandingScreen(context);
    }
  }
}

Future<void> clearOfflineData() async {
  l.d("Clearing offline data");
  await AuthProvider.instance.clearCredentials();
  await AppPinProvider.deleteRemainingAppPinAttempts();
  await AppPinProvider.deleteAppPin();
}

/// Attempts to open `destinationUrl`.
///
/// Throws `UrlDestinationMissingException` if opening `destinationUrl`
/// failed as a result of a missing destination for that url.
Future<void> launchExternalUrl(String destinationUrl) async {
  final Uri url = Uri.parse(destinationUrl);

  // Thanks: https://stackoverflow.com/a/66776776
  late bool didUrlOpen;

  // Thanks: https://stackoverflow.com/a/32627841
  try {
    didUrlOpen = await launchUrl(url, mode: LaunchMode.externalApplication);
  } on PlatformException catch (platformException) {
    // PlatformException is thrown when an error with the OS results
    // in this URL not being opened.
    // Currently the only case we know and care about is the one where Android
    // doesn't have an Activity to open a URL.
    // This doesn't happen on iOS since Safari can't be deleted as of this writing.
    // Therefore, we check if the PlatformException indicates a missing activity
    // error and tell the caller as much using `UrlDestinationMissingException`.
    //
    // We still don't know what *other* reasons there might be that result in a
    // PlatformException so we still crash with a DeveloperError there.
    if (_doesPlatformExceptionIndicateMissingActivity(platformException)) {
      throw UrlDestinationMissingException();
    } else {
      throw DeveloperError(
        'Unknown `PlatformException` encountered when opening URL: `$url`',
      );
    }
  }
  if (!didUrlOpen) {
    throw DeveloperError('Could not launch $url');
  }
}

/// A helper function to calculate the difference between two given dates.
///
/// [date1] should always be greater than [date2] otherwise a [DeveloperError]
/// is thrown.
int differenceInDays(DateTime date1, DateTime date2) {
  date2 = date2.toLocal().onlyDate;
  date1 = date1.onlyDate;
  final int difference = date1.difference(date2).inDays;
  if (difference < 0) {
    throw DeveloperError("date1 cannot be a past date");
  }
  return difference;
}

String getDaysLeftFromTodayString(BuildContext context, DateTime transferDate) {
  final int daysLeft = differenceInDays(transferDate, DateTime.now());
  if (daysLeft == 0) {
    return "${context.localizations.today.toLowerCase()}.";
  } else if (daysLeft == 1) {
    return "${context.localizations.inText} $daysLeft ${context.localizations.dayText}.";
  } else {
    return "${context.localizations.inText} $daysLeft ${context.localizations.daysText}.";
  }
}

bool _doesPlatformExceptionIndicateMissingActivity(
  PlatformException platformException,
) => platformException.code == _missingActivityErrorCode;

const _missingActivityErrorCode = "ACTIVITY_NOT_FOUND";

/// Sets the status bar color to [Colors.transparent].
/// Meaning this set the status bar color as per body
/// or the app bar whatever is near to the status bar.
void setStatusBarColorToTransparent([BuildContext? context]) {
  final currentContext =
      context ?? NavigationService.navigatorKey.currentContext!;

  // Set the status bar color of the app
  // as per current theme's primary default color.
  final currentStyle = (currentContext.isDarkMode
          ? SystemUiOverlayStyle.dark
          : SystemUiOverlayStyle.light)
      .copyWith(statusBarColor: Colors.transparent);
  SystemChrome.setSystemUIOverlayStyle(currentStyle);
}

// Google maps related.
/// Gets a [FormattedAddress] for a given coordinate.
Future<FormattedAddress?> getReverseGeocode(
  double latitude,
  double longitude,
) async {
  try {
    final List<Placemark> placeMarks = await placemarkFromCoordinates(
      latitude,
      longitude,
    );
    return FormattedAddress(
      addressLabel: "${placeMarks.first.name}, ${placeMarks.first.subLocality}",
      addressText:
          "${placeMarks.first.subAdministrativeArea}, ${placeMarks.first.locality}",
    );
  } on PlatformException catch (exception) {
    // The PlatformException has `exception.code` equal to `NOT_FOUND` when
    // the address is not found for the given coordinates.
    // The error code is `IO_ERROR` when there is no internet connection or
    // when the rate limit is exceeded.
    // Please check https://pub.dev/packages/geocoding.
    // The documentation for Geocoding plugin
    // https://pub.dev/documentation/geocoding/latest/, does not provide any
    // info on other possible exceptions.
    // The exception is logged as a warning irrespective of
    // the error code so that Firebase picks it up if there is a crash for this user.
    // However we need not crash the app on this error or stop the flow
    // since the Address string is just a good-to-have in the UI.
    FirebaseCrashlytics.instance.log(
      "Unable to fetch address string for selected location. Exception: ${exception.code}. Message: ${exception.message}",
    );
    return null;
  }
}
