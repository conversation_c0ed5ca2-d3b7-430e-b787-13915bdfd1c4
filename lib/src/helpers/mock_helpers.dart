// This file will contain all the constants and helpers needed for creating RPC Mocks

import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_rpc.dart';

/// This Method will only be used for Mock Impls.
/// While testing, instead of writing both errors and response separately
/// just declare them in [response] and [error]
/// [shouldThrowError] should be toggled to test for the response and error.
LeoRPCResult<T, R> getLeoRPCResult<
  T extends LeoRPCResponse,
  R extends LeoRPCError
>({required bool shouldThrowError, required T response, required R error}) {
  if (shouldThrowError) {
    return LeoRPCResult<T, R>.error(error);
  } else {
    return LeoRPCResult<T, R>.response(response);
  }
}

MultiResolutionBitmapImage get mockImage {
  const imageUrl =
      "https://expertphotography.b-cdn.net/wp-content/uploads/2020/08/social-media-profile-photos-3.jpg";
  final remoteBitmapImage = RemoteBitmapImage(
    imageURL: Uri.parse(imageUrl),
    imageType: BitmapImageTypeEnum.JPG,
    width: 40,
    height: 40,
  );
  return MultiResolutionBitmapImage(
    mdpi: remoteBitmapImage,
    xhdpi: remoteBitmapImage,
    xxhdpi: remoteBitmapImage,
    xxxhdpi: remoteBitmapImage,
  );
}

class OTPConstants {
  OTPConstants._();

  static OTPValidityDetails get otpValidityDetails {
    final now = DateTime.now();
    return OTPValidityDetails(
      nextResendAt: now.add(10.seconds),
      expiresAt: now.add(1.minutes),
    );
  }

  static OTPResendDetails get resendDetails {
    return OTPResendDetails(
      validityDetails: otpValidityDetails,
      numberOfResendsLeft: 2,
    );
  }
}

/// Check Whether the entered otp is right or wrong.
/// This is only for the mock RPC calls.
bool checkWhetherOTPValid(Otp otp) {
  const validOTP = "123456";
  return otp.otp == validOTP;
}
