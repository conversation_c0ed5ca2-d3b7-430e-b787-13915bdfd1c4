import 'dart:async';

import 'package:bcn_agency_banking_flutter/src/core/navigation_service.dart';
import 'package:bcn_agency_banking_flutter/src/errors/session_key_not_found_error.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/application_status/agent_application_status_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_manager_home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/landing_screen/landing_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/helpers.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/prominent_error_handlers.dart';
import 'package:bcn_agency_banking_flutter/src/widgets/dialogs.dart';
import 'package:dedwig/dedwig.dart';
import 'package:flutter/material.dart';
import 'package:store_redirect/store_redirect.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../core/auth/auth_provider.dart';
import '../core/logger.dart';
import '../errors/developer_error.dart';
import '../features/agent/home_screen.dart';
import '../utils/extensions.dart';

/// A mixin which has error handler.
mixin RPCHandler {
  /// Wrap your RPC call with [rpcHandler].
  ///
  /// provide RPC request inside [executor].
  ///
  /// [context] is not necessary as it will be taken from [MaterialApp.navigatorKey].
  /// Although, you can provide your own [context] as per your use case.
  ///
  /// [onTransientError] will be called when there's any network or slow connection related issues.
  /// Catching Network error and showing it in the snackBar will automatically be handled by this wrapper.
  ///
  /// [shouldHandleTransientError] decides whether the [rpcHandler] should handle the
  /// transient error itself. By Default, it is true.
  Future<void> rpcHandler(
    Future<void> Function() executor, {
    required void Function(String) onTransientError,
    bool shouldHandleTransientError = true,
    VoidCallback? onServerError,
    BuildContext? context,
  }) async {
    // If context is not provided take it from navigatorKey.
    final BuildContext currentContext =
        context ?? NavigationService.navigatorKey.currentContext!;
    l.v("Current Context in RPC Handler $currentContext");
    try {
      l.d("Running Executor in RPC Handler");
      return await executor();
    } on NetworkException catch (_) {
      l.d("Handling Transient Error");
      _handleTransientError(
        shouldHandleTransientError,
        currentContext,
        onTransientError,
      );
    } on LeoInvalidLLTException catch (_) {
      await _forciblySignUserOut(currentContext);
    } on LeoInvalidSLTException catch (_) {
      // This should never happen since on this exception
      // a retry is made to refresh the SLT which would give us
      // (at worst) a refresh LLT.
      // Therefore, seeing this exception is a developer error.
      rethrow;
    } on LeoUnauthenticatedException catch (_) {
      rethrow;
    } on LeoUnauthorizedException catch (_) {
      await _forciblySignUserOut(currentContext);
    } on LeoUnsupportedClientException catch (_) {
      _handleUnsupportedClientException(currentContext, onServerError);
    } on LeoUserDisabledException catch (_) {
      await _forciblySignUserOut(currentContext);
    } on LeoInvalidWTException catch (_) {
      // This is not applicable for agency banking.
      // Thus is a developer error.
      rethrow;
    } on LeoServerException catch (_) {
      // LeoServerException is thrown on status codes 429, 500, 502, 503.
      _handleServerException(currentContext, onServerError);
    } on LeoIllegalStateException catch (_) {
      _handleServerException(currentContext, onServerError);
    } on TimeoutException catch (_) {
      _handleTransientError(
        shouldHandleTransientError,
        currentContext,
        onTransientError,
      );
    } on SessionKeyNotFoundError catch (_) {
      // If session key is not found, the user should be signed out immediately.
      await _forciblySignUserOut(currentContext);
    }
  }

  Future<void> _forciblySignUserOut(BuildContext context) async {
    // If the app is already signed out, just return.
    if ((await AuthProvider.instance.getSignedInUserType()) != null) {
      if (context.mounted) {
        navigateToLandingScreen(context);
        clearOfflineData();
        final currentContext = NavigationService.navigatorKey.currentContext;
        if (currentContext != null) {
          AgencyAppDialog.showErrorDialog(
            context: currentContext,
            contentText: context.localizations.sessionExpired,
          );
        }
      }
    }
  }

  void _showNoInternetSnackBar(BuildContext context) {
    l.d("Show No Internet Snack bar");
    final snackBar = SnackBar(
      backgroundColor: context.appColors.neutralShade9Color,
      content: Text(
        context.localizations.noInternetConnection,
        style: context.appTextStyles.labelText2.copyWith(
          color: context.appColors.neutralShade4Color,
        ),
      ),
    );

    // Clear all the snack bars present in the queue and exit the current
    // one with normal animation before showing the new one.
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  void _handleTransientError(
    bool shouldHandleTransientError,
    BuildContext currentContext,
    void Function(String) onTransientError,
  ) {
    if (shouldHandleTransientError) {
      l.d("Handling Default transient Error");
      _handleDefaultTransientError(currentContext);
    }
    l.d("Calling onTransientError callback in RPC handler");
    onTransientError(currentContext.localizations.noInternetConnection);
  }

  void _handleDefaultTransientError(BuildContext currentContext) {
    _showNoInternetSnackBar(currentContext);
  }

  void _handleUnsupportedClientException(
    BuildContext context,
    VoidCallback? onServerError,
  ) {
    // TODO: https://bcnsurya.atlassian.net/browse/AGB-690
    AgencyAppDialog.showErrorDialog(
      context: context,
      contentText: context.localizations.updateApp,
      isDismissible: false,
      onOkay: (dialogContext) async {
        StoreRedirect.redirect();
      },
    );
  }

  void _handleServerException(
    BuildContext context,
    VoidCallback? onServerError,
  ) {
    l.d("Handling Server Exception");
    final bool isRootScreen = _isCurrentRouteRootScreen();
    // It's expected that root screens have their own state of
    // server error and can handle server error by themselves.
    l.d("IsCurrentRouteRootScreen $isRootScreen");
    if (isRootScreen) {
      onServerError?.call();
    } else {
      ProminentErrorHandler.genericError(
        onOkay: (dialogContext) async {
          await _navigateToRootScreen(dialogContext, onServerError);
        },
      );
    }
  }

  Future<void> _navigateToRootScreen(
    BuildContext context,
    VoidCallback? onServerError,
  ) async {
    l.d("Navigating to the Root Screen");
    // Go to the respective root screen.
    final currentUserType = await AuthProvider.instance.getSignedInUserType();
    if (context.mounted) {
      switch (currentUserType) {
        case UserType.agent:
          navigateToHomeScreen(context);
          break;
        case UserType.agentManager:
          navigateToAgentManagerHomeScreen(context);
          break;
        case null:
          navigateToLandingScreen(context);
          break;
      }
    }
  }
}

bool _isCurrentRouteRootScreen() {
  // Get the nearest page or screen route
  // from the top of the navigation stack.
  // We might get unwanted `DialogRoute` in the Navigation stack
  // that does not give information on which screen route we are.
  // We need screen route to further navigate the user to a particular screen.
  final String? currentScreenRoute = _getTopMostMaterialPageRoute();
  l.v("Current Screen Route $currentScreenRoute");

  if (currentScreenRoute == null) {
    // Throwing this error, since we will call the RPC handler only under some route
    // So this can never be null.
    l.e("Current Screen Route is null");
    throw DeveloperError("Current Route can never be null");
  }

  // Whether the current top route is the root screen.
  // For e.g. `HomeScreen`, `LandingScreen`, `AgentManagerHomeScreen`
  // or `AgentApplicationStatusScreen`.
  final bool isRootScreen =
      currentScreenRoute == HomeScreen.id ||
      currentScreenRoute == LandingScreen.id ||
      currentScreenRoute == AgentApplicationStatusScreen.id ||
      currentScreenRoute == AgentManagerHomeScreen.id;
  return isRootScreen;
}

/// Get the most bottom `MaterialPageRoute` in
/// the navigation stack.
String? _getTopMostMaterialPageRoute() {
  final navigationHistory = NavigationHistoryObserver().history;
  l.v("Navigation History $navigationHistory");
  for (int i = navigationHistory.length - 1; i >= 0; i--) {
    final route = navigationHistory[i];
    if (route is MaterialPageRoute) {
      return route.settings.name;
    }
  }
  return null;
}
