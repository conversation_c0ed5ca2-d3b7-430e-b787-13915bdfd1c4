import 'package:bcn_agency_banking_flutter/src/core/location_services/location_service.dart';
import 'package:bcn_agency_banking_flutter/src/core/service_locator.dart';
import 'package:dedwig/dedwig.dart';
import 'package:bcn_agency_banking_flutter/src/models/location_details.dart';

Headers? locationHeadersGenerator(Request request) {
  final LocationService locationService = locator<LocationService>();
  final LocationDetails? currentLocation = locationService.getCurrentLocation;
  if (currentLocation == null) return null;
  return Headers(
    headers: [
      Header(name: "AB-Lat", value: "${currentLocation.latitude}"),
      Header(name: "AB-Long", value: "${currentLocation.longitude}"),
      Header(
        name: "AB-Location-Accuracy-Meters",
        value: "${currentLocation.accuracy}",
      ),
    ],
  );
}
