import 'dart:async';
import 'package:agency_banking_rpcs/agency_banking_rpcs.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/mock_helpers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:leo_dart_runtime/leo_dart_runtime.dart';

class MockGetBCNUserDocumentURLRPCImpl extends GetBCNUserDocumentURLRPC {
  @override
  Future<
    LeoRPCResult<GetBCNUserDocumentURLResponse, GetBCNUserDocumentURLError>
  >
  execute(GetBCNUserDocumentURLRequest request) {
    final GetBCNUserDocumentURLResponse
    response = GetBCNUserDocumentURLResponse(
      documentURL: Uri.parse(
        "https://expertphotography.b-cdn.net/wp-content/uploads/2020/08/social-media-profile-photos-3.jpg",
      ),
    );
    final GetBCNUserDocumentURLError error =
        GetBCNUserDocumentURLError.GetBCNUserDocumentURLErrorInvalidAgentApplicationIdError(
          errorCode: "",
        );
    final LeoRPCResult<
      GetBCNUserDocumentURLResponse,
      GetBCNUserDocumentURLError
    >
    result = getLeoRPCResult<
      GetBCNUserDocumentURLResponse,
      GetBCNUserDocumentURLError
    >(response: response, error: error, shouldThrowError: false);

    return Future.delayed(2.seconds, () => result);
  }
}
