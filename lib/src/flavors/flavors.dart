abstract class AppFlavor {
  String get baseUrl;
}

class DevFlavor implements AppFlavor {
  @override
  String get baseUrl => "https://dev.resoluttech.link/";
}

class UatFlavor implements AppFlavor {
  @override
  String get baseUrl => "https://uat.resoluttech.link/";
}

class ProdFlavor implements AppFlavor {
  @override
  String get baseUrl => "https://resoluttech.link/";
}

class MockFlavor implements AppFlavor {
  @override
  String get baseUrl => "https://dev.resoluttech.link/";
}

AppFlavor get currentFlavor {
  final flavorString =
      const String.fromEnvironment("flavor", defaultValue: "dev").toLowerCase();
  switch (flavorString) {
    case "dev":
      return DevFlavor();
    case "prod":
      return ProdFlavor();
    case "uat":
      return UatFlavor();
    case "mock":
      return MockFlavor();
    default:
      return DevFlavor();
  }
}
