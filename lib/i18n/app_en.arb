{"appTitle": "Yafika Agency Banking", "exactTime12HrsFormat": "dd, MMM yyyy 'at' hh:mm aaa", "exactTime24HrsFormat": "dd, MMM yyyy 'at' HH:mm", "transactionDateTime12HrsFormat": "hh:mm aaa 'on' dd, MMM yyyy", "transactionDateTime24HrsFormat": "HH:mm 'on' dd, MMM yyyy", "transactionFee": "Transaction Fee", "customer": "Customer", "recipientNationalIdNo": "Recipient’s National ID No.", "sendAmount": "Send Amount", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "transactionDetails": "Transaction Details:", "customerCredit": "Customer's Credit", "customerDebit": "Customer's Debit", "recipientReceives": "Recipient Receives", "proceed": "Proceed", "cancel": "Cancel", "approveRequestConfirmation": "Please confirm that you want to approve this request.", "rejectRequestConfirmation": "Please confirm that you want to reject this request.", "enterComment": "Enter Comment", "okay": "OK", "confirm": "Confirm", "enterAmount": "Enter Amount", "enterMobileNumber": "Enter Mobile Number", "searchCountryCode": "Search Country Code", "password": "Password", "appBarTitle": "Hello, {name}", "@appBarTitle": {"placeholders": {"name": {"type": "String"}}}, "signOut": "Sign Out", "enterOTP": "Enter OTP", "otpValidTill": "• OTP valid till {time}.", "@otpValidTill": {"placeholders": {"time": {"type": "String"}}}, "resendOTP": "Resend OTP", "inText": "in", "sessionExpired": "Your session has expired. Please sign in again to continue.", "amountTooHighErrorWithAmount": "Entered amount is too high, please enter an amount lesser than {formattedAmount}.", "@amountTooHighErrorWithAmount": {"description": "Error Message in cash transactions, when the entered amount is too High.", "placeholders": {"formattedAmount": {"type": "String"}}}, "amountTooLessErrorWithAmount": "Entered amount is too less, please enter an amount higher than {formattedAmount}.", "@amountTooLessErrorWithAmount": {"description": "Error Message in cash transactions, when the entered amount is too less.", "placeholders": {"formattedAmount": {"type": "String"}}}, "amountTooHighError": "Entered amount is too high.", "amountTooLessError": "Entered amount is too less.", "periodicRequestTransactionLimitExceeded": "This transaction cannot be processed because the recipient's transaction limit has been exceeded.", "monetaryTransactionLimitExceeded": "This transaction cannot be processed because the monetary sum of transactions for the customer is greater than allowed.", "agentPeriodicTransactionLimitExceeded": "This transaction cannot be processed because your transaction limit has been exceeded.", "agentMonetaryTransactionLimitExceeded": "This transaction cannot be processed because the monetary sum of transactions for you is greater than allowed.", "receivingAccountWouldCrossLimit": "This transaction cannot be processed because the recipient account's limit would be crossed.", "agentDisabledForSpecificInterval": "Your ID has been disabled temporarily for misusing the OTP feature. Please try again later.", "unableToPerformExchange": "Sorry we are unable to process your request as processing the exchange rate failed.", "transactionSuccessful": "Transaction Successful", "contactSupport": "Contact Support", "done": "Done", "transactionIdString": "Transaction ID", "copiedToClipboardToastMessage": "Copied to Clipboard!", "applications": "Applications", "depositToOwnWallet": "Deposit to own wallet", "depositToOwnWalletTitle": "Deposit to Own Wallet", "noApplicationsTitle": "No New Agent Applications", "noApplicationsMessage": "New applications will appear here.", "confirmSignOut": "Are you sure you want to sign out?", "noUnblockedRefundRequestsTitle": "No New Unblock Refund Requests Yet", "noUnblockedRefundRequestsSubtitle": "New unblock refund requests will appear here.", "requestId": "Request ID - {requestIdentifier}", "@requestId": {"description": "Request Id of blocked refund request.", "placeholders": {"requestIdentifier": {"type": "String"}}}, "agentDetails": "Agent Details", "blockedOn": "Blocked on {dateTime}", "@blockedOn": {"description": "Formatted date and time of transaction block event", "placeholders": {"dateTime": {"type": "String"}}}, "senderRequest": "Sender Request", "requestDetails": "Request Details", "requestIdHeader": "Request ID", "amountTransferred": "Amount Transferred", "createdOnHeader": "Created On", "blockedOnHeader": "Blocked On", "agentsName": "Agent's Name", "agentsMobileNumber": "Agent's Mobile Number", "unblockRefund": "UNBLOCK REFUND", "locationServiceNeeded": "Location service is needed to use Yafika Agency Banking app.", "locationPermissionNeeded": "Location permission is needed to use Yafika Agency Banking app.", "openSettings": "OPEN SETTINGS", "agentRequest": "Agent Request", "next": "NEXT", "reject": "REJECT", "continueText": "Continue", "phoneNumberUnknownError": "Couldn’t find Yafika Mobile User with this number {number}.", "@phoneNumberUnknownError": {"placeholders": {"number": {"type": "String"}}}, "change": "Change", "saveChanges": "Save Changes", "shopName": "Shop Name", "editShopDetails": "Edit Shop Details", "shopDetails": "Shop Details", "edit": "Edit", "shopImageNotUploaded": "No shop photo was uploaded", "geocodingErrorMessage": "Unable to fetch address for your current marker location.", "searchLocationPlaceHolder": "Search Location", "inSufficientBalance": "This transaction cannot happen because of insufficient balance in your wallet. Please note that {transactionFee} will be added as transaction fee.", "@inSufficientBalance": {"placeholders": {"transactionFee": {"type": "String"}}}, "customerInsufficientBalance": "This transaction cannot happen because of insufficient balance in customer's wallet. Please note that {transactionFee} will be added as transaction fee.", "@customerInsufficientBalance": {"placeholders": {"transactionFee": {"type": "String"}}}, "cashInRecipientCannotBeSelf": "Cash In request cannot be created with yourself. Please try with a different number.", "cashInRecipientCannotBeAgent": "Cash In request cannot be created with an Agent. Please try with a different number.", "lookingUpDetailsPhoneNumber": "Looking up details for {number}.", "@lookingUpDetailsPhoneNumber": {"placeholders": {"number": {"type": "String"}}}, "distanceLimitMessage": "You need to be within {allowedDistance}m radius to confirm this agent’s location.", "@distanceLimitMessage": {"description": "It mentions that the Agent is not within the allowedDistance", "placeholders": {"allowedDistance": {"type": "int"}}}, "cancellationConfirmationMessage": "Are you sure you want to close the {processName} process?", "@cancellationConfirmationMessage": {"placeholders": {"processName": {"type": "String"}}}, "needHelp": "Need Help?", "callUs": "Call Us", "sendEmail": "Send an Email", "biometricAuthPrompt": "Biometric authentication required to access the app.", "withdrawFromOwnWallet": "Withdraw from own wallet", "withdrawFromOwnWalletTitle": "Withdraw from Own Wallet", "cashOutRecipientCannotBeSelf": "Withdraw request cannot be created with yourself. Please try with a different number.", "cashOutRecipientCannotBeAgent": "Withdraw request cannot be created with an Agent. Please try with a different number.", "senderAccountDeactivated": "Sender account is deactivated.", "agentAccountWouldCrossLimit": "Agent account would cross limit.", "noInternetConnection": "No internet connection, check your internet connection and try again", "pleaseTryAgainLater": "Please try again later.", "deletePhotoConfirmationMessage": "Are you sure you want to delete this photo?", "shopPhoto": "Shop Photo", "attemptTransfer": "Attempt Transfer", "confirmationTimeout": "This transaction has timed out. Please create a new {featureName} request.", "@confirmationTimeout": {"description": "Error message to be shown when a particular transaction has timed out.", "placeholders": {"featureName": {"type": "String"}}}, "couldNotSendOTPError": "We were unable to reach our servers. Please try again after some time.", "tooManyOTPRequests": "You have attempted too many times to make a transaction for this customer. Please try again later.", "tooManyResendRequests": "You tried to resend OTP too many times, please try again later.", "otpExpired": "OTP expired, please resend the OTP and try again.", "inactiveSender": "This transaction cannot be processed because the sender's account is deactivated.", "cashOut": "Cash Out", "withdrawAmount": "Withdraw Amount", "incorrectOTP": "Entered OTP is invalid. Please try again.", "cashIn": "Cash In", "inactiveRecipient": "This transaction cannot be processed because the recipient's account is deactivated.", "tooManyOTPSubmission": "You have attempted to submit OTP too many times. Please create a new {featureName} request.", "@tooManyOTPSubmission": {"description": "When user tries to submit OTP more than allowed times, this error will be shown.", "placeholders": {"featureName": {"type": "String"}}}, "refundAmount": "Refund Amount", "unblockRefunds": "Unblock Refunds", "sendMoney": "Send money", "createRequest": "Create Request", "acceptRequest": "Accept Request", "activeRequests": "Active Requests", "customerRefunds": "Customer Refunds", "locationRadiusCheck": "You need to be within {allowedDistance}m to your shop to go further.", "@locationRadiusCheck": {"description": "Error message which will be shown when user is trying to do some Cash transaction far away from allowed distance", "placeholders": {"allowedDistance": {"type": "int"}}}, "recipientsMobileNumber": "Recipient’s Mobile Number", "senderAndRecipientSame": "Sender's and recipient's phone numbers cannot be the same.", "incorrectNationalOrRequestId": "Incorrect national ID or request ID.\nPlease try again.", "refundFlowWasInitiatedErrorMessage": "You can not claim the amount for this request ID anymore.", "recipientNationalIdNumber": "Recipient's National ID Number", "senderNameLabelText": "Sender's Name", "noActiveRequestsYet": "No Active Requests Yet", "activeRequestsEmptyStateLabelText": "You will be able to view list of money transfer requests here.", "activeRequestsRequestCardTitle": "Amount - {transactionAmount}", "@activeRequestsRequestCardTitle": {"placeholders": {"transactionAmount": {"type": "String"}}}, "createdOnTimestamp": "Created on {timestamp}", "@createdOnTimestamp": {"placeholders": {"timestamp": {"type": "String"}}}, "senderOrRecipientCannotBeAgent": "Sender or recipient cannot be an agent.", "agentInformation": "Agent Information", "personalDetails": "Personal Details", "fullName": "Full Name", "otherName": "Other Name", "dateOfBirth": "Date of Birth", "nationalIdNumber": "National ID Number", "gender": "Gender", "male": "Male", "female": "Female", "agentSignature": "Agent Signature", "signaturePhoto": "Signature Photo", "approveApplication": "APPROVE APPLICATION", "signatureImageNotUploaded": "Signature photo was not uploaded", "confirmApproveRequest": "Please confirm that you want to approve this request. ", "confirmRejectRequest": "Please confirm that you want to reject this request. ", "somethingWentWrong": "Something Went Wrong. Please Try Again.", "inactiveUser": "User is inactive.", "applicationAlreadyRejected": "Application already rejected.", "applicationAlreadyApproved": "Application already approved.", "requestRejected": "{name}’s application is rejected.", "@requestRejected": {"placeholders": {"name": {"type": "String"}}}, "requestApproved": "{name}’s application is approved.", "@requestApproved": {"placeholders": {"name": {"type": "String"}}}, "senderVerification": "Sender Verification", "enterSendersOTP": "Enter Sender’s OTP", "recipientDetails": "Recipient Details", "recipientsNationalIDNumber": "Recipient’s National ID Number", "sendersNationalIDNumber": "Sender’s National ID Number", "sendersAddress": "Sender’s Address", "sendersPhoto": "Sender's photo", "sendersPhotoTitle": "Sender's Photo", "captureAPhotoOfSender": "Capture a Photo of Sender", "couldNotSendRequestIdErrorMessage": "We could not request ID to the recipient. Try again later, or contact customer support.", "identificationIsRequired": "Identification is required to bypass session P<PERSON> using the biometrics.", "sessionPinDoesNotMatchWithConfirmPin": "The session PIN you entered doesn’t match, please try again.", "setupSessionPin": "Setup Session PIN", "confirmSessionPin": "Confirm Session PIN", "bypassPinUsingBiometrics": "Bypass PIN entry using biometrics", "tooManyAppPinAttempts": "You have tried to enter the wrong session <PERSON><PERSON> too many times. You have been signed out of the app.", "wrongPinEntered": "You have entered wrong PIN.", "appPinAttemptsLeft": "Only {numberOfAttemptsLeft} attempts left!", "@appPinAttemptsLeft": {"placeholders": {"numberOfAttemptsLeft": {"type": "int"}}}, "appPinAttemptLeft": "Only {numberOfAttemptsLeft} attempt left!", "@appPinAttemptLeft": {"placeholders": {"numberOfAttemptsLeft": {"type": "int"}}}, "enterSessionPin": "Enter Session PIN", "accrued": "ACCRUED", "completed": "COMPLETED", "noTransactionsYet": "No Transactions Yet", "noAccruedTransactionDescriptionText": "You will be able to view your commission for each transaction here.", "cycleNotCompletedYet": "Cycle Not Completed Yet", "noCompletedCommissionsDescriptionText": "You can view your total commission earned for each cycle here.", "totalCommissionAccruedThisCycleTillDate": "Total commission accrued this cycle till date", "enterPinToTransferMoney": "Please enter PIN to transfer money", "welcomeHeading": "Welcome!", "welcomeMessage": "Service thousands of Yafika Mobile customers and earn commissions transparently!", "enterAsAgentManager": "<PERSON><PERSON> as agent manager", "enterAsAgent": "Enter as agent", "mobileNumber": "Mobile Number", "invalidCredentials": "Invalid Credentials. Either the phone number or password is not correct.", "userDisabled": "BCN Account for this user is deactivated!", "agentDisabled": "Your agent account has been disabled. Please contact customer support for more information.", "signInTemporarilyBlocked": "Attempts for verification were exhausted.", "senderDetails": "Sender Details", "retry": "Retry", "requestOTP": "Request OTP", "authenticateAccount": "Authenticate your Account", "authenticateAccountMessage": "Please enter the last 4 numbers of the 8-digit authentication code sent in PDF while signing up", "verify": "VERIFY", "invalidAuthCode": "Invalid code. Please ensure that you are entering the code sent to you in PDF while signing up.", "tooManyIncorrectAttempts": "You have exhausted all attempts. Please sign in again.", "home": "Home", "commissionEarned": "Commission Earned", "profile": "Profile", "commission": "Commission", "commissionTransferHelper": "Commission collected will be transferred to your wallet {remainingDuration}", "@commissionTransferHelper": {"placeholders": {"remainingDuration": {"type": "String"}}}, "wallet": "Wallet", "cashInRequest": "Cash In Request", "cashOutRequest": "Cash Out Request", "moneyTransfer": "Money Transfer", "sorryCouldNotFetchData": "Sorry! Couldn’t fetch data.", "shopClosedMessage": "Money transfers can be attempted only when the shop is set to Open.", "customerRefundsRequestCardTitle": "Amount - {transactionAmount}", "@customerRefundsRequestCardTitle": {"placeholders": {"transactionAmount": {"type": "String"}}}, "refundRequests": "Refund Requests", "customerRefundsEmptyStateTitle": "No Unclaimed Requests", "customerRefundsEmptyStateSubtitle": "You will be able to view the list of unclaimed requests here.", "at": "at", "open": "Open", "closed": "Closed", "shopStatusChangeLocationError": "You need to be within {allowedDistance}m radius to your shop to change your shop status to Open.", "@shopStatusChangeLocationError": {"description": "Error message which will be shown when agent tries to change shop status to Open and the agent is not in some predefined location from the shop", "placeholders": {"allowedDistance": {"type": "int"}}}, "today": "Today", "yesterday": "Yesterday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "sendersName": "Sender's Name", "sendersMobileNumber": "Sender's Mobile Number", "waitForResendOTPErrorMessage": "Please request an OTP after some time.", "shopClosedDialogMessage": "Please set your shop status to Open to use this feature.", "invalidNationalID": "Invalid National ID.", "requestIdWaitForResend": "Please wait for some time before sending the request ID again.", "resendRequestsBlockedTemporarily": "Resend Requests are blocked temporarily.", "requestIdSentToRecipient": "Request ID has been sent to the recipient.", "resendRequestIdInfoText": "To resend request ID to the recipient you must enter the national ID of the sender.", "transferTo": "Transfer To", "resendRequestIdToRecipient": "Resend Request ID to Recipient", "resendRequestIdInDuration": "Resend Request ID in {remainingDuration}", "@resendRequestIdInDuration": {"placeholders": {"remainingDuration": {"type": "String"}}}, "totalCommissionEarnedBetween": "Total commission earned between {fromDate} and {toDate}", "@totalCommissionEarnedBetween": {"placeholders": {"fromDate": {"type": "String"}, "toDate": {"type": "String"}}}, "unblockRefundsCommentReason": "Enter your comment to unblock the refund request.", "requestAlreadyUnblocked": "This request is already unblocked.", "requestAmountAlreadyRefunded": "This request amount is already refunded.", "refundRequestUnblockSuccess": "Refund request has been unblocked successfully.", "enterPinToUnblockRefundRequest": "Please enter PIN to unblock refund request", "recipientNationalId": "Recipient's National ID", "recipientReceivableAmount": "Recipient's Receivable Amount", "senderRefundAmount": "Sender's Refund Amount", "customerRefundsInfoText": "To refund you must enter the national ID of the sender and enter the OTP sent to the sender.", "attemptRefund": "Attempt Refund", "refundRequest": "Refund Request", "invalidSenderNationalIdDialogContentText": "The sender's national ID that you entered is incorrect, please try again.", "refundRequestBlockedErrorMessage": "The request for refund for this transaction is blocked. Please contact Agent Manager to unblock this request.", "refund": "refund", "customerRefundTransactionBlockedError": "Request OTP has been disabled as this request is blocked, please contact agent manager to unblock the request.", "sendingOTPBlockedTemporarilyErrorMsg": "The recipient has exhausted the attempts to generate the OTP, please try again after some time.", "enterTheOTP": "Enter the OTP", "otpSentToNumber": "OTP has been sent to {phoneNumber}", "@otpSentToNumber": {"placeholders": {"phoneNumber": {"type": "String"}}}, "transaction": "transaction", "signIn": "sign in", "autoReadingOTP": "Auto reading OTP", "signInSessionExpired": "Sign in session has been expired. Please restart sign in process again.", "kycNotCompleted": "KYC not completed.", "registeredAsAgentManager": "The phone number is registered as an Agent Manager. Please sign in as an Agent Manager.", "signInAsAgent": "Sign in as an Agent", "forgotPassword": "Forgot Password?", "incorrectPassword": "The password you entered is incorrect. Please try again.", "forgotPasswordMessage": "Please go to Yafika Mobile app and reset your Yafika Mobile account password.", "dismiss": "<PERSON><PERSON><PERSON>", "enterNewPassword": "Enter New Password", "confirmNewPassword": "Confirm New Password", "resetPassword": "Reset Password", "resetPasswordSubtitle": "Reset your password to sign in", "passwordConditionHeader": "Your password must:", "passwordLengthCondition": "• Be at least 8 characters long", "passwordCharacterCondition": "• Contain at least one upper case character, one lower case character and one digit", "passwordWordCondition": "• Not contain common dictionary words", "passwordWhitespaceCondition": "• Not start or end with white spaces", "passwordUnmatchCondition": "The paswords you entered does not match. Please try again.", "tryAgain": "Try Again", "insecurePasswordText": "The password does not contain either an uppercase letter, a lowercase letter or a digit.", "reusedPasswordText": "This password was used recently. Please choose a different one.", "welcomeTextForAgentsTitle": "Welcome, Thank you for your interest in Agency Banking", "welcomeTextForAgentsSubtitle": "Please submit your details and the application will be reviewed by your Agent Manager.", "applyToBeAnAgent": "Apply to be an agent", "userAlreadyRegisteredAsAgentManagerError": "This number has already been registered as an Agent Manager, please use a different number.", "agentDeactivated": "Your account has been deactivated, please contact customer support.", "applicationApproved": "Your application is approved!", "getStarted": "Get Started", "applicationSubmitted": "Your application has been submitted.", "tooManyApplications": "Too many applications were rejected for this account. Please try again later.", "previousApplications": "Your Previous Applications", "rejected": "Rejected", "reason": "Reason", "appliedDate": "Applied On:", "localeChangeAcknowledgment": "Your app language preference has been changed across Yafika.", "submit": "Submit", "optional": "(Optional)", "shopLocation": "Shop Location", "enterShopDetails": "Enter Shop Details", "invalidShopImage": "Something went wrong while uploading the image. Please try again.", "applicationAlreadySubmitted": "Application already submitted.", "applicationSubmissionLimitExceeded": "You have exhausted your limit to submit application.", "addShopPhoto": "Add shop photo", "requestID": "Request ID", "incorrectNationalOrRequestID": "Incorrect national ID or request ID. Please try again.", "refundAmountCommentReason": "Enter your comment to refund the amount back to the agent.", "refundSuccessMessage": "Amount has been successfully refunded to the agent, the sender can collect the amount from the agent.", "tapToAddLocation": "Tap here to add location", "updateApp": "Please update the app with the latest version and try again.", "profileInformation": "Profile Information", "changeDefaultWallet": "Change Default <PERSON>", "changeLanguage": "Change Language", "changeSessionPIN": "Change Session PIN", "privacyPolicy": "Privacy Policy", "noEmailAppFoundError": "No email app present on the device. Please contact {supportEmailAddress}.", "@noEmailAppFoundError": {"placeholders": {"supportEmailAddress": {"type": "String"}}}, "agentApplicationApprovalPending": "Thank you for submitting your application. It will now be reviewed by your Agent Manager. We kindly request you to wait for the approval.", "walletBalance": "Wallet Balance", "dayText": "day", "daysText": "days", "needCameraPermission": "Access to camera has been denied, please allow the access from your settings to capture a photo.", "needPhotosPermission": "Access to gallery has been denied, please allow the access from your settings to select a photo.", "uploadShopPhoto": "upload shop's photo", "uploadSenderPhoto": "upload sender's photo", "selectSmallerImage": "Failed to upload the image due to bigger size, please try again with a lower size image.", "defaultWallet": "<PERSON><PERSON><PERSON>", "walletIdDetails": "Wallet ID ****{id}", "@walletIdDetails": {"placeholders": {"id": {"type": "String"}}}, "setText": "Set", "changeWalletConfirmationMessage": "Are you sure you want to set this wallet as your default wallet?", "changeAgentAccountInActiveAccountErrorMessage": "The wallet you are trying to set as default wallet is currently inactive.", "shareLocationMessageTitle": "Shop Location", "shareLocationMessage": "Find my shop {shopName} at this location: {googleMapsUrl}", "@shareLocationMessage": {"placeholders": {"shopName": {"type": "String"}, "googleMapsUrl": {"type": "String"}}}, "selectLanguage": "Select Language", "enterCurrentSessionPIN": "Enter Current Session PIN", "sessionPinChangedMessage": "Your session PIN has been changed.", "enterNewSessionPIN": "Enter New Session PIN", "confirmNewSessionPIN": "Confirm New Session PIN", "biometricsNotAvailable": "Your device is not configured for biometrics authentication.", "signInPhoneNumberUnknown": "This number is not registered to Yafika Mobile. You need to be a Yafika Mobile user to become an agent.", "somethingWentWrongTitle": "Something Went Wrong", "senderAndRecipientNationalIdCannotBeSame": "Sender and recipient national ID cannot be the same.", "decline": "Decline", "accept": "accept", "acceptRequestLastAttemptErrorMessage": "You have 1 more OTP attempt remaining. Entering an incorrect OTP will result in this request being refunded to the sender.", "noLocationsFound": "No locations found for '{searchQuery}'.", "@noLocationsFound": {"placeholders": {"searchQuery": {"type": "String"}}}, "selectCountryCode": "Select Country Code", "cannotFindAddress": "Address unavailable. Location selected.", "unableToOpenBCNMissingWebBrowserErrorMessage": "Failed to open Yafika Mobile because your device is missing a browser.", "unableToOpenGoogleMaps": "Failed to open as your device is missing Google Maps application.", "homeScreen": "Home Screen", "userNotAgentManager": "You are not an Agent Manager. Please sign in with valid credentials.", "noInternetConnectionPleaseTryAgain": "No Internet Connection, Please Try Again", "invalidMobileNumberError": "Please enter a valid mobile number.", "invalidAmountError": "Please enter amount greater than 0.", "fieldIsRequired": "{fieldName} is required.", "@fieldIsRequired": {"description": "Will be used in form validator for mandatory fields.", "placeholders": {"fieldName": {"type": "String"}}}, "amount": "Amount", "numberOfOTPResendAttemptsLeft": "• You have {attempts} OTP resend attempts left.", "@numberOfOTPResendAttemptsLeft": {"placeholders": {"attempts": {"type": "int"}}}, "numberOfOTPResendAttemptLeft": "• You have {attempt} OTP resend attempt left.", "@numberOfOTPResendAttemptLeft": {"placeholders": {"attempt": {"type": "int"}}}, "invalidOTP": "Please enter valid OTP.", "otp": "OTP", "transactionDetailsString": "Transaction Details", "contactSupportHelperString": "Contact support in case of any issues with transaction", "signInAsAgentManager": "Sign in as Agent Manager", "signInWithMobileNumber": "Use your mobile number to sign in", "enterPasswordTitle": "Enter Your Password", "enterPasswordMessage": "Enter your password to sign in", "selectPhoto": "Select Photo", "deletePhoto": "Delete Photo", "shopPhotoTitle": "Shop Photo (Optional)", "resendRequestId": "Resend Request ID", "loading": "Loading, please wait.", "setupSessionPinSubtitle": "Setup session PIN so only you can use this app. Session PINs are never synced to our servers.", "biometricsUnavailableWarning": "Your device is not configured for biometrics authentication.", "back": "Back", "biometricsVerificationFailedError": "Biometric authentication failed. Bypass session PIN entry using the biometrics option is disabled.", "addShopLocation": "Add Shop Location", "addLocation": "Add location", "locationSelected": "Location selected", "basicDetails": "Basic Details", "applicationSubmittedText": "Application submitted", "forgotSessionPin": "FORGOT SESSION PIN?", "wrongSessionPinEnteredError": "Wrong session <PERSON><PERSON> entered, {attemptsLeft} attempts left", "@wrongSessionPinEnteredError": {"placeholders": {"attemptsLeft": {"type": "int"}}}, "singleAttemptLeftError": "Wrong session <PERSON><PERSON> entered, 1 attempt left", "biometricsUnavailableOnDevice": "Biometric authentication unavailable on your device", "biometricAuthReason": "Yafika Agency Banking needs to verify that it's you.", "exhaustedBiometricAttempts": "You've exhausted your attempts at a biometric sign in. Enter your Session PIN to continue.", "forgotSessionPinDialogMessage": "You will be signed out from the app, please sign in again to setup a new session PIN.", "agentApplications": "Agent Applications", "approveAgentApplications": "Approve agent applications", "refundAmountToTheAgent": "Refund amount to the agent", "unblockTheRefundRequests": "Unblock the refund requests", "userDetails": "User Details", "otpVerification": "OTP Verification", "nationalID": "National ID", "setAsDefault": "Set as default", "thirdPartySoftware": "Third Party Software", "unableToOpenUrlMissingBrowserErrorMessage": "Failed to open the URL because your device is missing a browser.", "commissionDetails": "Commission Details", "noDialerAppFoundError": "Failed to call this number because your device is missing a dialer.", "verifyShopDetails": "Verify Shop Details", "shopPhotoNotUploaded": "Shop photo was not uploaded", "approve": "Approve", "otpVerificationString": "OTP Verification", "basicDetailsString": "Basic Details", "comment": "Comment", "approveApplicationLocationCheck": "To approve, you need to be within {locationRadius}m radius of the agent’s location.", "@approveApplicationLocationCheck": {"placeholders": {"locationRadius": {"type": "int"}}}, "confirmDiscardChangesMessage": "Are you sure you want to go back? All the changes made to the shop details will be lost and can’t be recovered.", "updatedLocationOutOfRange": "Failed to update shop location. New location is more than {locationRadius}m away from the original location.", "@updatedLocationOutOfRange": {"placeholders": {"locationRadius": {"type": "int"}}}, "unblock": "unblock", "invalidComment": "Please enter a valid comment.", "exchangeRateError": "Sorry, we do not support currency exchange for now.", "senderNationalID": "Sender’s National ID", "noInternetTryAgainLater": "No internet connection, please try again later.", "appInfo": "App Info", "shareUsageReports": "Share usage reports", "shareUsageReportsRestartApp": "You will need to restart the app to apply this setting.", "closeApp": "Close App", "tooManyRequestAgentManagerSignInRequest": "Too many requests have been made. Please wait for sometime.", "applicationLimitExceededErrorMessage": "Your agent application request has exceeded the number of request limits. Please try again after your request limit is restored.", "refundTitle": "Refund", "createTitle": "Create"}