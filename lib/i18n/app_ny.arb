{"appTitle": "Yafika Agency Banking", "exactTime12HrsFormat": "dd, <PERSON><PERSON> yyyy 'pa' hh:mm aaa", "exactTime24HrsFormat": "dd, M<PERSON> yyyy 'pa' HH:mm", "transactionDateTime12HrsFormat": "hh:mm aaa 'pa' dd, MMM yyyy", "transactionDateTime24HrsFormat": "HH:mm 'pa' dd, MMM yyyy", "transactionFee": "Malipiro a Transaction", "customer": "Makasitomala", "recipientNationalIdNo": "<PERSON><PERSON><PERSON> ya <PERSON> ya Wolandira.", "sendAmount": "<PERSON><PERSON><PERSON><PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails": "Tsatanetsatane wa Transaction:", "customerCredit": "<PERSON><PERSON><PERSON> ya Makasitomala", "customerDebit": "Debit ya Makasitomala", "recipientReceives": "<PERSON><PERSON><PERSON><PERSON>", "proceed": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "approveRequestConfirmation": "Chonde tsimikizirani kuti mukufuna kuvomereza pempholi.", "rejectRequestConfirmation": "Chonde tsimikizani kuti mukufuna kukana pempholi.", "enterComment": "<PERSON><PERSON>", "okay": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirm", "enterAmount": "<PERSON><PERSON><PERSON>", "enterMobileNumber": "<PERSON><PERSON><PERSON>ja", "searchCountryCode": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "appBarTitle": "<PERSON><PERSON>, {name}", "@appBarTitle": {"placeholders": {"name": {"type": "String"}}}, "signOut": "<PERSON><PERSON><PERSON>", "enterOTP": "Lowetsani OTP", "otpValidTill": "OTP ndiyotheka mpaka {time}.", "@otpValidTill": {"placeholders": {"time": {"type": "String"}}}, "resendOTP": "Tumizaninso OTP", "inText": "mu", "sessionExpired": "<PERSON><PERSON><PERSON> lanu latha. <PERSON><PERSON> lowaninso kuti mupitilize.", "amountTooHighErrorWithAmount": "Ndalama zomwe mwalemba n<PERSON>, chonde low<PERSON>ani ndalama zochepa kuposa {formattedAmount}.", "@amountTooHighErrorWithAmount": {"description": "Uthenga wolakwika pakubwe<PERSON> ndalama, pamene ndalama zomwe zalowazo zakwera kwambiri.", "placeholders": {"formattedAmount": {"type": "String"}}}, "amountTooLessErrorWithAmount": "Ndalama zomwe mwalemba n<PERSON> kwa<PERSON>, chonde low<PERSON>ani ndalama zoku<PERSON>rapo kuposa {formattedAmount}.", "@amountTooLessErrorWithAmount": {"description": "Uthenga wolakwika pak<PERSON><PERSON> ndalama, pamene ndalama zomwe mwalowazo ndizochepa kwambiri.", "placeholders": {"formattedAmount": {"type": "String"}}}, "amountTooHighError": "Ndalama zomwe zidalowetsedwa ndizokwera kwambiri.", "amountTooLessError": "Ndalama zomwe zalowetsedwa ndizochepa kwambiri.", "periodicRequestTransactionLimitExceeded": "<PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON><PERSON> chifukwa malire a wolandila adutsa.", "monetaryTransactionLimitExceeded": "<PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> chifukwa ndalama zomwe kasitomala amagulitsa ndizochulukirapo kuposa zololedwa.", "agentPeriodicTransactionLimitExceeded": "<PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON><PERSON> chifukwa malire anu apitilira.", "agentMonetaryTransactionLimitExceeded": "Ku<PERSON><PERSON><PERSON><PERSON> sikutheka chifukwa ndalama zomwe mwachita ndi zazikulu kuposa zololedwa.", "receivingAccountWouldCrossLimit": "<PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON><PERSON> chifukwa malire a akaunti yolandila adutsa.", "agentDisabledForSpecificInterval": "ID yanu yayi<PERSON>dwa kwakanthawi chifukwa chogwiritsa ntchito molakwika mawonekedwe a OTP. Chonde yesaninso nthawi ina.", "transactionSuccessful": "<PERSON><PERSON><PERSON><PERSON>", "contactSupport": "<PERSON><PERSON><PERSON><PERSON> cha kulum<PERSON>a", "done": "<PERSON><PERSON><PERSON>", "transactionIdString": "id", "copiedToClipboardToastMessage": "Zakopedwa ku Clipboard!", "unableToPerformExchange": "<PERSON><PERSON><PERSON> sit<PERSON>the kuyankha pempho lanu chifukwa kusinthaku siku<PERSON>.", "depositToOwnWallet": "<PERSON><PERSON><PERSON><PERSON> chikwama chanu", "depositToOwnWalletTitle": "<PERSON><PERSON><PERSON><PERSON>", "applications": "Mapulogalamu", "noApplicationsTitle": "Palibe Ntchito <PERSON> za Wothandizira", "noApplicationsMessage": "<PERSON><PERSON>gal<PERSON><PERSON> at<PERSON> a<PERSON> a<PERSON>.", "noUnblockedRefundRequestsTitle": "Palibe Mapempho Atsopano Obweza Ndalama Osaletsa", "noUnblockedRefundRequestsSubtitle": "Zopempha zatsopano zobwza ndalama ziwoneka pano.", "requestId": "Pemphani ID - {requestIdentifier}", "@requestId": {"description": "Request Id of blocked refund request.", "placeholders": {"requestIdentifier": {"type": "String"}}}, "agentDetails": "Zambiri za Agent", "blockedOn": "Yaletsedwa {dateTime}", "@blockedOn": {"description": "Formatted date and time of transaction block event", "placeholders": {"dateTime": {"type": "String"}}}, "senderRequest": "<PERSON><PERSON><PERSON><PERSON>", "requestDetails": "<PERSON><PERSON><PERSON><PERSON>", "requestIdHeader": "Funsani Id", "amountTransferred": "Ndalama Z<PERSON>umizidwa", "createdOnHeader": "Adapangidwa Pa", "blockedOnHeader": "Oletsedwa Pa", "agentsName": "Dzina la Agent", "agentsMobileNumber": "<PERSON><PERSON><PERSON>manja ya <PERSON>", "unblockRefund": "TULANI KUBWERETSA NDONDOMEKO", "locationServiceNeeded": "Le service de localisation est nécessaire pour utiliser l'application Yafika Agency Banking.", "locationPermissionNeeded": "Une autorisation de localisation est nécessaire pour utiliser l'application Yafika Agency Banking.", "openSettings": "TSEGULANI ZAMBIRI", "agentRequest": "Pempho la Agent", "next": "ENA", "reject": "KANIZA", "continueText": "<PERSON><PERSON><PERSON><PERSON>", "phoneNumberUnknownError": "Sitinathe kupeza Yafika Mobile User ndi nambala iyi {number}.", "@phoneNumberUnknownError": {"placeholders": {"number": {"type": "String"}}}, "change": "<PERSON><PERSON><PERSON>", "saveChanges": "<PERSON><PERSON>", "shopName": "<PERSON>zin<PERSON> la Shopu", "editShopDetails": "<PERSON><PERSON><PERSON> wa <PERSON>", "shopDetails": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "shopImageNotUploaded": "<PERSON><PERSON><PERSON> chithunzi cham'sitolo chomwe chidakwe<PERSON>wa", "geocodingErrorMessage": "<PERSON><PERSON><PERSON> k<PERSON> adilesi yazo<PERSON>we zili pano.", "searchLocationPlaceHolder": "<PERSON><PERSON><PERSON>", "inSufficientBalance": "Kugulitsaku sikungachitike chifukwa chosakwan<PERSON> bwino m'chikwama chanu. Chonde d<PERSON>wani kuti {transactionFee} iwonjezedwa ngati chindapusa.", "@inSufficientBalance": {"placeholders": {"transactionFee": {"type": "String"}}}, "customerInsufficientBalance": "Kugulitsa<PERSON> sikungachitike chifukwa chosakwanira bwino m'chikwama cha kasitomala. Chonde d<PERSON>wani kuti {transactionFee} iwonjezedwa ngati chindapusa.", "@customerInsufficientBalance": {"placeholders": {"transactionFee": {"type": "String"}}}, "cashInRecipientCannotBeSelf": "<PERSON><PERSON><PERSON><PERSON> la ndalama silingapangidwe ndi Wothandizira. Chonde yesani ndi nambala ina.", "cashInRecipientCannotBeAgent": "<PERSON><PERSON><PERSON><PERSON> la ndalama silingapangidwe ndi Wothandizira. Chonde yesani ndi nambala ina.", "lookingUpDetailsPhoneNumber": "Kufu<PERSON>za zambiri za {number}.", "@lookingUpDetailsPhoneNumber": {"description": {"placeholders": {"number": {"type": "String"}}}}, "distanceLimitMessage": "<PERSON>ye<PERSON>a kukhala mkati mwa {allowedDistance}m utali wozungulira kuti mutsimikizire malo a wothandizirayo.", "@distanceLimitMessage": {"description": "<PERSON><PERSON><PERSON><PERSON> kuti <PERSON>othandizira sali mkati mwa Utali wololedwa", "placeholders": {"allowedDistance": {"type": "int"}}}, "cancellationConfirmationMessage": "Mukutsimikiza kuti mukufuna kutseka njira ya {processName}?", "@cancellationConfirmationMessage": {"placeholders": {"processName": {"type": "String"}}}, "needHelp": "<PERSON><PERSON><PERSON><PERSON>?", "callUs": "Tiyimbireni Tsopano", "sendEmail": "<PERSON><PERSON><PERSON><PERSON>", "biometricAuthPrompt": "Kutsimikizika kwa biometric kumafunika kuti mupeze pulogalamuyi.", "cashIn": "Cash In", "confirmationTimeout": "This transaction has timed out. Please create a new {featureName} request.", "@confirmationTimeout": {"description": "Error message to be shown when a particular transaction has timed out.", "placeholders": {"featureName": {"type": "String"}}}, "couldNotSendOTPError": "<PERSON><PERSON><PERSON> kufikira ma seva athu. <PERSON><PERSON> yesaninso pakapita n<PERSON>wi.", "tooManyOTPRequests": "Mwayesapo ka<PERSON>mbiri kupanga transaction kwa kasitomalayu. <PERSON><PERSON> yesaninso nthawi ina.", "inactiveRecipient": "<PERSON><PERSON><PERSON> uku sikungasinthidwe chifukwa akaunti ya wolandirayo yatsekedwa.", "tooManyResendRequests": "<PERSON><PERSON><PERSON><PERSON> nthawi <PERSON>, chonde yes<PERSON><PERSON> nthawi ina.", "attemptTransfer": "<PERSON><PERSON><PERSON>", "otpExpired": "<PERSON><PERSON> ya<PERSON>, chonde tumizan<PERSON>o OTP ndikuyesanso.", "incorrectOTP": "OTP yolowetsedwa ndiyosavomerezeka. <PERSON>nde yesaninso.", "withdrawFromOwnWallet": "<PERSON><PERSON><PERSON> chikwama chanu", "withdrawFromOwnWalletTitle": "<PERSON><PERSON><PERSON>", "cashOutRecipientCannotBeSelf": "<PERSON><PERSON><PERSON><PERSON> siku<PERSON>we ndi wothandizira. <PERSON>nde yesani ndi nambala ina.", "cashOutRecipientCannotBeAgent": "<PERSON>empho lochotsa siling<PERSON>ngidwe ndi Wothandizira. Chonde yesani ndi nambala ina.", "senderAccountDeactivated": "<PERSON><PERSON><PERSON><PERSON> yo<PERSON> n<PERSON>.", "agentAccountWouldCrossLimit": "<PERSON><PERSON><PERSON>i ya <PERSON> id<PERSON> malire.", "noInternetConnection": "<PERSON><PERSON>be intaneti, yang'<PERSON><PERSON> k<PERSON>wa kwanu pa intaneti ndikuyesanso", "pleaseTryAgainLater": "<PERSON><PERSON> yesaninso nthawi ina.", "deletePhotoConfirmationMessage": "Mukutsimikiza kuti mukufuna kuchotsa chithunzi ichi?", "shopPhoto": "<PERSON><PERSON><PERSON><PERSON> chogula", "refundAmount": "<PERSON><PERSON><PERSON><PERSON>", "unblockRefunds": "<PERSON><PERSON><PERSON><PERSON>", "recipientsMobileNumber": "<PERSON><PERSON><PERSON>manja ya <PERSON>", "senderAndRecipientSame": "<PERSON><PERSON><PERSON> za foni za wotumiza ndi wolandira si<PERSON><PERSON>.", "senderOrRecipientCannotBeAgent": "<PERSON><PERSON><PERSON><PERSON> kapena wolandira sang<PERSON> wothandiz<PERSON>.", "sendMoney": "<PERSON><PERSON><PERSON><PERSON>", "createRequest": "Pangani pempho", "acceptRequest": "Land<PERSON>ni pempho", "activeRequests": "Zopempha zachangu", "customerRefunds": "Kubwezeredwa kwamakasitomala", "tooManyOTPSubmission": "Mwayesa kutumiza OTP kambirimbiri. Chonde pangani {featureName} pempho latsopano.", "@tooManyOTPSubmission": {"description": "Ngati wosuta ayesa kutumiza OTP kuposa nthawi <PERSON>, cholakwika ichi chiwonetsedwa.", "placeholders": {"featureName": {"type": "String"}}}, "inactiveSender": "<PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON><PERSON> chifukwa akaunti ya wotumizayo yatsekedwa.", "cashOut": "Cash Out", "incorrectNationalOrRequestId": "ID Yolakwika Yadziko Lonse kapena ID Yofunsira.\nChonde yesaninso.", "refundFlowWasInitiatedErrorMessage": "Simungatengenso kuchuluka kwa ID ya pempholi.", "recipientNationalIdNumber": "<PERSON><PERSON><PERSON> ya <PERSON> ya Wolandira.", "senderNameLabelText": "Dzina la Wotumiza", "noActiveRequestsYet": "Palibe Zomwe Mukufuna <PERSON>obe", "activeRequestsEmptyStateLabelText": "<PERSON><PERSON> kuwona mndandanda wazofunsira kutumiza ndalama pano.", "activeRequestsRequestCardTitle": "Ndalama - {transactionAmount}", "@activeRequestsRequestCardTitle": {"placeholders": {"transactionAmount": {"type": "String"}}}, "createdOnTimestamp": "Adapangidwa pa {timestamp}", "@createdOnTimestamp": {"placeholders": {"timestamp": {"type": "String"}}}, "withdrawAmount": "<PERSON><PERSON><PERSON>", "agentInformation": "Zambiri za Agent", "personalDetails": "<PERSON><PERSON><PERSON><PERSON>", "fullName": "<PERSON>zina lo<PERSON>", "otherName": "<PERSON><PERSON><PERSON>", "dateOfBirth": "Tsiku lobadwa", "nationalIdNumber": "Nambala ya <PERSON> ya dziko", "gender": "<PERSON><PERSON>", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Mkazi", "agentSignature": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON>", "signaturePhoto": "Chith<PERSON>zi cha Signature", "approveApplication": "VOMEREZANI MAFUNSO", "signatureImageNotUploaded": "Signature photo was not uploaded", "confirmApproveRequest": "Chonde tsimikizirani kuti mukufuna kuvomereza pempholi. ", "confirmRejectRequest": "Chonde tsimikizirani kuti mukufuna kukana pempholi. ", "somethingWentWrong": "Chinachake Chalakwika. <PERSON><PERSON>.", "inactiveUser": "<PERSON><PERSON><PERSON><PERSON><PERSON> saku<PERSON> n<PERSON>.", "applicationAlreadyRejected": "<PERSON><PERSON><PERSON> ya<PERSON> kale.", "applicationAlreadyApproved": "<PERSON><PERSON><PERSON> ya<PERSON> kale.", "requestRejected": "<PERSON><PERSON><PERSON> ya {name} ya<PERSON><PERSON><PERSON>.", "@requestRejected": {"placeholders": {"name": {"type": "String"}}}, "requestApproved": "<PERSON><PERSON><PERSON> ya {name} n<PERSON>yovomerezeka.", "@requestApproved": {"placeholders": {"name": {"type": "String"}}}, "locationRadiusCheck": "<PERSON><PERSON><PERSON>a kukhala mkati mwa {allowedDistance}m kuti mupite patsogolo.", "@locationRadiusCheck": {"description": "Error message which will be shown when user is trying to do some Cash transaction far away from allowed distance", "placeholders": {"allowedDistance": {"type": "int"}}}, "senderVerification": "Kutsimikizira kwa Sender", "enterSendersOTP": "Lowetsani OTP ya Sender", "recipientDetails": "Tsatanetsatane wa Wolandira", "recipientsNationalIDNumber": "<PERSON><PERSON><PERSON> ya <PERSON> ya Wolandira", "sendersNationalIDNumber": "<PERSON><PERSON><PERSON> ya <PERSON> ya Wotumiza", "sendersAddress": "<PERSON><PERSON><PERSON> ya Wotumiza", "sendersPhotoTitle": "<PERSON><PERSON><PERSON><PERSON> cha <PERSON>er", "sendersPhoto": "<PERSON><PERSON><PERSON><PERSON> cha <PERSON>er", "captureAPhotoOfSender": "Jambulani <PERSON> cha Wotumiza", "couldNotSendRequestIdErrorMessage": "<PERSON><PERSON>the kupempha ID kwa wolandira. <PERSON><PERSON><PERSON>a, kapena funsani othan<PERSON> ma<PERSON>.", "identificationIsRequired": "Chizindikiritso chikufunika kuti mulambalale PIN ya gawo pogwiritsa ntchito ma biometric.", "sessionPinDoesNotMatchWithConfirmPin": "PIN siyikugwirizana. Chonde yesaninso.", "setupSessionPin": "Kukhazikitsa PIN ya Gawo", "confirmSessionPin": "Tsimikizirani PIN ya Pulogalamu", "bypassPinUsingBiometrics": "PIN yolowera pogwiritsa ntchito biometrics", "tooManyAppPinAttempts": "Mwayesa kuyika PIN yolakwika nthawi zambiri. Mwatuluka mu pulogalamuyi.", "wrongPinEntered": "Mwalowetsa pini yolakwika.", "appPinAttemptsLeft": "Kuyesa {numberOfAttemptsLeft} kokha kwatsala!", "@appPinAttemptsLeft": {"placeholders": {"numberOfAttemptsLeft": {"type": "int"}}}, "appPinAttemptLeft": "Kuyesa {numberOfAttemptsLeft} kokha kwatsala!", "@appPinAttemptLeft": {"placeholders": {"numberOfAttemptsLeft": {"type": "int"}}}, "enterSessionPin": "Lowetsani Session PIN", "enterPinToTransferMoney": "Chonde lowetsani PIN kuti mutumize ndalama", "commissionEarned": "Commission Yapeza", "accrued": "ZOCHITIKA,", "completed": "ZAMALIZA", "noTransactionsYet": "Palibe Zochita <PERSON>", "noAccruedTransactionDescriptionText": "<PERSON><PERSON> kuwona ntchito yanu pazochitika zilizonse pano.", "cycleNotCompletedYet": "Ku<PERSON>ng<PERSON>ra <PERSON>", "noCompletedCommissionsDescriptionText": "<PERSON><PERSON> kuwona komishoni yanu yonse yomwe mwapeza pamayendedwe aliwonse apa.", "totalCommissionAccruedThisCycleTillDate": "Zonse ntchito idachita izi mpaka pano", "today": "<PERSON><PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "monday": "Lolemba", "tuesday": "<PERSON><PERSON><PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON><PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON>", "saturday": "Loweruka", "sunday": "Lamulungu", "sendersMobileNumber": "<PERSON><PERSON><PERSON>manja ya <PERSON>", "at": "ku", "sendersName": "Dzina la Wotumiza", "senderDetails": "Tsatanetsatane wa Sender", "welcomeHeading": "Takulandirani!", "welcomeMessage": "<PERSON><PERSON><PERSON> masauzande amakasi<PERSON> a Yafika Mobile ndikupeza ma commissions mowonekera!", "enterAsAgentManager": "<PERSON>ts<PERSON> ngati woya<PERSON>'anira wothandizira", "enterAsAgent": "<PERSON><PERSON> ngati wothand<PERSON>ira", "waitForResendOTPErrorMessage": "Chonde funsani OTP pakapita nthawi.", "mobileNumber": "<PERSON><PERSON><PERSON> yafoni yam'manja", "invalidCredentials": "Zizind<PERSON><PERSON>. <PERSON><PERSON>a nambala yafoni kapena mawu a<PERSON> sizo<PERSON>.", "userDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "agentDisabled": "<PERSON><PERSON><PERSON><PERSON> yanu ya wothandizira yayimitsidwa. <PERSON>nde funsani thandizo lama<PERSON>a kuti mudziwe zambiri.", "signInTemporarilyBlocked": "Zoyesa zotsimikizira zidathetsedwa.", "authenticateAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yanu", "authenticateAccountMessage": "Chonde lowetsani manambala 4 omaliza a manambala 8 otsimikizira omwe atumizidwa mu PDF pamene mukulembetsa", "verify": "TSIMIZANI", "invalidAuthCode": "<PERSON><PERSON><PERSON> yo<PERSON>. Chonde onetsetsani kuti mukulemba khodi yotumizidwa kwa inu mu PDF pamene mukulembetsa.", "tooManyIncorrectAttempts": "Mwatopetsa zoyesayesa zonse. <PERSON><PERSON>.", "retry": "<PERSON><PERSON><PERSON>", "requestOTP": "Funsani OTP", "shopClosedDialogMessage": "Chonde ikani status yanu ya shopu kuti Open kuti mugwiritse ntchito izi.", "home": "<PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON><PERSON>", "commission": "Commission", "commissionTransferHelper": "<PERSON><PERSON><PERSON><PERSON> yomwe yasonkhanitsidwa idzasamutsidwa ku chikwama chanu {remainingDuration}.", "@commissionTransferHelper": {"placeholders": {"days": {"type": "String"}}}, "wallet": "<PERSON><PERSON><PERSON><PERSON>", "cashInRequest": "<PERSON>dal<PERSON> mu Pempho", "cashOutRequest": "<PERSON><PERSON><PERSON><PERSON>", "moneyTransfer": "<PERSON><PERSON><PERSON>", "sorryCouldNotFetchData": "Pepani! Sindinathe kutenga deta.", "invalidNationalID": "ID Yadziko Lonse yolakwika.", "requestIdWaitForResend": "Chonde dikirani kwakan<PERSON>wi musanatumizenso ID yopempha.", "resendRequestsBlockedTemporarily": "<PERSON><PERSON><PERSON><PERSON>aletsedwa <PERSON>wakanthawi.", "requestIdSentToRecipient": "ID yofunsira yatumizidwa kwa wolandira.", "resendRequestIdInfoText": "<PERSON><PERSON> mut<PERSON> ID ya pempho kwa wolandirayo muyenera kulowa ID ya dziko la wotumizayo.", "transferTo": "<PERSON><PERSON><PERSON><PERSON>", "resendRequestIdToRecipient": "Tumizaninso ID Yofunsira kwa Wolandira", "resendRequestIdInDuration": "Tumizaninso ID Yofunsira mu {remainingDuration}", "@resendRequestIdInDuration": {"placeholders": {"remainingDuration": {"type": "String"}}}, "customerRefundsRequestCardTitle": "Ndalama - {transactionAmount}", "@customerRefundsRequestCardTitle": {"placeholders": {"transactionAmount": {"type": "String"}}}, "refundRequests": "Zopempha <PERSON>", "customerRefundsEmptyStateTitle": "Palibe Zopempha Z<PERSON>fu<PERSON>idwa", "customerRefundsEmptyStateSubtitle": "<PERSON><PERSON> kuwona mndandanda wazofunsira zomwe simunanene pano.", "shopClosedMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> ndalama kungayesedwe kokha pamene sitolo yakha<PERSON>tsidwa kuti Yotsegula.", "totalCommissionEarnedBetween": "<PERSON><PERSON><PERSON><PERSON> yonse yomwe idapeza pakati {fromDate} ndi {toDate}", "@totalCommissionEarnedBetween": {"placeholders": {"fromDate": {"type": "String"}, "toDate": {"type": "String"}}}, "open": "<PERSON><PERSON><PERSON><PERSON>", "closed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shopStatusChangeLocationError": "<PERSON>ye<PERSON>a kukhala mkati mwa {allowedDistance}m utali wozungulira kuti musinthe malo anu oguli<PERSON>.", "@shopStatusChangeLocationError": {"description": "Error message which will be shown when agent tries to change shop status to Open and the agent is not in some predefined location from the shop", "placeholders": {"allowedDistance": {"type": "int"}}}, "confirmSignOut": "Mukutsimikiza kuti mukufuna kutuluka?", "unblockRefundsCommentReason": "<PERSON><PERSON><PERSON> ndemanga yanu kuti musatseke pempho laku<PERSON> ndal<PERSON>.", "requestAlreadyUnblocked": "<PERSON><PERSON><PERSON><PERSON> ili n<PERSON> kale.", "requestAmountAlreadyRefunded": "Pempholi labwe<PERSON>edwa kale ndalama.", "refundRequestUnblockSuccess": "<PERSON>emph<PERSON> lobweza ndalama lats<PERSON>uli<PERSON>wa bwino.", "enterPinToUnblockRefundRequest": "<PERSON>nde lowetsani PIN kuti musatseke pempho lobweza ndalama", "recipientNationalId": "ID Yadziko Lo<PERSON> ya Wolandira", "senderRefundAmount": "Ndalama Zobwezeredwa za Wotumiza", "customerRefundsInfoText": "<PERSON><PERSON> mubwe<PERSON>e ndalama muyenera kulowa ID ya dziko la wotumiza ndikulowetsa OTP yotumizidwa kwa wotumiza.", "attemptRefund": "<PERSON><PERSON><PERSON>", "refundRequest": "<PERSON><PERSON><PERSON><PERSON>", "invalidSenderNationalIdDialogContentText": "ID yadziko ya wotumizayo yomwe mudalemba ndi<PERSON>, chonde yes<PERSON>.", "refundRequestBlockedErrorMessage": "Pempho lobweza ndalama pamalondawa laletsedwa. <PERSON>nde funsani <PERSON> W<PERSON>andiz<PERSON> kuti mutsegule pempholi.", "refund": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "customerRefundTransactionBlockedError": "Pempho la OTP layimitsidwa chifukwa pempholi laletsedwa, chonde lemberani woyang'anira wothandizira kuti aletse pempholi.", "sendingOTPBlockedTemporarilyErrorMsg": "<PERSON><PERSON><PERSON><PERSON>o watopa poyesa kup<PERSON> OTP, chonde yes<PERSON><PERSON> paka<PERSON>a n<PERSON>.", "enterTheOTP": "Lowetsani OTP", "otpSentToNumber": "OTP yatumizidwa ku {phoneNumber}", "@otpSentToNumber": {"placeholders": {"phoneNumber": {"type": "String"}}}, "transaction": "<PERSON>ug<PERSON><PERSON><PERSON>", "signIn": "<PERSON><PERSON> mua<PERSON>i", "autoReadingOTP": "Kuwerenga Auto OTP", "recipientReceivableAmount": "Kuchuluka kwa Wolandira", "signInSessionExpired": "<PERSON><PERSON><PERSON> yolowera yatha. <PERSON><PERSON> yamb<PERSON>aninso ntchito yolowera", "kycNotCompleted": "KYC sinamalizidwe.", "registeredAsAgentManager": "<PERSON>bala yafoni idalembetsedwa ngati Woyang'anira Wothandizira. Chonde lowani ngati Woyang'anira Wothandizira.", "signInAsAgent": "<PERSON><PERSON> ng<PERSON>", "forgotPassword": "<PERSON><PERSON><PERSON>wala mawu a<PERSON> o<PERSON>?", "incorrectPassword": "<PERSON><PERSON> omwe mudalemba ndi olakwika. <PERSON><PERSON> yesaninso.", "forgotPasswordMessage": "Chonde pitani ku pulogalamu ya Yafika Mobile ndikukhazikitsanso password ya akaunti yanu ya Yafika Mobile.", "dismiss": "<PERSON><PERSON><PERSON>", "enterNewPassword": "Lowets<PERSON>pan<PERSON>", "confirmNewPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetPassword": "Bwezerani Password", "resetPasswordSubtitle": "Bwezer<PERSON> mawu a<PERSON>si anu kuti mulowe", "passwordConditionHeader": "<PERSON><PERSON> anu a<PERSON> ayenera:", "passwordLengthCondition": "• Zilembo zikhale zosachepera 8", "passwordCharacterCondition": "• <PERSON><PERSON><PERSON> ndi z<PERSON>mbo z<PERSON> imodzi, zing'onozing'ono ndi dijiti imodzi", "passwordWordCondition": "• <PERSON><PERSON><PERSON> mawu od<PERSON><PERSON><PERSON> b<PERSON>o m<PERSON><PERSON><PERSON> mawu", "passwordWhitespaceCondition": "• <PERSON><PERSON><PERSON><PERSON> kapena kutha ndi mipata yoyera", "passwordUnmatchCondition": "<PERSON><PERSON> omwe mudalowe<PERSON>a sa<PERSON>. <PERSON><PERSON> yesaninso.", "tryAgain": "<PERSON><PERSON><PERSON>", "insecurePasswordText": "<PERSON><PERSON> alibe zilembo zazi<PERSON>lu, zilembo zazing'ono kapena manambala.", "reusedPasswordText": "<PERSON><PERSON> a<PERSON> agwiritsidwa ntchito posachedwa. <PERSON><PERSON> sankhani ina.", "welcomeTextForAgentsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> chif<PERSON> cha chidwi chanu ku Agency Banking", "welcomeTextForAgentsSubtitle": "<PERSON>nde perekani zambiri zanu ndipo ntchitoyo iwunikiridwa ndi Woya<PERSON> wanu.", "applyToBeAnAgent": "<PERSON><PERSON><PERSON> kuk<PERSON> woth<PERSON>", "userAlreadyRegisteredAsAgentManagerError": "<PERSON><PERSON><PERSON> iyi idale<PERSON>edwa kale ngati Agent Manager, chonde g<PERSON><PERSON><PERSON> ntchito nambala ina.", "agentDeactivated": "<PERSON><PERSON><PERSON><PERSON> yanu <PERSON>, chonde le<PERSON>ani chithandizo chama<PERSON>.", "applicationApproved": "Pempho lanu lavomerezedwa!", "getStarted": "<PERSON><PERSON><PERSON>", "applicationSubmitted": "<PERSON>empho lanu la<PERSON>.", "tooManyApplications": "<PERSON><PERSON>galam<PERSON> ambiri akani<PERSON>wa pa akauntiyi. <PERSON><PERSON> yesaninso nthawi ina.", "previousApplications": "Mapulogalamu <PERSON>", "rejected": "Kanidwa", "reason": "Chifukwa", "appliedDate": "Imayikidwa Pa:", "localeChangeAcknowledgment": "<PERSON><PERSON><PERSON><PERSON> chanu cha pulogalamu ya<PERSON> pa Ya<PERSON>.", "submit": "<PERSON><PERSON><PERSON><PERSON>", "optional": "(<PERSON><PERSON><PERSON><PERSON>)", "shopLocation": "<PERSON><PERSON>", "enterShopDetails": "Lowetsani T<PERSON> wa <PERSON>", "invalidShopImage": "Chinachake chalakwika pokwezera chithunzichi. <PERSON><PERSON> yesaninso.", "applicationAlreadySubmitted": "<PERSON><PERSON><PERSON> kale.", "applicationSubmissionLimitExceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> malire anu kuti mutumize ntchito.", "addShopPhoto": "<PERSON><PERSON><PERSON><PERSON> chithunzi cha shopu", "requestID": "Pemphani ID", "incorrectNationalOrRequestID": "ID yolakwika ya dziko kapena ID yofunsira. <PERSON>nde yesaninso.", "refundAmountCommentReason": "<PERSON><PERSON><PERSON> ndemanga yanu kuti mubweze ndalamazo kwa wothandizira.", "refundSuccessMessage": "Ndalama zabwezeredwa bwino kwa wothandizira, wotumiza atha kutolera ndalamazo kuchokera kwa wothandizira.", "updateApp": "<PERSON><PERSON> sinthani pulogala<PERSON>yi ndi mtundu wapo<PERSON>wa ndiku<PERSON>o.", "profileInformation": "<PERSON><PERSON><PERSON><PERSON>", "changeDefaultWallet": "<PERSON><PERSON><PERSON>", "changeLanguage": "<PERSON><PERSON><PERSON>", "changeSessionPIN": "Sinthani PIN ya App", "privacyPolicy": "Mfundo Zazinsinsi", "tapToAddLocation": "<PERSON><PERSON> apa kuti muwonjezere malo", "noEmailAppFoundError": "Palibe pulogalamu ya imelo yomwe ilipo pachidachi. <PERSON><PERSON> lember<PERSON> {supportEmailAddress}.", "@noEmailAppFoundError": {"placeholder": {"supportEmailAddress": {"type": "String"}}}, "agentApplicationApprovalPending": "Zikomo potumiza fomu yanu. T<PERSON>pano iwu<PERSON>irid<PERSON> ndi <PERSON>an<PERSON> Wothandizira. Tikukupemphani kuti mudikire chivomerezo.", "walletBalance": "Chikwama cha Wallet", "dayText": "tsiku", "daysText": "masiku", "needCameraPermission": "Ku<PERSON><PERSON>ra ku kamera k<PERSON>, chonde lolani mwayi wofikira pazokonda zanu kuti mujambule chithunzi.", "needPhotosPermission": "<PERSON><PERSON><PERSON><PERSON> kugalari k<PERSON>, chonde lolani mwayi wofikira pazokonda zanu kuti musankhe chithunzi.", "uploadShopPhoto": "k<PERSON><PERSON>i chithunzi cha shopu", "uploadSenderPhoto": "k<PERSON><PERSON>i chithunzi cha wotumiza", "selectSmallerImage": "<PERSON><PERSON><PERSON> chithun<PERSON><PERSON> chifuk<PERSON> chak<PERSON>, chonde yesani<PERSON> ndi chithunzi choch<PERSON>ako.", "defaultWallet": "Chik<PERSON><PERSON>", "walletIdDetails": "ID ya Wallet ****{id}", "@walletIdDetails": {"placeholders": {"id": {"type": "String"}}}, "setText": "<PERSON><PERSON><PERSON>", "changeWalletConfirmationMessage": "<PERSON><PERSON> mukutsimikiza kuti mukufuna kusintha chikwamachi kukhala chikwama chanu chokhazikika?", "changeAgentAccountInActiveAccountErrorMessage": "Chik<PERSON>ma chomwe mukuyesera kuti chikhale chokhazikika sichikugwira ntchito pakadali pano.", "shareLocationMessageTitle": "<PERSON><PERSON>", "shareLocationMessage": "P<PERSON>ani shopu yanga {shopName} pamalo pano: {googleMapsUrl}", "@shareLocationMessage": {"placeholders": {"shopName": {"type": "String"}, "googleMapsUrl": {"type": "String"}}}, "selectLanguage": "<PERSON><PERSON><PERSON>", "enterCurrentSessionPIN": "Lowetsani PIN ya Panopa Gawo", "sessionPinChangedMessage": "PIN yanu yagawo yasinthi<PERSON>wa.", "enterNewSessionPIN": "Lowetsani PIN Ya<PERSON>ano ya Session", "confirmNewSessionPIN": "Tsimikizirani PIN Yatsopano Yachigawo", "biometricsNotAvailable": "Chipangizo chanu sichinasinthidwe kuti chitsimikizire ma biometrics.", "signInPhoneNumberUnknown": "Nambala iyi sinalembetsedwe ku Yafika Mobile. Muyenera kukhala wogwiritsa ntchito Yafika Mobile kuti mukhale wothandizira.", "somethingWentWrongTitle": "<PERSON><PERSON><PERSON>", "senderAndRecipientNationalIdCannotBeSame": "Wotumiza ndi wolandira ID ya dziko sangakhale yemweyo.", "decline": "<PERSON><PERSON><PERSON>", "accept": "kuvomereza", "acceptRequestLastAttemptErrorMessage": "<PERSON><PERSON> ndi kuyesanso kumodzi kwa OTP. Kulowetsa OTP yolakwika kupangitsa kuti pempholi libwezedwe kwa wotumiza.", "noLocationsFound": "Palibe malo omwe apezeka a '{searchQuery}'.", "@noLocationsFound": {"placeholders": {"searchQuery": {"type": "String"}}}, "selectCountryCode": "<PERSON><PERSON><PERSON>", "cannotFindAddress": "<PERSON><PERSON><PERSON> palibe. Malo asankhidwa.", "unableToOpenBCNMissingWebBrowserErrorMessage": "Zalephera kutsegula Yafika Mobile chifukwa chipangizo chanu chilibe msakatuli.", "unableToOpenGoogleMaps": "Zalephera kutsegulidwa chifukwa chipangizo chanu chilibe pulogalamu ya Google Maps.", "homeScreen": "Home Screen", "userNotAgentManager": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> lowani ndi zovomerezeka.", "noInternetConnectionPleaseTryAgain": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "invalidMobileNumberError": "<PERSON><PERSON> low<PERSON>ani nambala yafoni yolon<PERSON>la.", "invalidAmountError": "Chonde lowetsani ndalama zoposa 0.", "fieldIsRequired": "{fieldName} ndiyofunika.", "@fieldIsRequired": {"description": "Will be used in form validator for mandatory fields.", "placeholders": {"fieldName": {"type": "String"}}}, "amount": "<PERSON><PERSON><PERSON>", "numberOfOTPResendAttemptsLeft": "• <PERSON>li ndi {attempts} OTP oyesa kutumizanso omwe atsala.", "@numberOfOTPResendAttemptsLeft": {"placeholders": {"attempts": {"type": "int"}}}, "numberOfOTPResendAttemptLeft": "• <PERSON><PERSON> ndi {attempt} OTP oyesa kutumizanso omwe atsala.", "@numberOfOTPResendAttemptLeft": {"placeholders": {"attempt": {"type": "int"}}}, "invalidOTP": "Chonde lowetsani OTP yolondola.", "otp": "OTP", "transactionDetailsString": "Tsatanetsatane wa Transaction", "contactSupportHelperString": "Lumi<PERSON>zanani ndi chithandizo ngati muli ndi vuto lili<PERSON>e pakugulitsa", "signInAsAgentManager": "<PERSON><PERSON> ngati <PERSON>", "signInWithMobileNumber": "<PERSON><PERSON><PERSON><PERSON> ntchito nambala yanu yam'manja kuti mulowe", "enterPasswordTitle": "<PERSON><PERSON><PERSON>", "enterPasswordMessage": "Enter your password to sign in", "selectPhoto": "<PERSON><PERSON><PERSON>", "deletePhoto": "<PERSON><PERSON><PERSON>", "shopPhotoTitle": "<PERSON><PERSON><PERSON> (chosasankha)", "resendRequestId": "Tumizaninso ID Yofunsira", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>, chonde di<PERSON>.", "setupSessionPinSubtitle": "K<PERSON><PERSON><PERSON>tsani pini yagawo kuti muthe kugwiritsa ntchito pulogalamuyi. Zikhomo za nthawi sizimalumikizidwa ku maseva athu.", "biometricsUnavailableWarning": "Chipangizo chanu sichinasinthidwe kuti chitsimikizire ma biometrics.", "biometricsVerificationFailedError": "Kutsimikizira kwa biometric kwalephera. Kulowetsa PIN ya Bypass Session pogwiritsa ntchito njira ya biometrics kwayimitsidwa.", "back": "Kumb<PERSON><PERSON>", "addShopLocation": "<PERSON><PERSON><PERSON><PERSON>", "addLocation": "<PERSON><PERSON><PERSON><PERSON> malo", "locationSelected": "Emplacement sélectionné", "basicDetails": "Tsatanets<PERSON><PERSON>", "applicationSubmittedText": "<PERSON><PERSON><PERSON>", "forgotSessionPin": "WAIWALA PIN YA SESSION?", "wrongSessionPinEnteredError": "PIN yanthawi yo<PERSON> ya<PERSON>, zoyesa {attemptsLeft} zatsala", "@wrongSessionPinEnteredError": {"placeholders": {"attemptsLeft": {"type": "int"}}}, "singleAttemptLeftError": "PIN yolakwika ya<PERSON>, kuy<PERSON>a kamo<PERSON> k<PERSON>a", "biometricsUnavailableOnDevice": "Kutsimikizira kwa Biometric sikukupezeka pa chipangizo chanu", "biometricAuthReason": "Yafika Agency Banking ikuyenera kutsimikizira kuti ndi inu.", "exhaustedBiometricAttempts": "Mwatopa kulowa poyesa kulowa mu biometric. Lowetsani PIN yanu ya Session kuti mupitilize.", "forgotSessionPinDialogMessage": "Mudzatuluka mu pu<PERSON>, chonde low<PERSON> kuti mukhazikitse pini yagawo yatsopano.", "agentApplications": "<PERSON><PERSON>galamu a <PERSON>", "approveAgentApplications": "<PERSON><PERSON><PERSON><PERSON>", "refundAmountToTheAgent": "<PERSON><PERSON><PERSON><PERSON> ndalama kwa wothandizira", "unblockTheRefundRequests": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>mp<PERSON> zobwezeredwa", "userDetails": "Tsatanetsatane wa Wogwiritsa", "otpVerification": "Kutsimikizira kwa OTP", "nationalID": "Nambala ID", "setAsDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ngati chosa<PERSON>tha", "unableToOpenUrlMissingBrowserErrorMessage": "Yakanika kutsegula URL chifukwa chipangizo chanu chilibe msakatuli.", "thirdPartySoftware": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commissionDetails": "Zambiri za Commission", "noDialerAppFoundError": "<PERSON><PERSON><PERSON><PERSON> kuyimba nambalayi chifukwa chipangizo chanu chilibe choyimbira.", "verifyShopDetails": "Tsimiki<PERSON>rani T<PERSON> wa Masi<PERSON>lo", "shopPhotoNotUploaded": "<PERSON><PERSON><PERSON><PERSON> chama<PERSON>da sic<PERSON>zed<PERSON>", "approve": "<PERSON><PERSON><PERSON><PERSON>", "otpVerificationString": "Kutsimikizira kwa OTP", "basicDetailsString": "Tsatanets<PERSON><PERSON>", "comment": "Ndemanga", "approveApplicationLocationCheck": "<PERSON><PERSON>, muy<PERSON>a kukhala mkati mwa {locationRadius}m utali wa malo omwe wothandizira ali.", "@approveApplicationLocationCheck": {"placeholders": {"locationRadius": {"type": "int"}}}, "confirmDiscardChangesMessage": "Mukutsimikiza kuti mukufuna kubwerera? Zosintha zonse zomwe zasinthidwa kusitolo zidzatayika ndipo sizingabwezeretsedwe.", "updatedLocationOutOfRange": "<PERSON><PERSON><PERSON><PERSON> kusintha malo og<PERSON>. Malo atsopano ndi opitilira {locationRadius}m kutali ndi malo oyamba.", "@updatedLocationOutOfRange": {"placeholders": {"locationRadius": {"type": "int"}}}, "unblock": "<PERSON><PERSON><PERSON><PERSON>", "invalidComment": "<PERSON><PERSON> low<PERSON>ani nde<PERSON>ga yolon<PERSON>.", "exchangeRateError": "<PERSON><PERSON><PERSON>, sit<PERSON><PERSON><PERSON><PERSON><PERSON> kusintha ndalama pakadali pano.", "senderNationalID": "Nambala ID Ya Wotumiza", "noInternetTryAgainLater": "<PERSON><PERSON><PERSON> intaneti, chonde yesaninso nthawi ina.", "appInfo": "Zambiri za App", "shareUsageReports": "<PERSON><PERSON><PERSON> ma<PERSON> n<PERSON>", "shareUsageReportsRestartApp": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> pulog<PERSON>yi kuti mug<PERSON>e ntchito <PERSON>.", "closeApp": "<PERSON><PERSON><PERSON><PERSON>", "tooManyRequestAgentManagerSignInRequest": "Zopempha zambiri zapangidwa. <PERSON><PERSON> di<PERSON>rani kanthawi.", "applicationLimitExceededErrorMessage": "Pempho lanu lopempha wothandizira ladutsa kuchuluka kwa zomwe mwapempha. Chonde yesaninso malire anu akabwezeretsedwa.", "refundTitle": "Zopempha", "createTitle": "Pangan<PERSON>"}