import 'dart:io';

import 'package:bcn_agency_banking_flutter/src/core/auth/auth_provider.dart';
import 'package:bcn_agency_banking_flutter/src/core/locale_service/bcn_locale.dart';
import 'package:bcn_agency_banking_flutter/src/core/location_services/location_bloc.dart';
import 'package:bcn_agency_banking_flutter/src/core/route_generator.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent/agent_setup/application_status/agent_application_status_screen.dart';
import 'package:bcn_agency_banking_flutter/src/features/agent_manager/agent_manager_home_screen.dart';
import 'package:bcn_agency_banking_flutter/src/helpers/location_headers.dart';
import 'package:bcn_agency_banking_flutter/src/utils/extensions.dart';
import 'package:dedwig/dedwig.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:google_maps_flutter_android/google_maps_flutter_android.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'src/core/auth/app_pin_provider.dart';
import 'src/core/data_source/app_data_source.dart';
import 'src/core/service_locator.dart';
import 'src/core/temporary_logger.dart';
import 'src/features/bcn_agency_app.dart';
import 'src/features/landing_screen/landing_screen.dart';
import 'src/features/session_pin/enter_session_pin/enter_session_pin_screen.dart';
import 'src/firebase/setup_firebase.dart';
import 'src/flavors/flavors.dart';
import 'src/helpers/user_agent_headers.dart';
import 'src/utils/network_proxy.dart';

void main() async {
  final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isAndroid) {
    final mapsImplementation = GoogleMapsFlutterAndroid();

    if (kDebugMode) {
      try {
        // We don't need this value.
        final _ = await mapsImplementation.initializeWithRenderer(
          AndroidMapRenderer.latest,
        );
      } on PlatformException catch (e) {
        const renderAlreadyInitializedError = "Renderer already initialized";
        final isRenderAlreadyInitializedError =
            e.code.toLowerCase() == renderAlreadyInitializedError.toLowerCase();
        // If Renderer is already initialized, then ignore the exception.
        // This error is safe to exit in debug mode
        // since native code initialization has already been done
        // and on Hot restart it again tries to do it which throws this error.
        if (!isRenderAlreadyInitializedError) {
          rethrow;
        }
      }
    } else {
      // We don't need this value.
      final _ = await mapsImplementation.initializeWithRenderer(
        AndroidMapRenderer.latest,
      );
    }
  }
  // Preserve the Splash screen until Initial route is setup.
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Force Portrait mode.
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  setupLocator();
  await setupFirebase();
  await _setAPIClient();

  final currentUserType = await AuthProvider.instance.getSignedInUserType();

  // Initial Route of the app when the app starts.
  await _setInitialRoute(currentUserType);
  runApp(
    BlocProvider(
      create: (_) => LocationBloc(),
      child: BCNAgencyApp(
        currentUserType: currentUserType,
        key: BCNLocale.mainAppKey,
      ),
    ),
  );
}

Future<void> _setAPIClient() async {
  final AppFlavor flavor = currentFlavor;
  if (flavor.isDev) await setupNetworkProxy();
  final LogLevel logLevelBasedOnFlavor =
      flavor.isDev ? LogLevel.debug : LogLevel.none;
  final packageInfo = await PackageInfo.fromPlatform();
  final BaseDeviceInfo deviceInfo = await DeviceInfoPlugin().deviceInfo;

  // Register Singleton of AppDataSource across the whole app
  locator.registerLazySingleton<AppDataSource>(() {
    final AsyncAPIClient apiClient = AsyncAPIClient(
      configuration: APIClientConfiguration(
        baseURL: Uri.parse(currentFlavor.baseUrl),
        logConfiguration: LogConfiguration(
          logger: TemporaryConsoleLogger(),
          requestBody: logLevelBasedOnFlavor,
          requestMetadata: logLevelBasedOnFlavor,
          responseBody: logLevelBasedOnFlavor,
          responseMetadata: logLevelBasedOnFlavor,
        ),
        headersGenerators: [
          locationHeadersGenerator,
          (request) => userAgentHeaders(request, packageInfo, deviceInfo),
        ],
        timeout: 60000,
      ),
    );
    final AppDataSource dataSource = AppDataSource(apiClient: apiClient);
    return dataSource;
  });
}

Future<void> _setInitialRoute(UserType? currentUserType) async {
  final bool isAppPinSetup = await AppPinProvider.isAppPinSetup();
  switch (currentUserType) {
    case UserType.agent:
      if (isAppPinSetup) {
        Routes.initialRoute = EnterSessionPinScreen.launchId;
      } else {
        // If app pin is not setup for the current session, the agent is
        // navigated to the `AgentApplicationStatusScreen` instead of the
        // `AppPinScreen`. On the `AgentApplicationStatusScreen`
        // the `SetupAppPinScreen` is displayed when the app pin is not
        // set up for the current session.
        Routes.initialRoute = AgentApplicationStatusScreen.id;
      }
      break;
    case UserType.agentManager:
      if (isAppPinSetup) {
        Routes.initialRoute = EnterSessionPinScreen.launchId;
      } else {
        // If app pin is not setup for the current session, the agent manager is
        // navigated to the `AgentManagerHomeScreen` instead of the
        // `AppPinScreen`. On the `AgentManagerHomeScreen`
        // the `SetupAppPinScreen` is displayed when the app pin is not
        // set up for the current session.
        Routes.initialRoute = AgentManagerHomeScreen.id;
      }
      break;
    case null:
      Routes.initialRoute = LandingScreen.id;
      break;
  }
  FlutterNativeSplash.remove();
}
