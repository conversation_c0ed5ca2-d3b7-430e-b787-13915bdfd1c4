# BCN-Agency-Banking-Flutter

The Agency Banking app for the BCN platform, written in Flutter.

Currently, releases are done every Thursday only for the Android platform.

## Cloning the project

To get setup with the project, please install [Git LFS](https://git-lfs.github.com) and clone the
project.

## Getting started

1. Get all the packages by running following command: <br>
    ```console
    flutter pub get 
    ```
2. Run the build runner: <br>
    ```console
    flutter pub run build_runner build --delete-conflicting-outputs 
    ```   

## Setting up Flavor in your favourite IDE

- For Android Studio users, no need to worry about run configurations as run configurations are
  already checked in the Git and you can view the configurations like so:
  ![Flavors](git_assets/android_studio.png)

- For VSCode users, Create `launch.json` file if not made in the `.vscode` folder. Copy and paste
  below snippet inside the `launch.json` file and then you can view all the run configurations.

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "MOCK",
      "request": "launch",
      "type": "dart",
      "args": [
        "--dart-define=flavor=MOCK",
        "--flavor dev"
      ]
    },
    {
      "name": "DEV",
      "request": "launch",
      "type": "dart",
      "args": [
        "--dart-define=flavor=DEV",
        "--flavor dev"
      ]
    },
    {
      "name": "PROD",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "args": [
        "--dart-define=flavor=PROD",
        "--flavor prod"
      ]
    },
    {
      "name": "UAT",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "args": [
        "--dart-define=flavor=UAT",
        "--flavor uat"
      ]
    }
  ]
}
```

## Setting up Google Maps key

This app uses Google Maps for various features. For Google Maps to work correctly, you need to
initiate Google maps key using the script.
<details>
  <summary>Google Maps API Key For developers</summary>
  AIzaSyB_JjfSSespKSawVKY7Z0qzW-znJFbfGLY
</details>
Run the command `sh set-google-maps-key.sh [ABOVE_GOOGLE_MAPS_API_KEY_HERE]` from the root of
this project.
Note: The above key is only used for development purpose. Release builds for DEV, UAT and PROD will have their own key.
This will set the keys for each of the targets this app supports.
All files that are created as a result of the key being set are ignored by git so key-related
changes don't make their way into the git repository.

When the project is built for release using CI, the same script is called using keys for release.

If you don't call the script before attempting to build/run the project, the app will not compile.

## Running the app

- The Agency app runs on three flavors viz DEV, PROD and UAT. DEV is the default flavor.
- To run the app in an X flavor using CLI, run the following command:

```console
    flutter run --dart-define=flavor=X --flavor=Y
```

where, X can be MOCK, DEV, PROD or UAT case insensitive.
where, Y can be dev, uat and prod case sensitive.

- X is the Flutter level flavor and Y is the native level flavor. MOCK Flutter flavor uses dev
  native flavor.

## How Firebase is setup

- The App is made in such a way that the Firebase will take the configuration for the corresponding
  flavor while running or building the app.
- In Android, the [Firebase Configuration files](lib/src/firebase) is enough for the Flutter app to
  connect to the Firebase server and it doesn't require any `google-service.json` file to connect to
  the Firebase server.
- In iOS, the `GoogleService-Info.plist` file is required and Firebase expect the same to be present
  in the root of the `ios` folder.
- Oh, we have 3 native flavors, How the running instance of the app will come to know
  which `GoogleService-Info.plist` file to pick?
    - Well, here we
      use [Build Phase](https://developer.apple.com/documentation/xcode/customizing-the-build-phases-of-a-target)
      scripts to get it done.
    - A shell script is run just before running the app and based on the scheme (or flavor) running,
      the script will copy the respective `GoogleService-Info.plist` file from
      the [Firebase Config directory](ios/Firebase) to the build folder of the iOS where Firebase
      expects the `GoogleService-Info.plist` file to be present.
    - The script can be seen and edited in the XCode by clicking on `Runner` target and going to
      Build Phase script. Refer below image for more.
      ![Copy GoogleService-Info.plist Script Screenshot](git_assets/copy_google_service_script.png)

## How Firebase Crashlytics is setup

- Firebase Crashlytics will work out of the box in Android.
- In iOS, it's bit tricky. Firebase Crashlytics needs dSYM files to convert unsymbolicated crash
  report to symbolicated crash report.
  Read [this](https://firebase.google.com/docs/crashlytics/get-deobfuscated-reports?platform=flutter)
  for why do we need this.
- The run script which uploads the dSYM file to the Crashlytics server can be found in
  the `Build Phase`. This script is responsible to find the corresponding `GoogleService-Info.plist`
  file for the current scheme and use it for uploading the dSYM file. Refer below image.
  ![Upload dSYM files to Crashlytics](git_assets/upload_dsym_to_crashlytics.png)

## Using Extensions

- Why are we using extensions?
    - For ease of writing the code. For example, instead of writing,
        ```dart
        Theme.of(context)
        ```
      we can write something like this,
        ```dart
        context.theme
        ```
      This is very much helpful and saves a good amount of time.
    - For more extensions, please have a look over [here](lib/src/utils/extensions.dart).

## Navigation

1. There are two types of navigator in the app viz Root Navigator and Nested Navigator. Root
   Navigator is across the whole app whereas Nested Navigator is used in a particular flow.
2. You can learn more about Nested navigator
   over [here](https://docs.flutter.dev/cookbook/effects/nested-nav).
3. We used nested navigator so that we can have more control over a flow's navigation without
   touching the root navigation of the app.
4. When to use Nested Navigator:
    - You use Nested Navigator everytime you wanted to start a new Flow. For example: Cash In, Cash
      Out, and Money Transfer, etc.
5. Adding a new screen route:

- [Routes](lib/src/core/route_generator.dart) class has all the routes which are currently in the
  application.
- Suppose you want to add a new screen `HomeScreen` in the route.
- Add a `static` id of the screen in the `HomeScreen` widget itself. Make sure it has "/" prefixed.
- Define that route in `Routes.generateRoute` method.
- Please follow this same procedure for the Nested Navigator as well.
- Navigate to that route using:

  ```dart 
  context.navigator.pushNamed(HomeScreen.id);
  ```

## Location Architecture

### How are we making sure that the agent is within the allowed limit(configurable) of their shop while performing any cash transactions or agent manager is within the allowed limit(configurable) of their shop while performing any agent approval?

- There is a stream running every 10 (configurable) seconds that gets the necessary location
  requirements and then tries to fetch the location and store it in a static variable, so that it
  can be accessed all over the app.
- The stream is triggered by `LocationBloc`. `LocationBloc` makes sure to handle this business
  logics of when the user disables the location service in between or maybe removes the location
  permission
  while using the app.

### How are we handling on the UI side when the user has disabled the location service or permission in between while using the app?

- We show a corresponding dialog when the user has disabled the service or permission in between.
- The dialog we are showing is a custom dialog that overrides traditional dialog's navigation
  behaviour and is actually a `Route` and comes under root `Navigator` of the app.

### Why are we using a custom dialog to show a location permission or service dialog?

- Traditional(or normal) dialog can be popped using `Navigator`'s `pop` method. Creating an edge
  case of it getting popped when it was not intended to from somewhere inside the app other
  than `LocationBloc`.
- We needed a proper control over dialog's navigation like if a `pop` method is called from
  somewhere in the app's code, and the location check cycle hits in showing the location dialog on
  the top of the navigation stack, the `pop` method should remove the `Route` that is underneath the
  location dialog and not the location dialog.
- This control over dialog's route is not offered by the Flutter out of the box. Hence this custom
  dialog.
- For more information on this custom dialog,
  See [this](lib/src/widgets/agency_location_error_dialog_route.dart).

## Splash Screen

1. We are using [flutter_native_splash](https://pub.dev/packages/flutter_native_splash) to generate
   the native side splash screen implementation.
2. The splash screen's preserving and removing from the Dart side is also handled by this package.
   Click [here](https://pub.dev/packages/flutter_native_splash#3-set-up-app-initialization-optional)
   to learn more.

## Uploading builds to Appstore/TestFlight

1. We only upload UAT and Prod builds to Appstore/Testflight.
2. Before uploading to the Appstore/TestFlight, make sure you have gone
   through [this](https://docs.flutter.dev/deployment/ios).
3. Before making a build, please make sure you run

```console
flutter pub clean && flutter pub get && flutter pub run build_runner build --delete-conflicting-outputs
```

command. <br>

4. Please also make sure you have cleaned the pod packages. You can run

```console
cd ios && pod deintegrate && pod install
```

command from the project root. <br>

5. After this, please run the app using XCode and test if the app works as expected. <br>
6. Please archive the build by going to `Product -> Archive` in the XCode. <br>
7. Known issues: <br>

- If you are on XCode 14.3, you might get this error while archiving the build:

  ```
  PhaseScriptExecution failed with a nonzero exit code
  ``` 

- Please update your Cocoapods version to fix this.
  Check [this](https://stackoverflow.com/a/75842910).

8. After archiving, you can see your build in `Window -> Organiser`. You can further upload this
   build to the Testflight.

## Some Tips:

1. While writing mock code, make sure you refer [mock_helpers](lib/src/helpers/mock_helpers.dart).
    - `OTPConstants` can help you get latest `OTPValidityDetails` and `ResendValidityDetails` based
      on the current time.
    - `getLeoRPCResult` can help you get response or error based on the `shouldThrowError` boolean.
      It will be helpful when testing the RPC in the mock environment.
    - For more information, Have a look at [mock_helpers](lib/src/helpers/mock_helpers.dart).
2. The UI code should never have Business Logic code. All the Business Logic code should go under
   the corresponding BLOC class.

## Decisions

1. We have used SVG pictures everywhere, as they are more optimized. But only for the google maps
   location marker in [map_preview_widget.dart](lib/src/features/maps/map_preview_widget.dart), we
   had to use png image. This had to be done, as the `google_maps_flutter` library doesn't
   support SVGs for the purpose.
2. For the dark theme of the Google Maps feature, we have used `Aubergine` map style, as provided in
   in the official [Google Maps styling](https://mapstyle.withgoogle.com/). Though the `Dark` style
   defined there suited better as per the design, but due to colors overlaying issue the bounds of
   buildings were not visible with that. To not waste more time on finding a workaround to be
   particular about using `Dark` style, we have opted to go for `Aubergine` styling here to meet
   the ultimate purpose of having a dark themed google maps feature.

## Language Support Update

As of now, we are discontinuing support for change language feature. Here are some important
details:

- The current foreign language strings within the app are google-translated versions of the English
  texts used in the app.
- In the future, we will be updating all foreign language strings with translations provided by the
  FDH team.
- To enable or disable change language feature support, you can use
  the[`supportChangeLanguageFeature`](https://github.com/Resolut-Tech/BCN-Agency-Banking-Flutter/blob/7bc1ed31d96d85fdf54d25599f7941aba1c63d0f/lib/src/utils/constants.dart#L123)
  flag.
- It's crucial that the server only send english locale information in the Home RPC to
  prevent potential app crashes.
- SignIn flow does not support any locale other than English.